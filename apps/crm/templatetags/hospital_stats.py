from django import template
from django.db.models import Count
from django.utils import timezone
from datetime import datetime, timedelta
from calendar import monthrange
from apps.genobenet.models.patientInfo import GBN_PatientInfo
from apps.genobenet.models.sampleInfo import GBN_SampleInfo
from apps.genomom.models.sample import SampleInfo

register = template.Library()

@register.simple_tag
def get_previous_month_stats(hospital_id):
    # Get first and last day of previous month
    today = timezone.now()
    if today.month == 1:
        first_day = datetime(today.year - 1, 12, 1)
    else:
        first_day = datetime(today.year, today.month - 1, 1)
    
    last_day = datetime(first_day.year, first_day.month, monthrange(first_day.year, first_day.month)[1])
    
    # Get NIPT samples from SampleInfo model
    nipt_samples = SampleInfo.objects.filter(
        patient__hospital_id=hospital_id,
        entry_date__gte=first_day,
        entry_date__lte=last_day,
        is_active=True, 
        report_publish=True,
    ).count()
    
    # Get GenoBenet samples
    genobenet_samples = GBN_SampleInfo.objects.filter(
        patient__hospital_id=hospital_id,
        entry_date__gte=first_day,
        entry_date__lte=last_day,
        patient__service_type__pdf_name='genobenet'
    ).count()
    
    # Get GenoFind samples
    genofind_samples = GBN_SampleInfo.objects.filter(
        patient__hospital_id=hospital_id,
        entry_date__gte=first_day,
        entry_date__lte=last_day,
        patient__service_type__pdf_name='genofind'
    ).count()
    
    # Count by service type
    service_counts = {
        'nipt': nipt_samples,
        'pgt': 0,  # To be implemented later
        'ora': 0,  # To be implemented later
        'genofind': genofind_samples,
        'genobenet': genobenet_samples,
    }
    
    return service_counts

@register.simple_tag
def get_doctor_service_stats(hospital_id):
    # Get first and last day of previous month
    today = timezone.now()
    if today.month == 1:
        first_day = datetime(today.year - 1, 12, 1)
    else:
        first_day = datetime(today.year, today.month - 1, 1)
    
    last_day = datetime(first_day.year, first_day.month, monthrange(first_day.year, first_day.month)[1])
    
    # Get NIPT doctor stats
    nipt_doctors = SampleInfo.objects.filter(
        patient__hospital_id=hospital_id,
        entry_date__gte=first_day,
        entry_date__lte=last_day,
        is_active=True,
        report_publish=True
    ).values(
        'patient__doctor__full_name',
        'patient__doctor__department'
    ).annotate(
        nipt_count=Count('id')
    )
    
    # Get GenoBenet/GenoFind doctor stats
    gbn_doctors = GBN_SampleInfo.objects.filter(
        patient__hospital_id=hospital_id,
        entry_date__gte=first_day,
        entry_date__lte=last_day
    ).values(
        'patient__doctor__full_name',
        'patient__doctor__department',
        'patient__service_type__pdf_name'
    ).annotate(
        service_count=Count('id')
    )
    
    # Combine and organize the data
    doctor_stats = {}
    
    # Process NIPT doctors
    for doc in nipt_doctors:
        dept = doc['patient__doctor__department'] or '기타'
        name = doc['patient__doctor__full_name']
        
        if dept not in doctor_stats:
            doctor_stats[dept] = {}
        
        if name not in doctor_stats[dept]:
            doctor_stats[dept][name] = {
                'name': name,
                'nipt_count': 0,
                'pgt_count': 0,
                'ora_count': 0,
                'genofind_count': 0,
                'genobenet_count': 0
            }
        
        doctor_stats[dept][name]['nipt_count'] = doc['nipt_count']
    
    # Process GenoBenet/GenoFind doctors
    for doc in gbn_doctors:
        dept = doc['patient__doctor__department'] or '기타'
        name = doc['patient__doctor__full_name']
        service = doc['patient__service_type__pdf_name']
        
        if dept not in doctor_stats:
            doctor_stats[dept] = {}
        
        if name not in doctor_stats[dept]:
            doctor_stats[dept][name] = {
                'name': name,
                'nipt_count': 0,
                'pgt_count': 0,
                'ora_count': 0,
                'genofind_count': 0,
                'genobenet_count': 0
            }
        
        if service == 'genofind':
            doctor_stats[dept][name]['genofind_count'] = doc['service_count']
        elif service == 'genobenet':
            doctor_stats[dept][name]['genobenet_count'] = doc['service_count']
    
    # Convert to list format for template
    result = {}
    for dept, doctors in doctor_stats.items():
        result[dept] = list(doctors.values())
    
    return result

@register.simple_tag
def get_doctor_categories(hospital_id):
    # Get all doctors for the hospital from both models
    doctors = []
    
    # Get doctors from NIPT samples
    nipt_doctors = SampleInfo.objects.filter(
        patient__hospital_id=hospital_id
    ).values('patient__doctor__full_name', 'patient__doctor__department').annotate(
        patient_count=Count('id')
    )
    
    # Get doctors from GenoBenet/GenoFind samples
    gbn_doctors = GBN_PatientInfo.objects.filter(
        hospital_id=hospital_id
    ).values('doctor__full_name', 'doctor__department').annotate(
        patient_count=Count('id')
    )
    
    # Combine both querysets
    doctors = list(nipt_doctors) + list(gbn_doctors)
    
    # Group by department
    categories = {}
    for doc in doctors:
        dept = doc.get('patient__doctor__department') or doc.get('doctor__department') or '기타'
        name = doc.get('patient__doctor__full_name') or doc.get('doctor__full_name')
        count = doc.get('patient_count', 0)
        
        if dept not in categories:
            categories[dept] = []
        categories[dept].append({
            'name': name,
            'count': count
        })
    
    return categories

@register.simple_tag
def get_doctor_total_stats(hospital_id):
    # Get NIPT doctor stats
    nipt_doctors = SampleInfo.objects.filter(
        patient__hospital_id=hospital_id,
        is_active=True,
        report_publish=True
    ).values(
        'patient__doctor__full_name'
    ).annotate(
        nipt_count=Count('id')
    )
    
    # Get GenoBenet/GenoFind doctor stats
    gbn_doctors = GBN_SampleInfo.objects.filter(
        patient__hospital_id=hospital_id
    ).values(
        'patient__doctor__full_name',
        'patient__service_type__pdf_name'
    ).annotate(
        service_count=Count('id')
    )
    
    # Combine and organize the data
    doctor_stats = {}
    
    # Process NIPT doctors
    for doc in nipt_doctors:
        name = doc['patient__doctor__full_name']
        
        if name not in doctor_stats:
            doctor_stats[name] = {
                'name': name,
                'nipt_count': 0,
                'pgt_count': 0,
                'ora_count': 0,
                'genofind_count': 0,
                'genobenet_count': 0
            }
        
        doctor_stats[name]['nipt_count'] = doc['nipt_count']
    
    # Process GenoBenet/GenoFind doctors
    for doc in gbn_doctors:
        name = doc['patient__doctor__full_name']
        service = doc['patient__service_type__pdf_name']
        
        if name not in doctor_stats:
            doctor_stats[name] = {
                'name': name,
                'nipt_count': 0,
                'pgt_count': 0,
                'ora_count': 0,
                'genofind_count': 0,
                'genobenet_count': 0
            }
        
        if service == 'genofind':
            doctor_stats[name]['genofind_count'] = doc['service_count']
        elif service == 'genobenet':
            doctor_stats[name]['genobenet_count'] = doc['service_count']
    
    # Convert to list and sort by total count
    result = []
    for doctor in doctor_stats.values():
        total = (doctor['nipt_count'] + doctor['genofind_count'] + 
                doctor['genobenet_count'] + doctor['pgt_count'] + 
                doctor['ora_count'])
        doctor['total'] = total
        result.append(doctor)
    
    # Sort by total count in descending order
    result.sort(key=lambda x: x['total'], reverse=True)
    
    return result 