from django.db import models
from django.utils import timezone
from apps.hospital.models    import <PERSON><PERSON>   , HospitalInfo
from apps.authentication.models import Employee

# Create your models here.


class CS_Inquiry(models.Model):
    """
    CS Inquiry - Customer Service Inquiry Tracking
    """
    
    CS_CLASS = (
        ("low", "낮음 모두 공유"), #
        ("medium", "중간 영업 팀 "), # Marketin all
        ("high", "높음 대표님+부서장 "), # selected 경정 마무리 단계 
        #("critical", "긴급 (대표님 + 부서장 )"), #! ONlY CEO and 
    )

        
    # Choices
    DEPART = [      
        ('cs', "CS팀"),                 #! Customer Service Team
        ("lab", "검사본부"),            # Stored as "lab", Displayed as "검사본부"
        ("marketing", "마케팅팀"),      # Stored as "marketing", Displayed as "마케팅팀"
        ("rnd", "연구개발팀"),          # Stored as "rnd", Displayed as "연구개발팀"
        ("ora", "ORA 팀"),            # Stored as "ora", Displayed as "ORA 팀"        
        ("it", "IT팀"),                # Stored as "it", Displayed as "IT팀"
        ("hr", "인사팀"),              # Stored as "hr", Displayed as "인사팀"
        ("finance", "재무팀"),         # Stored as "finance", Displayed as "재무팀"
        ("other", "기타"),            # Stored as "other", Displayed as "기타"
        ("ceo", "CEO"),               # Stored as "ceo", Displayed as "CEO"
    ]
    
    SERVICE = (
        ("nipt", "제노맘 (NIPT)"),
        ("pgs", "지노브로 (PGS)"),
        ("genobenet", "GenoBenet"),
        ("gfn", "GenoFind"),
        ("era", "ERA"),
        ("ora", "ORA"),
        ("other", "기타"),
    )
    
    
    CATEGORY1 = (        
       ("gender", "성별"),
        ("test", "검사"),
        ("result", "결과"),
        ("sales", "영업"),
        ("incoming", "입고"),
        ("specimen", "검체"),
        ("others", "기타"),
        ("sales_supplies", "영업 소모품"),
        ("academic", "학술"),
        ("service", "서비스"),
        ("other", "기타"), 
    ) 
    
    

    
    PRIORITY = (
        ("low", "낮음"),
        ("medium", "중간"),
        ("high", "높음"),
    )
    INQUIRY_SOURCE = (
            
            ("mobile", "CS_모바일"),
            ("phone", "전화"),            
            ("website", "웹사이트"),
            ("email", "이메일"),
            ("visit", "방문"),
            ("seminar", "세미나"),
            ("other", "기타"),
            
            )
    
    PRIORITY = (
        ("low", "낮음"),
        ("medium", "중간"),
        ("high", "높음"),
    )
    
    STATUS = (
        ("open", "미해결"),
        ("in_progress", "진행 중"),
        ("resolved", "해결됨"),
    )
    
    
    
    category1 = models.CharField(
        max_length=50,
        verbose_name="문의 구분 1",
        choices=CATEGORY1,
        null=True,
        blank=True,
        default="result",
        help_text="문의의 주요 분류 선택",
        )
     
    

    #! IF Internal Department is required like to forward
    related_department = models.CharField(
        max_length=20,
        verbose_name="내부 관련 부서",
        choices=DEPART,
        default="cs",
        blank=True,
        null=True,
        help_text="문의를 처리할 내부 부서 선택",
        )
    
    
    #! Which Source? Phone, Email, Seminar, Website, Visit, Other
    inquiry_source = models.CharField(
        max_length=20,
        verbose_name="문의 출처",
        choices=INQUIRY_SOURCE,
        null=True,
        blank=True,
        default="mobile",
        help_text="문의가 접수된 출처 (예: 이메일, 전화, 세미나 등)",
    )
    
    

    service_type = models.CharField(
        max_length=20,
        verbose_name="서비스 유형",
        choices=SERVICE,
        default="nipt",
        help_text="문의와 관련된 서비스 (기본값: 제노맘 NIPT)",
    )
    
    cs_class = models.CharField(
        max_length=20,
        verbose_name="CS 보안 등급",
        choices=CS_CLASS,
        default="low",
        help_text="CS 분류 (기본값: 낮음)",
    )

    # Fields Jedan

    
    jedan = models.ForeignKey(
        Jedan,  # or Organization if it's already defined above
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="재단 발신 기관",
        help_text="문의자가 소속된 재단 기관 (예: SML)",
        related_name="jedan_inquiries"
    )
    
    
    #! Now due to old datas There will be error 
    
    jedan_branch = models.CharField(
        max_length=100,
        verbose_name="지점 발신 기관",
        null=True,
        blank=True,
        help_text="문의 지점명 (예: 강남지점)",
    )
    
    
    #! Hospital Name later also add Foreign Key    
    hospital = models.ForeignKey(
        HospitalInfo,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        verbose_name="병원명 발신 기관",
        help_text="문의와 관련된 병원 이름 (예: 서울병원)",
        related_name="hospital_inquiries"
    )
    
    other_org = models.CharField(
        max_length=200,
        verbose_name="기타 발신 기관 ",
        null=True,
        blank=True,
        help_text="문의 등록 기관 ( 재단 병원 제외 ) )",
    )
    
    
    inquiry_person = models.CharField(
        max_length=100,
        verbose_name="발신자 이름",
        help_text="문의자 성명 (예: 김산모)",
    )
    
    
    inquiry_phone = models.CharField(
        max_length=200,
        verbose_name="문의자 연락처",
        null=True,
        blank=True,
        help_text="문의자의 전화번호 (예: 010-1234-5678)",
    )
    
    
    inquiry_email = models.EmailField(
                    verbose_name="문의자 이메일",
                    null=True,
                    blank=True,
                    help_text="문의자의 이메일 주소 (예: <EMAIL>)"
                )

    
    
    
    sample_id = models.CharField(
        max_length=100,
        verbose_name="검체 접수번호",
        blank=True,
        null=True,
        help_text="검체의 고유 접수 번호 (예: AGC2411N14561 (김*현)  )",
    )
    
    hospital_chart = models.CharField(
        max_length=100,
        verbose_name="차트번호",
        blank=True,
        null=True,
        help_text="검체의 고유 병원/재단 차트번호   )",
    )
     
    
    priority = models.CharField(
        max_length=20,
        verbose_name="우선순위",
        choices=PRIORITY,
        default="medium",
        help_text="문의 처리 우선순위 (기본값: 중간)",
    )
    
    
    inquiry_question = models.CharField(
        max_length=255,
        verbose_name="문의 내용",
        help_text="고객의 문의 사항 요약 (최대 255자)",
    )
    
    inquiry_date = models.DateField(
        verbose_name="문의 날짜",
        help_text="문의 날짜 (예: 2024-01-01)",
        null=True,
        blank=True,
        default=timezone.now,
    )
    
    
    
    
    # author = models.CharField(
    #     max_length=250,
    #     verbose_name="작성자",
    #     default="-",
    #     blank=True,
    #     help_text="문의 기록을 작성한 사람 (기본값: 크리스나 )",
    # )
    
    recorded_by = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='cs_questions',
        verbose_name='담당자'
    )
    
    
    
    # Suggested Additional Fields
    status = models.CharField(
        max_length=20,
        verbose_name="상태",
        choices=STATUS,
        default="resolved",
        help_text="문의 처리 상태 (기본값: 미해결)",
    )
    
    
    resolution_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="해결일",
        help_text="문의가 해결된 날짜와 시간",
        default=timezone.now,
    )
    
    
    internal_notes = models.CharField(
        null=True,
        blank=True,
        verbose_name="내부 메모",
        help_text="고객과 공유되지 않는 내부용 메모",
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="생성일",
        help_text="문의 기록이 생성된 날짜와 시간",
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name="수정일",
        help_text="문의 기록이 마지막으로 수정된 날짜와 시간",
    )
    
    

    def __str__(self):
        return self.inquiry_question if self.inquiry_question else "문의 내용 없음"
    
    def inquiry_org_name(self):
        return " ".join(str(x) for x in [
            getattr(self.hospital, 'hospital_name',
                    None) if self.hospital else None,
            getattr(self.jedan, 'jedan_name', None) if self.jedan else None,
            self.jedan_branch,
            self.other_org
        ] if x)

    def inquiry_org_name_kakao(self):
        return " ".join(str(x) for x in [
            
            getattr(self.jedan, 'jedan_name', None) if self.jedan else None,
            self.jedan_branch,
            self.other_org
        ] if x)
