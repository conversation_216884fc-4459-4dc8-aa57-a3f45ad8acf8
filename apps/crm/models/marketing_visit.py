from django.db import models
import os
from django.utils.timezone import now
from apps.authentication.models import Employee
from django.core.exceptions import ValidationError
from apps.hospital.models import HospitalInfo
from django.utils import timezone

def get_upload_path(instance, filename):
    return os.path.join(
        'marketing_visits',
        instance.visit_date.strftime("%Y-%m-%d"),
        filename
    )

class MarketingVisit(models.Model):
    VISIT_TYPE_CHOICES = [
        ('HOSPITAL', '병원'),
        ('SEMINAR', '세미나'),
        ('CONFERENCE', '학회'),
        ('OTHER', '기타'),
    ]

    marketing_manager = models.ForeignKey(
        Employee, 
        on_delete=models.CASCADE, 
        related_name='marketing_visits',
        verbose_name='담당자'
    )
    
    visit_date = models.DateField(
        verbose_name='방문일자',
        help_text='방문한 날짜를 선택해주세요',
        default=now
    )
    visit_type = models.CharField(
        max_length=20, 
        choices=VISIT_TYPE_CHOICES,
        verbose_name='방문유형',
        help_text='방문 유형을 선택해주세요',
        default="HOSPITAL"
    )
    location_name = models.ForeignKey(
        HospitalInfo,
        on_delete=models.PROTECT,
        verbose_name='방문장소',
        related_name='marketing_visits',
        help_text='방문한 장소를 선택해주세요'
    )
    person_met = models.CharField(
        max_length=250,
        verbose_name='면담자',
        help_text='만난 사람을 입력해주세요,여러 명 경우 쉼표로,구분'
    )
    files_provided = models.CharField(
        max_length=250,
        verbose_name='자료제공',
        help_text='제공한 자료를 입력해주세요',
        blank=True,
        null=True,
        default="-"
    )
    main_topic = models.CharField(
        max_length=200,
        verbose_name='주요 논의 사항',
        help_text='방문의 주요 논의 사항을 간단히 작성해주세요',
    )
    discussion_details = models.TextField(
        verbose_name='상담내용',
        help_text='상담 내용을 상세히 기록해주세요'
    )
    attachment = models.FileField(
        upload_to=get_upload_path,
        verbose_name='첨부파일',
        help_text='관련 문서나 사진을 첨부해주세요 (선택사항)',
        blank=True,
        null=True
    )
    attachment_description = models.CharField(
        max_length=255,
        verbose_name='첨부파일 설명',
        help_text='첨부파일에 대한 간단한 설명을 입력해주세요',
        blank=True,
        default="-"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='작성일시')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='수정일시')
    
    # New field for edit history
    edit_history = models.JSONField(default=list, blank=True, verbose_name='수정 기록')

    class Meta:
        ordering = ['-visit_date']
        verbose_name = '마케팅 방문'
        verbose_name_plural = '마케팅 방문 목록'

    def __str__(self):
        return f"{self.marketing_manager.full_name} - {self.location_name.hospital_name} - {self.visit_date}"

    def save(self, *args, **kwargs):
        user = kwargs.pop('user', None)

        if self.pk and user:
            edit_entry = {
                'editor_name': user.get_full_name() or user.username,                
                'edited_at': timezone.now().strftime('%Y/%m/%d %H:%M'),  # pre-formatted!
            }

            if not self.edit_history:
                self.edit_history = []

            self.edit_history.append(edit_entry)
            self.edit_history = self.edit_history[-10:]

        super().save(*args, **kwargs)
        
        
    
    

    def get_last_editor(self):
        """Get the last person who edited this record"""
        if self.edit_history and len(self.edit_history) > 0:
            last_edit = self.edit_history[-1]
            return {
                'name': last_edit.get('editor_name', 'Unknown'),
                'date': last_edit.get('edited_at')
            }
        return None

    def get_edit_history(self):
        """Get formatted edit history"""
        return self.edit_history

    def clean(self):
        pass

    def delete(self, *args, **kwargs):
        if self.attachment:
            if os.path.isfile(self.attachment.path):
                os.remove(self.attachment.path)
        super().delete(*args, **kwargs)

    def get_file_name(self):
        if self.attachment:
            return os.path.basename(self.attachment.name)
        return None 
