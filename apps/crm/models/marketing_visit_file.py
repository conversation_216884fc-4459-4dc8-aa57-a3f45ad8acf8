from django.db import models
import os
from django.utils.timezone import now
from apps.crm.models.marketing_visit import MarketingVisit

def get_file_upload_path(instance, filename):
    """
    Generate a unique path for uploaded files based on marketing visit date and ID
    """
    visit = instance.marketing_visit
    return os.path.join(
        'marketing_visits',
        str(visit.id),
        'files',
        filename
    )

class MarketingVisitFile(models.Model):
    """
    Model for storing multiple files related to a marketing visit
    """
    marketing_visit = models.ForeignKey(
        MarketingVisit, 
        on_delete=models.CASCADE,
        related_name='files',
        verbose_name='마케팅 방문'
    )
    
    file = models.FileField(
        upload_to=get_file_upload_path,
        verbose_name='파일',
        help_text='관련 문서나 사진을 첨부해주세요'
    )
    
    description = models.CharField(
        max_length=255,
        verbose_name='파일 설명',
        help_text='파일에 대한 간단한 설명을 입력해주세요',
        blank=True,
        default="-"
    )
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='업로드 일시')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='수정 일시')
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = '마케팅 방문 파일'
        verbose_name_plural = '마케팅 방문 파일 목록'
    
    def __str__(self):
        return f"File for {self.marketing_visit} - {self.description}"
    
    def get_file_name(self):
        """Get just the filename without the path"""
        if self.file:
            return os.path.basename(self.file.name)
        return None
    
    def delete(self, *args, **kwargs):
        """Delete the file from storage when the model instance is deleted"""
        if self.file:
            if os.path.isfile(self.file.path):
                os.remove(self.file.path)
        super().delete(*args, **kwargs)
