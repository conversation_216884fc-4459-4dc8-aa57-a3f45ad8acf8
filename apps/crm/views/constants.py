"""
상수 정의 모듈
"""
import re
import time
from bs4 import BeautifulSoup as bs
from django.utils.timezone import localtime
from apps.authentication.models import Employee
def shorten(text, max_length=14):
    if isinstance(text, str) and len(text) > max_length:
        return text[:max_length - 1] + '…'
    return text

def format_datetime(dt):
    """날짜/시간을 YYYY-MM-DD HH:MM:SS 형식으로 포맷팅"""
    if dt is None:
        return ""
    # 밀리초와 시간대 정보 제외하고 시분초까지만 표시
    return dt.strftime("%Y-%m-%d %H:%M:%S")

def extract_cs_info(inquiry):
    def get_value(value):
        return '-' if value is None or value == '' else value
    
    time.sleep(1)
    
    # 전체 inquiry 필드 확인
    # print("\n[DEBUG] inquiry.__dict__:")
    # for k, v in inquiry.__dict__.items():
    #     print(f"{k}: {v}")

    # print("\n[DEBUG] inquiry.dir():")
    # for attr in dir(inquiry):
    #     if not attr.startswith('_'):
    #         try:
    #             print(f"{attr}: {getattr(inquiry, attr)}")
    #         except Exception as e:
    #             print(f"{attr}: <Error: {e}>")
    
    # 응답 객체 찾기
    response_obj = inquiry.responses.first()  # 첫 번째 응답 객체 가져오기
    try:
        print(f"response_obj_answer: {response_obj.answer}")
        soup = bs(response_obj.answer, 'html.parser')
        print(f"soup_text: {soup.get_text()}")
    except:
        print(f"answer/html_answer 속성이 없습니다.")

    
    # HTML 텍스트가 있는 경우도 처리하도록 수정
    try:
        response_text = response_obj.answer
    except:
        response_text = '자세한 사항은 링크를 클릭해주세요.'
    
    cs_infos = {
        "category1": inquiry.get_category1_display(),  # 문의 구분
        "related_department": inquiry.get_related_department_display(),  # 내부 관련 부서
        "inquiry_source": inquiry.get_inquiry_source_display(),  # 문의 출처
        "service_type": inquiry.get_service_type_display(),  # 서비스 유형
        "cs_class": inquiry.get_cs_class_display(),  # CS 보안 등급
        "inquiry_org": get_value(inquiry.other_org),  # 문의 기관

        "hospital_chart": get_value(inquiry.hospital_chart),
        "branch": get_value(inquiry.inquiry_org_name_kakao()),  # 지점

        "inquiry_person": get_value(inquiry.inquiry_person),  # 문의자 이름
        "inquiry_phone": get_value(inquiry.inquiry_phone),  # 문의자 전화번호
        "inquiry_email": get_value(inquiry.inquiry_email),  # 문의자 이메일
        # 병원명
        "hospital_name": get_value(inquiry.hospital.hospital_name if inquiry.hospital else '-'),
        "sample_id": get_value(inquiry.sample_id),  # 검체번호
        "priority": inquiry.get_priority_display(),  # 우선순위
        "inquiry_question": get_value(inquiry.inquiry_question),  # 문의 내용 
        "inquiry_date": format_datetime(inquiry.inquiry_date) if inquiry.inquiry_date else '-',  # 문의 날짜
        "status": inquiry.get_status_display(),  # 상태
        "resolution_date": format_datetime(inquiry.resolution_date) if inquiry.resolution_date else '-',  # 해결 날짜
        "internal_notes": get_value(inquiry.internal_notes),  # 내부 메모
        "inquiry_answer": response_text,  # 응답 객체 자체를 전달 (notification_templates.py에서 처리)
        "inquiry_id": inquiry.id  # 문의 ID
    }
    return cs_infos

def extract_cs_comment(inquiry):
    """
    Extract CS comment information into a dictionary
    """
    
    time.sleep(1)
    
    cs_comment_infos = {
        "inquiry_id": inquiry.id,
        "inquiry_author": inquiry.recorded_by.full_name,
        "comment_author": inquiry.responses.last().answered_by.full_name,
        "comment_content": inquiry.responses.last().answer
    }
    
    return cs_comment_infos

def extract_marketing_info(marketing_visit):
    """
    Extract marketing visit information into a dictionary
    """
    def get_value(value):
        if value is None or value == '':
            return '-'
        return str(value)  # 모든 값을 문자열로 변환
    
    marketing_infos = {
        "marketing_id": get_value(marketing_visit.id),  # 방문 아이디
        # 위치
        "location_name": get_value(marketing_visit.location_name.hospital_name).replace(" ", ""),
        "full_name": get_value(marketing_visit.marketing_manager.full_name),  # 담당자
        "visit_date": get_value(marketing_visit.visit_date),  # 방문일자
        "person_met": get_value(marketing_visit.person_met),  # 면담자
        "main_topic": get_value(marketing_visit.main_topic),  # 주요 논의 사항
        "discussion_details": get_value(marketing_visit.discussion_details),  # 상담내용
    }
    
    return marketing_infos

def extract_marketing_comment(marketing_visit_comment):
    """
    Extract marketing visit comment information into a dictionary
    """
    def get_value(value):
        if value is None or value == '':
            return '-'
        return str(value)  # 모든 값을 문자열로 변환
    
    time.sleep(1)
    
    local_time = localtime(marketing_visit_comment.created_at)
    formatted_time = local_time.strftime('%Y-%m-%d %H:%M:%S')
    
    marketing_infos = {
        "marketing_id": get_value(marketing_visit_comment.marketing_visit.id),  # 방문 아이디
        "main_topic": get_value(marketing_visit_comment.marketing_visit.main_topic),  # 주요 논의 사항
        "full_name": get_value(marketing_visit_comment.marketing_visit.marketing_manager.full_name),  # 원글 담당자
        "comment_author": get_value(marketing_visit_comment.author.full_name),  # 댓글 작성자
        "comment_content": get_value(marketing_visit_comment.content),  # 댓글 내용
        "comment_date": formatted_time,  # 댓글 작성일시
    }

    return marketing_infos

def add_it_employee_contacts(employee_contacts):
    """
    Add test team employee contacts to the employee contacts dictionary
    """
    # (테스트용) IT팀 추가
    test_employees = Employee.objects.filter(team__in=['it'])
    for emp in test_employees:
        if hasattr(emp, 'mobile_nb') and emp.mobile_nb:
            employee_contacts[emp.full_name] = emp.mobile_nb
    return employee_contacts