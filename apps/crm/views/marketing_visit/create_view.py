from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import CreateView
from django.urls import reverse_lazy
from django.shortcuts import redirect
from django.contrib import messages
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import login_required
from apps.authentication.decorators import staff_required
from apps.crm.models import MarketingVisit
from apps.authentication.models import Employee
from apps.crm.forms.marketing_visit_form import MarketingVisitForm
from apps.common.notifications import send_notifications_async
from apps.crm.views.constants import add_it_employee_contacts





@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(staff_required(allowed_user_groups=["2", "7"]), name='dispatch')
class MarketingVisitCreateView(LoginRequiredMixin, CreateView):
    model = MarketingVisit
    form_class = MarketingVisitForm
    template_name = 'crm/marketing_visit/form.html'
    success_url = reverse_lazy('marketing_visit_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        context['active_menu'] = 'crm'
        
        context['form'].fields['location_name'].widget.attrs.update({
            'data-ajax-url': reverse_lazy('location_search'),
            'data-create-url': reverse_lazy('location_create_popup'),
            'data-minimum-input-length': '1'
        })
        return context

    def form_valid(self, form):
        form.instance.marketing_manager = self.request.user.user_employee 
        response = super().form_valid(form)
        
        # 템플릿 코드 입력 kakao template code is Fix 
        template_code = 'gc_marketing_info'
       
        # 알림 전송 대상 직원 목록
        employee_contacts = {}
        
        # # 원글 작성자
        # original_by = form.instance.marketing_visit.marketing_manager
        # employee_contacts[original_by.full_name] = original_by.mobile_nb
        
        # CEO 추가
        ceo = Employee.objects.get(position='대표이사')
        employee_contacts[ceo.full_name] = ceo.mobile_nb

        # (테스트용) IT팀 추가
        employee_contacts = add_it_employee_contacts(employee_contacts)

        print("직원 연락처 정보:", employee_contacts)
        #! to stop kakao alarm sending comment this 
        send_notifications_async(template_code, form.instance, employee_contacts)
        #print("kakao Commented " * 100 )

        if 'save_and_new' in self.request.POST:
            messages.success(self.request, 'Visit record created successfully! You can add another.')
            return redirect('marketing_visit_create')

        messages.success(self.request, 'Visit record created successfully!')
        return response

    def form_invalid(self, form):
        messages.error(self.request, 'Please correct the errors below.')
        return super().form_invalid(form)