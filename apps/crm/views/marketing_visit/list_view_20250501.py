from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView
from apps.crm.mixins import CRMContextMixin,  allowed_users_only
from apps.crm.models import MarketingVisit
from apps.crm.views.marketing_visit.filters import MarketingVisitFilter
from django.utils.decorators import method_decorator



class MarketingVisitListView(CRMContextMixin, LoginRequiredMixin, ListView):
    model = MarketingVisit
    template_name = 'crm/marketing_visit/list.html'
    context_object_name = 'visits'
    paginate_by = 100
    crm_segment = 'marketing-visit'
    
    def get_queryset(self):
        queryset = super().get_queryset().order_by("-created_at", '-id')
        user = self.request.user

        if not hasattr(user, 'user_employee'):
            return queryset.none()

        employee = user.user_employee
        department = employee.team.lower()
        

        if user.is_superuser and  department in ['it', 'ceo']:
            filtered_queryset = queryset
        elif department in ['marketing']:
            filtered_queryset = queryset.filter(
                marketing_manager__team='marketing'
            )
            #print("Hello " * 100)
        else:
            #print(department  * 100)
            filtered_queryset = queryset.filter(marketing_manager=employee)
            

        self.filterset = MarketingVisitFilter(self.request.GET, queryset=filtered_queryset)
        return self.filterset.qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter'] = self.filterset
        context['active_menu'] = 'crm'
        return context
