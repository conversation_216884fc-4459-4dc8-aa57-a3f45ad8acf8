
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView
from django.db.models import Q
from apps.crm.mixins import CRMContextMixin, allowed_users_only
from apps.crm.models import MarketingVisit
from apps.crm.views.marketing_visit_comment import MarketingVisitCommentForm
from django.contrib.auth.mixins import LoginRequiredMixin
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import login_required


# Define role-based user groups
CEO = ['hglim', ]  # 대표님
IT = ['krishdb38', 'hch7627']  # 크리스나 , 황창회
MARKET = ["rlarjsgml4", "tjsals68"]  # "현선민", "김건희"
SALES = ['yang7', 'mjkim', 'sd5300']  # 양진석, 백인종, 김민주


# Concatenate all groups into ALLOWED_USERS
ALLOWED_USERS = CEO + SALES + IT


@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(allowed_users_only(ALLOWED_USERS), name='dispatch')
class MarketingVisitListDetailView(CRMContextMixin, LoginRequiredMixin, ListView):
    """
    영업 김민주 요청 한 한 페이지 에 모두 정보 보여주는 페이지
    crm/marketing_visit
    """
    model = MarketingVisit
    template_name = 'crm/marketing_visit/list_detail.html'
    context_object_name = 'visits'
    paginate_by = 20
    crm_segment = 'marketing-visit'

    def get_queryset(self):
        queryset = super().get_queryset().order_by('-visit_date')
        search_query = self.request.GET.get('search', '')
        date_from = self.request.GET.get('date_from', '')
        date_to = self.request.GET.get('date_to', '')

        if search_query:
            queryset = queryset.filter(
                Q(location_name__hospital_name__icontains=search_query) |
                Q(person_met__icontains=search_query) |
                Q(main_topic__icontains=search_query) |
                Q(marketing_manager__full_name__icontains=search_query)
            )

        if date_from:
            queryset = queryset.filter(visit_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(visit_date__lte=date_to)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Always pass these
        context['search_query'] = self.request.GET.get('search', '')
        context['date_from'] = self.request.GET.get('date_from', '')
        context['date_to'] = self.request.GET.get('date_to', '')
        context['active_menu'] = 'crm'
    
    
        
        # Get the selected visit if any
        selected_visit_id = self.request.GET.get('selected_visit')
        if selected_visit_id:
            selected_visit_id = int(selected_visit_id.strip("/"))
            try:
                selected_visit = MarketingVisit.objects.get(id=selected_visit_id)
                context['selected_visit'] = selected_visit
                
                # Get comments for the selected visit
                context['comments'] = selected_visit.comments.filter(parent_comment=None).order_by('-created_at')
                
                # Add comment form
                context['comment_form'] = MarketingVisitCommentForm()
                
                # Get hospital info
                context['hospital'] = selected_visit.location_name
                
    
    
                
                # Get all visits to the same hospital (excluding current visit)
                context['all_visits_in_same_place'] = MarketingVisit.objects.filter(
                    location_name=selected_visit.location_name
                ).exclude(id=selected_visit.id).order_by('-visit_date')
                
            except MarketingVisit.DoesNotExist:
                pass
        
        return context