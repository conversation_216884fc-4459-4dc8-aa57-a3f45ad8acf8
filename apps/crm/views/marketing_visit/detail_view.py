from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import DetailView
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import login_required
from apps.authentication.decorators import staff_required
from apps.crm.mixins import CRMContextMixin
from apps.crm.models import MarketingVisit, MarketingVisitFile
from apps.crm.views.marketing_visit_comment import MarketingVisitCommentForm
from apps.crm.forms.marketing_visit_file_form import MarketingVisitFileForm




@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(staff_required(allowed_user_groups=[ "7","8","9"]), name='dispatch')
class MarketingVisitDetailView(CRMContextMixin, LoginRequiredMixin, DetailView):
    """
    7 영업 , 8 학술 , 9 영업 일일 보고
    simple detail view not ajax

    """
    model = MarketingVisit
    template_name = 'crm/marketing_visit/detail.html'
    context_object_name = 'selected_visit'
    crm_segment = 'marketing-visit'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['comments'] = self.object.comments.filter(parent_comment=None).order_by('-created_at')
        context['comment_form'] = MarketingVisitCommentForm()
        context['active_menu'] = 'crm'

        visit = self.get_object()  # Get the current MarketingVisit object

        # Add files related to this marketing visit
        context['files'] = self.object.files.all().order_by('-created_at')

        # Add file upload form
        context['file_form'] = MarketingVisitFileForm()

        # Retrieve the related hospital
        hospital = visit.location_name

        # Get all visits that happened at the same hospital (excluding the current one)
        all_visits_in_same_place = MarketingVisit.objects.filter(
            location_name=hospital
        ).exclude(id=visit.id)

        # Add hospital and visit list to context
        context['hospital'] = hospital
        context['all_visits_in_same_place'] = all_visits_in_same_place
        return context