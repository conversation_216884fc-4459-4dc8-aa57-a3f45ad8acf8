from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.views.generic import UpdateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.shortcuts import redirect
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import login_required
from apps.authentication.decorators import staff_required
from apps.crm.mixins import CRMContextMixin
from apps.crm.models import MarketingVisit, MarketingVisitFile
from apps.crm.forms.marketing_visit_form import MarketingVisitForm
from apps.crm.forms.marketing_visit_file_form import MarketingVisitFileForm

@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(staff_required(allowed_user_groups=["2", "7"]), name='dispatch')
class MarketingVisitUpdateView(CRMContextMixin, LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    model = MarketingVisit
    form_class = MarketingVisitForm
    template_name = 'crm/marketing_visit/form.html'
    success_url = reverse_lazy('marketing_visit_list')
    crm_segment = 'marketing-visit'

    def test_func(self):
        """
        Check if the user has permission to edit this visit record
        Returns True if user is the original author
        """
        obj = self.get_object()
        return obj.marketing_manager == self.request.user.user_employee

    def handle_no_permission(self):
        """
        Redirect to a custom 'no permission' page with an error message.
        """
        messages.error(
            self.request,
            '수정 권한이 없습니다. 작성자만 이 방문 기록을 수정할 수 있습니다.'
        )
        # change this to your actual named URL
        return redirect('no_permission_page')

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        form.fields['location_name'].widget.attrs['readonly'] = True
        form.fields['location_name'].widget.attrs['disabled'] = True
        return form

    def form_valid(self, form):
        marketing_visit = form.save(commit=False)

        # Ensure the marketing_manager remains the same (original author)
        # We don't want to change the author even if a different user somehow edits it

        marketing_visit.save(user=self.request.user)  # pass user here for edit history

        messages.success(self.request, 'Visit record updated successfully!')
        return redirect(self.success_url)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add edit history to context
        context['edit_history'] = self.object.get_edit_history()

        # Add permission context for template
        context['can_edit'] = self.test_func()

        # Add files related to this marketing visit
        context['files'] = self.object.files.all().order_by('-created_at')

        # Add file upload form
        context['file_form'] = MarketingVisitFileForm()

        context['active_menu'] = 'crm'

        return context