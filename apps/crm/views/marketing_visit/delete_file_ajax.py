from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from apps.authentication.decorators import staff_required
from apps.crm.models import MarketingVisitFile

@login_required(login_url="/")
@staff_required(allowed_user_groups=["2", "7"])
@require_POST
def delete_file_ajax(request, file_id):
    """
    AJAX view for deleting a file from a marketing visit
    """
    # Get the file or return 404
    file_obj = get_object_or_404(MarketingVisitFile, id=file_id)
    
    # Check if the user has permission to delete this file
    marketing_visit = file_obj.marketing_visit
    if not (request.user.is_superuser or marketing_visit.marketing_manager == request.user.user_employee):
        return JsonResponse({
            'status': 'error',
            'message': '이 파일을 삭제할 권한이 없습니다.'
        }, status=403)
    
    # Delete the file
    file_obj.delete()
    
    # Return success response
    return JsonResponse({
        'status': 'success',
        'message': '파일이 성공적으로 삭제되었습니다.'
    })
