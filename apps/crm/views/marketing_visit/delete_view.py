from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import login_required
from apps.authentication.decorators import staff_required
from apps.crm.mixins import CRMContextMixin
from apps.crm.models import MarketingVisit
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect

@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(staff_required(allowed_user_groups=["2", "7"]), name='dispatch')
class MarketingVisitDeleteView(CRMContextMixin, LoginRequiredMixin, DeleteView):
    model = MarketingVisit
    template_name = 'crm/marketing_visit/confirm_delete.html'
    success_url = reverse_lazy('marketing_visit_list')
    crm_segment = 'marketing-visit'
    
    def dispatch(self, request, *args, **kwargs):
        obj = self.get_object()
        written_by = obj.marketing_manager  # the person who wrote the post

        # Allow only if user is superuser and the one who wrote the post
        #! Generally block to delete post 
        if request.user.is_superuser and written_by == request.user.user_employee:
            return super().dispatch(request, *args, **kwargs)
        else:
            # raise PermissionDenied("You are not allowed to delete this record.")
            # If not allowed, redirect with an error message
            messages.error(request, "삭제 권한이 없습니다. 작성자이자 관리자만 삭제할 수 있습니다.")
            return redirect('no_permission_page')  # Replace with your actual named URL
    
    

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Visit record deleted successfully!')
        return super().delete(request, *args, **kwargs)