from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView
from apps.crm.mixins import CRMContextMixin
from apps.crm.models import MarketingVisit
from apps.crm.views.marketing_visit.filters import MarketingVisitFilter
from django.utils.decorators import method_decorator
from apps.authentication.decorators import staff_required


@method_decorator(staff_required(allowed_user_groups=["7", "8" , "9"]), name='dispatch')
class MarketingVisitListView(CRMContextMixin, LoginRequiredMixin, ListView):
    model = MarketingVisit
    template_name = 'crm/marketing_visit/list.html'
    context_object_name = 'visits'
    paginate_by = 100
    crm_segment = 'marketing-visit'
    # or use reverse_lazy('login')
    login_url = ''
    
    def get_queryset(self):
        # Start with all records
        queryset = MarketingVisit.objects.all()
        
        user = self.request.user

        # Apply user permissions filtering
        if not hasattr(user, 'user_employee'):
            if user.is_superuser:
                filtered_queryset = queryset
            else:
                return queryset.none()
        else:
            employee = user.user_employee
            
            if hasattr(employee, 'team'):
                department = employee.team.lower()
                
                if user.is_superuser or department in ['it', 'ceo']:
                    filtered_queryset = queryset
                elif department in ['marketing']:
                    filtered_queryset = queryset.filter(
                        marketing_manager__team='marketing'
                    )
                else:
                    filtered_queryset = queryset.filter(marketing_manager=employee)
            else:
                if user.is_superuser:
                    filtered_queryset = queryset
                else:
                    return queryset.none()
        
        # Apply filters
        self.filterset = MarketingVisitFilter(self.request.GET, queryset=filtered_queryset)
        filtered_qs = self.filterset.qs
        
        # Get sort parameter from request
        sort_by = self.request.GET.get('sort', '-created_at')
        
        # Define valid sort fields
        valid_sort_fields = ['id', 'visit_date', 'created_at', 'location_name__hospital_name', 'person_met']
        
        # Check if the sort field is valid
        sort_field = sort_by.replace('-', '')
        if sort_field not in valid_sort_fields:
            sort_by = '-created_at'
        
        # Apply sorting with a secondary sort by ID to ensure consistent ordering
        if sort_by:
            # If sorting by a field that might have duplicates, add ID as secondary sort
            if sort_by.startswith('-'):
                filtered_qs = filtered_qs.order_by(sort_by, '-id')
            else:
                filtered_qs = filtered_qs.order_by(sort_by, 'id')
        else:
            # Default sorting
            filtered_qs = filtered_qs.order_by('-created_at', '-id')
        
        return filtered_qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter'] = self.filterset
        context['active_menu'] = 'crm'
        
        # Add sorting context
        sort_by = self.request.GET.get('sort', '-created_at')
        context['sort_by'] = sort_by
        
        return context
