from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from apps.authentication.decorators import staff_required
from apps.crm.models import MarketingVisit, MarketingVisitFile
from apps.crm.forms.marketing_visit_file_form import MarketingVisitFileForm
import os

@login_required(login_url="/")
@staff_required(allowed_user_groups=["2", "7"])
@require_POST
def add_file_ajax(request, visit_id):
    """
    AJAX view for adding a file to a marketing visit
    """
    # Get the marketing visit or return 404
    marketing_visit = get_object_or_404(MarketingVisit, id=visit_id)
    
    # Check if the user has permission to add files to this visit
    if not (request.user.is_superuser or marketing_visit.marketing_manager == request.user.user_employee):
        return JsonResponse({
            'status': 'error',
            'message': '이 방문 기록에 파일을 추가할 권한이 없습니다.'
        }, status=403)
    
    # Process the form
    form = MarketingVisitFileForm(request.POST, request.FILES)
    
    if form.is_valid():
        # Create the file but don't save it yet
        file_obj = form.save(commit=False)
        file_obj.marketing_visit = marketing_visit
        file_obj.save()
        
        # Return success response with file details
        return JsonResponse({
            'status': 'success',
            'message': '파일이 성공적으로 업로드되었습니다.',
            'file': {
                'id': file_obj.id,
                'name': os.path.basename(file_obj.file.name),
                'description': file_obj.description,
                'created_at': file_obj.created_at.strftime('%Y-%m-%d %H:%M')
            }
        })
    else:
        # Return error response with form errors
        return JsonResponse({
            'status': 'error',
            'message': '파일 업로드 중 오류가 발생했습니다.',
            'errors': form.errors
        }, status=400)
