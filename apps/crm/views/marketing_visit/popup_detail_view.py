from django.views.generic import DetailView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse
from apps.crm.models import MarketingVisit
from django.template.loader import render_to_string
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import login_required

from apps.crm.mixins import CRMContextMixin, allowed_users_only


# Define role-based user groups
CEO = ['hglim', ] # 대표님 
IT = ['krishdb38', 'hch7627'] # 크리스나 , 황창회 
MARKET = ["rlarjsgml4", "tjsals68"]  # "현선민", "김건희"
SALES = ['yang7', 'mjkim', 'sd5300'] # 양진석, 백인종, 김민주


# Concatenate all groups into ALLOWED_USERS
ALLOWED_USERS = MARKET + SALES + CEO + IT



@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(allowed_users_only(ALLOWED_USERS), name='dispatch')
class MarketingVisitPopupDetailView(LoginRequiredMixin, DetailView):
    """
    모바일 전용 상세 페이지
    pop up page specially for marketing team 학술 팀 
    
    """
    model = MarketingVisit
    context_object_name = 'selected_visit'
    template_name = 'crm/marketing_visit/popup_detail.html'  # default for AJAX

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        context = self.get_context_data(object=self.object)
        
        context['comments'] = self.object.comments.all().order_by('-created_at')

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return HttpResponse(render_to_string(self.template_name, context, request))

        # Set full-page template dynamically
        self.template_name = 'crm/marketing_visit/visit_detail_mobile.html'
        return self.render_to_response(context)
