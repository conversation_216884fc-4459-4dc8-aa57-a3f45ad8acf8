from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Value
from django.db.models.functions import Replace, Lower
from django.views.generic import View, CreateView
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.http import JsonResponse
from django.urls import reverse_lazy
from apps.authentication.decorators import staff_required
from apps.hospital.models import HospitalInfo


@method_decorator(require_http_methods(["GET"]), name='dispatch')
class LocationSearchView(View):
    def get(self, request):
        query = request.GET.get('q', '').strip()
        normalized_query = ''.join(
            query.split()).lower()  # Remove all whitespace

        if normalized_query:
            # Use REGEXP_REPLACE to handle multiple types of whitespace
            locations = HospitalInfo.objects.annotate(
                cleaned_name=Lower(
                    Replace(
                        Replace('hospital_name', Value(' '), Value('')),
                        # Handle non-breaking spaces
                        Value('\u00A0'), Value('')
                    )
                )
            ).filter(cleaned_name__icontains=normalized_query)[:10]

            # Alternative approach using raw SQL for more control
            """
            locations = HospitalInfo.objects.raw(
                'SELECT id, hospital_name FROM your_table_name WHERE '
                'LOWER(REGEXP_REPLACE(hospital_name, \'\\s+\', \'\')) LIKE %s LIMIT 10',
                [f'%{normalized_query}%']
            )
            """

            results = [{'id': loc.id, 'text': loc.hospital_name}
                       for loc in locations]
        else:
            results = []

        return JsonResponse({'results': results})



@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(staff_required(allowed_user_groups=["2", "7"]), name='dispatch')
class LocationCreatePopupView(LoginRequiredMixin, CreateView):
    model = HospitalInfo
    template_name = 'crm/marketing_visit/location_popup_form.html'
    fields = ["jedan", "jedan_branch", 'hospital_name', 'phone_number', "do_address", 'si_address', "last_address"]
    success_url = reverse_lazy('marketing_visit_list')

    def form_valid(self, form):
        # Set the added_by field before saving
        form.instance.added_by = self.request.user.user_employee
        
        response = super().form_valid(form)
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'id': self.object.id,
                'text': self.object.hospital_name,
                'status': 'success'
            })
        return response

    def form_invalid(self, form):
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'status': 'error',
                'errors': form.errors
            }, status=400)
        return super().form_invalid(form)