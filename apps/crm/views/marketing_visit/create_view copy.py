from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import CreateView
from django.urls import reverse_lazy
from django.shortcuts import redirect
from django.contrib import messages
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import login_required
from apps.authentication.decorators import staff_required
from apps.crm.models import MarketingVisit
from apps.authentication.models import Employee
from apps.crm.forms.marketing_visit_form import MarketingVisitForm
from apps.crm.views.constants import extract_marketing_info
from apps.common.notifications import send_notifications_async
from apps.common.notification_templates import prepare_marketing_info_template
@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(staff_required(allowed_user_groups=["2", "7"]), name='dispatch')
class MarketingVisitCreateView(LoginRequiredMixin, CreateView):
    model = MarketingVisit
    form_class = MarketingVisitForm
    template_name = 'crm/marketing_visit/form.html'
    success_url = reverse_lazy('marketing_visit_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        context['active_menu'] = 'crm'
        
        context['form'].fields['location_name'].widget.attrs.update({
            'data-ajax-url': reverse_lazy('location_search'),
            'data-create-url': reverse_lazy('location_create_popup'),
            'data-minimum-input-length': '1'
        })
        return context

    def form_valid(self, form):
        form.instance.marketing_manager = self.request.user.user_employee 
        response = super().form_valid(form)
        
        # 템플릿 코드 입력 kakao template code is Fix 
        template_code = 'gc_marketing_info'
       
        employee_contacts = {}
        written_by = form.instance.marketing_manager
        ceo = Employee.objects.get(position='대표이사')   #! Later need to change to CEO 


        # (테스트용) IT팀 추가
        target_employees = Employee.objects.filter(team__in=['it'])
        employee_contacts = {
            emp.full_name: emp.mobile_nb 
            for emp in target_employees 
            if hasattr(emp, 'mobile_nb') and emp.mobile_nb
        }
                
        employee_contacts[written_by.full_name] = written_by.mobile_nb
        employee_contacts[ceo.full_name] = ceo.mobile_nb

        print("직원 연락처 정보:", employee_contacts)
        
        # 방문 정보 가져오기
        # marketing_info = extract_marketing_info(form.instance)
        # template_parameters, wehago_msg = prepare_marketing_info_template(marketing_info)
        
        send_notifications_async(template_code, form.instance, employee_contacts)
        
        # 여기에 marketing_info를 활용하는 코드 추가
        # 예: 이메일 전송, 데이터 처리 등
        
        # 보낸사람, 대표님
        

        if 'save_and_new' in self.request.POST:
            messages.success(self.request, 'Visit record created successfully! You can add another.')
            return redirect('marketing_visit_create')

        messages.success(self.request, 'Visit record created successfully!')
        return response

    def form_invalid(self, form):
        messages.error(self.request, 'Please correct the errors below.')
        return super().form_invalid(form)