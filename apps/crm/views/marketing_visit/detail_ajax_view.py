# views.py
from django.shortcuts import get_object_or_404
from django.template.loader import render_to_string
from django.http import JsonResponse
from django.views import View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import DetailView
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import login_required
from apps.authentication.decorators import staff_required

from apps.crm.models import MarketingVisit
from apps.crm.views.marketing_visit_comment import MarketingVisitCommentForm
from apps.crm.mixins import allowed_users_only


# 7 영업 , 8 학술 , 9 영업 일일 보고 

ALLOWED_USERS = ['hglim', "mjkim",  "yang7", "sd5300", 'krishdb38', 'hch7627']  # 'krishdb38',

@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(allowed_users_only(ALLOWED_USERS), name='dispatch')
@method_decorator(staff_required(allowed_user_groups=[ "9"]), name='dispatch')
class MarketingVisitDetailAjaxView(View):
    """
    to view this permission user Employee table need to update , and if new user then user id need to pass
    
    """
    
    
    
    
    def get(self, request, *args, **kwargs):
        visit_id = request.GET.get('visit_id')
        visit = get_object_or_404(MarketingVisit, id=visit_id)

        all_visits_in_same_place = MarketingVisit.objects.filter(
            location_name=visit.location_name
        ).exclude(id=visit.id).order_by('-visit_date')

        # Middle column
        middle_html = render_to_string(
            'crm/marketing_visit/detail_middle_col.html',
            {'selected_visit': visit},
            request=request
        )

        # Right column
        right_html = render_to_string(
            'crm/marketing_visit/detail_last_col.html',
            {
                'selected_visit': visit,
                'comments': visit.comments.filter(parent_comment=None).order_by('-created_at'),
                'comment_form': MarketingVisitCommentForm(),
                'hospital': visit.location_name,
                'all_visits_in_same_place': all_visits_in_same_place
            },
            request=request
        )

        # Past visits
        past_html = render_to_string(
            'crm/marketing_visit/hospital_visit_list_past.html',
            {
                'all_visits_in_same_place': all_visits_in_same_place,
                'selected_visit': visit,
                'hospital': visit.location_name,  # assuming location_name is a Hospital object
            },
            request=request
        )
        

        return JsonResponse({
            'middle_html': middle_html,
            'right_html': right_html,
            'past_html': past_html,
        })



