from django import forms
from django.db.models import Q
from django_filters import Filter<PERSON>et, <PERSON><PERSON><PERSON><PERSON><PERSON>, Date<PERSON><PERSON><PERSON>, <PERSON><PERSON>anFilter, ModelChoiceFilter
from apps.crm.models import MarketingVisit
from apps.authentication.models import Employee
import django_filters


class MarketingVisitFilter(FilterSet):
    search = CharFilter(
        method='filter_search', 
        label='검색어', 
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': '검색어 입력'})
    )
    
    date_range = DateFilter(
        field_name='visit_date', 
        lookup_expr='gte', 
        label='방문일자 이후', 
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    
    end_date = DateFilter(
        field_name='visit_date', 
        lookup_expr='lte', 
        label='방문일자 이전', 
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    
    marketing_manager = django_filters.ModelChoiceFilter(
        queryset=Employee.objects.filter(
            Q(team='marketing') | Q(team='sales')),
        label='담당자',
        required=False,
        widget=forms.Select(attrs={'class': 'form-control form-select'})
    )
    
    has_attachment = BooleanFilter(
        method='filter_has_attachment', 
        label='첨부파일 있음', 
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    
    def filter_search(self, queryset, name, value):
        if not value:
            return queryset
        return queryset.filter(
            Q(location_name__hospital_name__icontains=value) |
            Q(person_met__icontains=value) |
            Q(main_topic__icontains=value)
        )
    
    def filter_has_attachment(self, queryset, name, value):
        if value:
            return queryset.exclude(attachment='')
        return queryset
    
    class Meta:
        model = MarketingVisit
        fields = ['search', 'date_range', 'end_date', 'marketing_manager', 'has_attachment']
