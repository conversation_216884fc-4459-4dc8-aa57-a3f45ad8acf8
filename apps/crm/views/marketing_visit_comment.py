from django.shortcuts import get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.views.generic import CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.http import JsonResponse
from django.template.loader import render_to_string

from django import forms
from apps.authentication.models import Employee
from apps.crm.models import MarketingVisit, MarketingVisitComment
from apps.crm.views.constants import add_it_employee_contacts
from apps.common.notifications import send_notifications_async


# Form for the comment
class MarketingVisitCommentForm(forms.ModelForm):
    class Meta:
        model = MarketingVisitComment
        fields = ['content']
        widgets = {
            'content': forms.Textarea(attrs={'rows': 3, 'placeholder': '댓글을 입력하세요...', 'class': 'form-control'}),
        }


class CommentEditMixin(UserPassesTestMixin):
    def test_func(self):
        comment = self.get_object()
        return self.request.user.user_employee == comment.author


# Class-based view for adding comments (if you prefer this style)
class CommentCreateView(LoginRequiredMixin, CreateView):
    """
    AJAX-based comment creation view for marketing visit comments
    """
    model = MarketingVisitComment
    form_class = MarketingVisitCommentForm
    template_name = 'crm/marketing_visit_comment_form.html'

    def form_valid(self, form):
        form.instance.author = self.request.user.user_employee
        form.instance.marketing_visit_id = self.kwargs['visit_id']
        if self.kwargs.get('parent_id'):
            form.instance.parent_comment_id = self.kwargs['parent_id']
        self.object = form.save()
        
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            context = {
                'comment': self.object,
                'request': self.request
            }
            html = render_to_string(
                'crm/marketing_visit/comment_content.html',
                context,
                request=self.request
            )
            
            # 템플릿 코드 입력 kakao template code is Fix 
            template_code = 'gc_marketing_comment'

            # 알림 전송 대상 직원 목록
            employee_contacts = {}
            
            # # 작성자 본인            
            # written_by = form.instance.author
            # employee_contacts[written_by.full_name] = written_by.mobile_nb
            
            # 원글 작성자
            original_by = form.instance.marketing_visit.marketing_manager
            employee_contacts[original_by.full_name] = original_by.mobile_nb
            
            # CEO 추가
            ceo = Employee.objects.get(position='대표이사')   #! Later need to change to CEO 
            employee_contacts[ceo.full_name] = ceo.mobile_nb

            # 테스트용 IT팀 추가
            employee_contacts = add_it_employee_contacts(employee_contacts)
            
            
            send_notifications_async(template_code, form.instance, employee_contacts)
            
            
            return JsonResponse({
                'status': 'success',
                'html': html
            })
        return super().form_valid(form)

    def form_invalid(self, form):
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'status': 'error',
                'errors': form.errors
            })
        return super().form_invalid(form)

    def get_success_url(self):
        return reverse_lazy('marketing_visit_detail', kwargs={'pk': self.kwargs['visit_id']})



class CommentUpdateView(LoginRequiredMixin, CommentEditMixin, UpdateView):
    model = MarketingVisitComment
    fields = ['content']
    template_name = 'crm/marketing_visit/comment_edit_form.html'

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        content = request.POST.get('content')
        
        if not content:
            return JsonResponse({
                'status': 'error',
                'message': '댓글 내용을 입력해주세요.'
            }, status=400)

        self.object.content = content
        self.object.save()

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            context = {'comment': self.object}
            html = render_to_string(
                'crm/marketing_visit/comment_content.html',
                context,
                request=request
            )
            
            # message 보내기
            # print(self.object.author) # 작성자 
            # print(self.object.marketing_visit_id) # 방문 아이디
            # print(self.object.content) # 댓글 내용
            # print(self.object.created_at) # 작성일시
            
            return JsonResponse({
                'status': 'success',
                'html': html
            })
            
        return redirect(self.get_success_url())

    def get_success_url(self):
        return reverse_lazy('marketing_visit_detail', kwargs={'pk': self.object.marketing_visit_id})

class CommentDeleteView(LoginRequiredMixin, CommentEditMixin, DeleteView):
    """
    AJAX-based comment delete view
    """
    model = MarketingVisitComment
    template_name = 'crm/marketing_visit/comment_confirm_delete.html'

    def post(self, request, *args, **kwargs):
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            try:
                self.object = self.get_object()
                if not self.test_func():
                    return JsonResponse({
                        'status': 'error',
                        'message': '삭제 권한이 없습니다.'
                    }, status=403)
                
                self.object.delete()
                return JsonResponse({
                    'status': 'success',
                    'message': '댓글이 삭제되었습니다.'
                })
            except Exception as e:
                return JsonResponse({
                    'status': 'error',
                    'message': str(e)
                }, status=400)
        return super().post(request, *args, **kwargs)

    def get_success_url(self):
        return reverse_lazy('marketing_visit_detail', kwargs={'pk': self.object.marketing_visit_id})