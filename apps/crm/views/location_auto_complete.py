from django.http import JsonResponse
from django.views import View
from django.views.generic.edit import CreateView
from django.views.generic.detail import DetailView
from django.template.loader import render_to_string
from django.forms import ModelForm

from apps.hospital.models import HospitalInfo
# from apps.crm.models.location_person import Location
from django.db.models.functions import Lower, Replace
from django.db.models import F, Value

from django.utils.decorators import method_decorator

from django.contrib.auth.decorators import login_required
from apps.authentication.decorators import staff_required

# Autocomplete API

@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(staff_required(allowed_user_groups=["2", "7"]), name='dispatch')
class LocationAutocomplete(View):
    """
    Location Autocomplete API
    This is used to autocomplete the location name in the marketing visit form
    if user type hospital name, it will show the hospital name and address
    """

    def get(self, request):
        
        q = request.GET.get('q', '')
        normalized_query = q.replace(' ', '').replace('\u00A0', '').lower()

        if normalized_query:
            locations = HospitalInfo.objects.annotate(
                cleaned_name=Lower(
                    Replace(
                        Replace(
                            F('hospital_name'),
                            Value(' '),
                            Value('')
                        ),
                        Value('\u00A0'),
                        Value('')
                    )
                )
            ).filter(cleaned_name__icontains=normalized_query).values('id', 'hospital_name', 'si_address')[:10]

            results = [
                {
                    'id': loc['id'],
                    'text': f"{loc['hospital_name']} --> [{loc['si_address']}]" if loc['si_address'] else loc['hospital_name']
                }
                for loc in locations
            ]
        else:
            results = []

        return JsonResponse({'results': results})




# Location creation modal form
class LocationForm_del(ModelForm):
    class Meta:
        model = HospitalInfo
        fields = ['hospital_name' ]  # adjust to your model







@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(staff_required(allowed_user_groups=["2", "7"]), name='dispatch')
class LocationCreateView_del(CreateView):
    model = HospitalInfo
    form_class = LocationForm_del
    template_name = 'crm/location_form_modal.html'

    def form_valid(self, form):
        self.object = form.save()
        return JsonResponse({
            'status': 'success',
            'id': self.object.id,
            'text': self.object.name,
        })

    def form_invalid(self, form):
        return JsonResponse({
            'status': 'error',
            'html': render_to_string(self.template_name, {'form': form}, request=self.request),
        })


# Optional: Detail view to show location info
@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(staff_required(allowed_user_groups=["2", "7"]), name='dispatch')
class LocationDetailView(DetailView):
    model = HospitalInfo
    template_name = 'crm/location_detail.html'
