import traceback
from django.utils import timezone
from django.shortcuts import render, redirect
from django.contrib import messages
from django.db import transaction
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views.generic import CreateView
from django.urls import reverse_lazy

from apps.authentication.decorators import staff_required
from apps.crm.models import CS_Inquiry
from apps.crm.forms import CS_InquiryForm, get_response_formset
from apps.hospital.models import HospitalInfo
from apps.authentication.models import Employee
from apps.common.notifications import send_notifications_async


@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(staff_required(allowed_user_groups=["2", "7"]), name='dispatch')
class CS_Inquiry_CreateView(CreateView):
    model = CS_Inquiry
    form_class = CS_InquiryForm
    template_name = 'crm/create_inquiry.html'
    success_url = reverse_lazy('cs_inquiry_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        CS_ResponseFormSet = get_response_formset(extra=1, can_delete=False)

        if self.request.POST:
            context['formset'] = CS_ResponseFormSet(
                self.request.POST, self.request.FILES)
        else:
            context['formset'] = CS_ResponseFormSet()

        # Add individual response forms for template logic (optional)
        if context['formset'].forms:
            context['response_form'] = context['formset'].forms[0]
            if len(context['formset'].forms) > 1:
                context['response_form_2'] = context['formset'].forms[1]
            if len(context['formset'].forms) > 2:
                context['response_form_3'] = context['formset'].forms[2]

        # Preload hospital if editing
        if hasattr(self, 'object') and self.object and self.object.hospital:
            context['initial_hospital'] = {
                'id': self.object.hospital.id,
                'name': self.object.hospital.name
            }

        context['title'] = 'Create New CS Inquiry'
        context['active_menu'] = 'crm'
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']
        

        if form.is_valid() and formset.is_valid():
            try:
                with transaction.atomic():
                    inquiry = form.save(commit=False)
                    
                    inquiry.recorded_by = self.request.user.user_employee

                    hospital_id = self.request.POST.get('hospital_name')
                    # print(hospital_id)
                    
                    if hospital_id:
                        # print("Hospital ID inside view :", hospital_id)
                        inquiry.hospital = HospitalInfo.objects.filter(
                            id=hospital_id).first()

                    inquiry.save()

                    # inquiry 객체의 모든 필드와 값 출력
                    # print("\n=== CS Inquiry 정보 ===")
                    # print(f"ID: {inquiry.id}")
                    # for field in inquiry._meta.fields:
                    #     field_value = getattr(inquiry, field.name)
                    #     print(f"{field.name}: {field_value}")
                    # print("=====================\n")

                    responses = formset.save(commit=False)
                    for response in responses:
                        if response.answer and response.answer.strip():
                            response.cs_inquiry = inquiry
                            response.answered_by = self.request.user.user_employee
                            response.save()

                    # Send notifications if needed
                    template_code = 'gc_cs_info'

                    #! Do NOT COMMIT/PUSH THIS CODE WITHOUT DE-ANNOTATION
                    target_employee = (
                        Employee.objects.filter(team__in=['cs', 'sales','marketing', 'it']) |
                        Employee.objects.filter(position__in=['대표이사', '상무이사', '이사']) |
                        Employee.objects.filter(user_id=self.request.user.id)
                    ).filter(user__is_active=True).distinct()
                    
                    
                    #! DO NOT COMMIT/PUSH THIS CODE WITHOUT ANNOTATION
                    # target_employee = (
                    #     Employee.objects.filter(team__in=['it']) |
                    #     Employee.objects.filter(user_id=self.request.user.id)
                    # ).filter(user__is_active=True).distinct()
                    
                    # full_name과 mobile_nb를 키-값 쌍으로 하는 딕셔너리 생성
                    employee_contacts = {
                        emp.full_name: emp.mobile_nb 
                        for emp in target_employee 
                        if hasattr(emp, 'mobile_nb') and emp.mobile_nb
                    }
                    
                    print("직원 연락처 정보:", employee_contacts)

                    #! * Uncomment to enable alerts
                    send_notifications_async(template_code, inquiry, employee_contacts)

                    messages.success(
                        self.request, '문의가 성공적으로 등록되었으며, 알림이 전송되었습니다.')
                    return super().form_valid(form)

            except Exception as e:
                traceback.print_exc()
                form.add_error(None, f'문의 등록 중 오류가 발생했습니다: {str(e)}')
                return self.form_invalid(form)

        return self.form_invalid(form)

    def form_invalid(self, form):
        context = self.get_context_data(form=form)

        # Ensure formset is rebuilt
        CS_ResponseFormSet = get_response_formset(extra=1, can_delete=False)
        context['formset'] = CS_ResponseFormSet(
            self.request.POST, self.request.FILES)

        formset = context['formset']

        # Print to console for debugging
        print("❌ Form errors:")
        print(form.errors)
        print("❌ Form non-field errors:")
        print(form.non_field_errors())

        print("❌ Formset non-form errors:")
        print(formset.non_form_errors())
        for idx, f in enumerate(formset.forms):
            print(f"Formset form {idx} errors: {f.errors}")

        # Add a generic visible message
        messages.error(
            self.request, '입력하신 내용에 오류가 있습니다. 필수 항목을 확인해주세요.', extra_tags='danger')

        return self.render_to_response(context)
