from django.views.decorators.http import require_POST
from django.contrib.auth.decorators import login_required
from django.shortcuts import redirect
from django.contrib import messages
from apps.authentication.decorators import staff_required
from django.shortcuts import get_object_or_404
from apps.crm.models import CS_Inquiry
from apps.crm.forms.cs_inquiry_response_form import CS_ResponseForm
from apps.authentication.models import Employee
from apps.common.notifications import send_notifications_async
from apps.crm.views.constants import add_it_employee_contacts

@require_POST
@login_required
@staff_required(allowed_user_groups=["2", "7"])
def submit_response(request, pk):
    """
    in cs detail vieew page  /crm/cs_inquiry/1250/
    this is using 
    
    
    """
    
    
    inquiry = get_object_or_404(CS_Inquiry, pk=pk)
    form = CS_ResponseForm(request.POST)

    if form.is_valid():
        print("**** " * 100 )
        response = form.save(commit=False)
        response.cs_inquiry = inquiry
        response.answered_by = request.user.user_employee
        # response.author = request.user.username
        response.save()
        
        # Send notifications if needed
        template_code = 'gc_cs_comment'
        
        #! Do NOT COMMIT/PUSH THIS CODE WITHOUT DE-ANNOTATION
        target_employee = (
            Employee.objects.filter(team__in=['cs', 'sales','marketing', 'it']) |
            Employee.objects.filter(position__in=['대표이사', '상무이사', '이사']) |
            Employee.objects.filter(user_id=request.user.id)
        ).filter(user__is_active=True).distinct()

        employee_contacts = {
            emp.full_name: emp.mobile_nb 
            for emp in target_employee 
            if hasattr(emp, 'mobile_nb') and emp.mobile_nb
            }
        # full_name과 mobile_nb를 키-값 쌍으로 하는 딕셔너리 생성

        print("직원 연락처 정보:", employee_contacts)

        # * Uncomment to enable alerts
        send_notifications_async(template_code, inquiry, employee_contacts)

        
        messages.success(request, "응답이 등록되었습니다.")
    else:
        messages.error(request, "응답 내용을 확인해주세요.")

    return redirect('cs_inquiry_detail', pk=pk)
