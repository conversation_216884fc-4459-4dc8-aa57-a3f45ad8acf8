from django.views.generic import DeleteView
from apps.crm.models.cs_inquiry import CS_Inquiry
from django.urls import reverse_lazy
from django.http import HttpResponseForbidden
from django.contrib import messages
from django.shortcuts import redirect

class CSInquiryDeleteView(DeleteView):
    model = CS_Inquiry
    template_name = 'crm/cs_inquiry_confirm_delete.html'
    success_url = reverse_lazy('cs_inquiry_list') 
    
    def dispatch(self, request, *args, **kwargs):
        # Get the object before calling super()
        obj = self.get_object()

        # Check if the user is either the one who recorded the inquiry or a superuser
        #! for now blocking to delete and later will give only one person to delete 
        if request.user.is_superuser and obj.recorded_by == request.user.user_employee:
            return super().dispatch(request, *args, **kwargs)
        else:
            #return HttpResponseForbidden("<h1 style='color:red;'> You are not allowed to delete this entry. </h1>")
            
            # If not allowed, redirect with an error message
            messages.error(request, "삭제 권한이 없습니다. ")  # 작성자이자 관리자만 삭제할 수 있습니다.
            return redirect('no_permission_page')  # Replace with your actual named URL
