from django.views.generic import ListView
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.db import models
from django.http import JsonResponse

from django.utils.timezone import localtime

from apps.authentication.decorators import staff_required
from apps.crm.models import CS_Inquiry

@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(staff_required(allowed_user_groups=[ "7","8","9"]), name='dispatch')
class CS_Inquiry_ListView(ListView):
    model = CS_Inquiry
    template_name = 'crm/cs_inquiry_list.html'
    context_object_name = "cs_inquiry_list"

    def get_queryset(self):
        # Print the total count of inquiries for debugging
        total_count = CS_Inquiry.objects.count()
        

        queryset = CS_Inquiry.objects.select_related('hospital', 'jedan').all()

        # Enhanced Search
        search_value = self.request.GET.get('search[value]', '')
        if search_value:
            queryset = queryset.filter(
                models.Q(other_org__icontains=search_value) |
                models.Q(inquiry_person__icontains=search_value) |
                models.Q(jedan_branch__icontains=search_value) |
                models.Q(inquiry_question__icontains=search_value) |
                models.Q(sample_id__icontains=search_value) |
                models.Q(inquiry_phone__icontains=search_value)  |
                models.Q(hospital__hospital_name__icontains=search_value) |
                models.Q(jedan__jedan_name__icontains=search_value) |
                models.Q(recorded_by__full_name__icontains=search_value) |
                models.Q(service_type__icontains=search_value)
            )

        # Status Filter
        status_filter = self.request.GET.get('status', '')
        if status_filter:

            queryset = queryset.filter(status=status_filter)

        # Service Type Filter
        service_filter = self.request.GET.get('service_type', '')
        if service_filter:
            queryset = queryset.filter(service_type=service_filter)

        # Date Range Filter
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__lte=date_to)

        # Column Ordering
        order_column_index = self.request.GET.get('order[0][column]', '0')  # Default to ID column (0)
        order_direction = self.request.GET.get('order[0][dir]', 'desc')     # Default to descending

        # Print ordering parameters for debugging
        #print(f"Ordering by column index: {order_column_index}, direction: {order_direction}")

        # Updated column mapping to match your model
        column_mapping = {
            '0': 'id',
            '1': 'category1',
            '2': 'service_type',
            '3': 'hospital__hospital_name',  # Hospital column
            '4': 'other_org',  # Jedan column
            '5': 'related_department',
            '6': 'sample_id',
            '7': 'other_org',
            '8': 'status',
            '9': 'inquiry_person',
            '10': 'inquiry_phone',
            '11': 'inquiry_question',
            '12': 'inquiry_source',
            '13': 'id',  # Actions column maps to ID
            '14': 'author',
            '15': 'created_at'
        }

        # If ordering by ID column, use created_at instead for more reliable ordering
        if order_column_index == '0':
            order_column = 'created_at'  # Use created_at instead of id for more reliable ordering
        else:
            order_column = column_mapping.get(order_column_index, 'created_at')  # Default to created_at if column not found

        # Always apply ordering
        if order_direction == 'desc':
            order_column = f'-{order_column}'

        #print(f"Ordering by database column: {order_column}")
        queryset = queryset.order_by(order_column)

        # Pagination
        start = int(self.request.GET.get('start', 0))
        length = int(self.request.GET.get('length', 10))

        return queryset[start:start + length], queryset.count()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_menu'] = 'crm'
        
        # Add choices for filters
        context.update({
            'status_choices': CS_Inquiry.STATUS,
            'service_choices': CS_Inquiry.SERVICE,
            'category_choices': CS_Inquiry.CATEGORY1,
            'department_choices': CS_Inquiry.DEPART,
            'source_choices': CS_Inquiry.INQUIRY_SOURCE,
            
        })
        return context

    def get(self, request, *args, **kwargs):
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            try:
                queryset, total_filtered = self.get_queryset()
                total = CS_Inquiry.objects.count()


                data = [{
                    'id': f"{item.id}",
                    'category1': dict(CS_Inquiry.CATEGORY1).get(item.category1, ''),
                    'service_type': dict(CS_Inquiry.SERVICE).get(item.service_type, ''),
                    'inquiry_source': dict(CS_Inquiry.INQUIRY_SOURCE).get(item.inquiry_source, ''),
                    'related_department': dict(CS_Inquiry.DEPART).get(item.related_department, ''),
                    'inquiry_org': item.inquiry_org_name_kakao(),
                    'sample_id': item.sample_id or '',
                    'inquiry_person': item.inquiry_person or '',
                    'inquiry_phone': item.inquiry_phone or '',
                    'inquiry_question': item.inquiry_question or '',
                    'author': item.recorded_by.full_name  or '',
                    #'created_at': item.created_at.strftime('%Y-%m-%d %H:%M') if item.created_at else '',
                    'created_at': localtime(item.created_at).strftime('%Y-%m-%d %H:%M') if item.created_at else '',

                    'status': item.status,
                    'sample_id': f"{item.hospital_chart or ''}/{item.sample_id or ''}" ,
                    'hospital': item.hospital.hospital_name if item.hospital else '-',  # Add hospital name
                    'jedan': item.jedan.jedan_name if item.jedan else '',  # Add jedan name
                } for item in queryset]

                return JsonResponse({
                    'draw': int(request.GET.get('draw', 1)),
                    'recordsTotal': total,
                    'recordsFiltered': total_filtered,
                    'data': data
                })
            except Exception as e:
                print(e)
                return JsonResponse({
                    'error': str(e)
                }, status=500)

        return super().get(request, *args, **kwargs)



