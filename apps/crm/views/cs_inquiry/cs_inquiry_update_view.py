# views.py

import json
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.db import transaction
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import UpdateView
from django.urls import reverse_lazy
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.template.loader import render_to_string

from apps.authentication.decorators import staff_required
from apps.crm.models import CS_Inquiry
from apps.crm.forms import CS_InquiryForm, get_response_formset


@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(staff_required(allowed_user_groups=["2", "7"]), name='dispatch')
class CS_Inquiry_UpdateView(LoginRequiredMixin, UpdateView):
    model = CS_Inquiry
    form_class = CS_InquiryForm
    template_name = 'crm/update_inquiry.html'
    success_url = reverse_lazy('cs_inquiry_list')

    def get_object(self, queryset=None):
        pk = self.kwargs.get('pk')
        obj = get_object_or_404(CS_Inquiry, pk=pk)
        if not self.request.user.is_staff and obj.author != self.request.user.username:
            messages.error(
                self.request, 'You do not have permission to edit this inquiry.')
            return None
        return obj

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Use extra=0 to show only existing responses.
        CS_ResponseFormSet = get_response_formset(extra=0, can_delete=True)
        if self.request.POST:
            context['formset'] = CS_ResponseFormSet(
                self.request.POST,
                self.request.FILES,
                instance=self.object,
                prefix='response'
            )
        else:
            context['formset'] = CS_ResponseFormSet(
                instance=self.object,
                prefix='response'
            )
        context['title'] = f'Update CS Inquiry #{self.object.id}'
        context['inquiry'] = self.object
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']
        if formset.is_valid():
            try:
                with transaction.atomic():
                    inquiry = form.save()
                    formset.instance = inquiry
                    formset.save()
                    messages.success(
                        self.request, 'CS Inquiry updated successfully.')
                    return JsonResponse({
                        'status': 'success',
                        'message': 'CS Inquiry updated successfully.',
                        'redirect_url': str(self.success_url)
                    })
            except Exception as e:
                messages.error(
                    self.request, f'Error updating CS Inquiry: {str(e)}')
                return JsonResponse({
                    'status': 'error',
                    'message': f'Error updating CS Inquiry: {str(e)}'
                }, status=400)
        else:
            messages.error(self.request, 'Please correct the errors below.')
            return JsonResponse({
                'status': 'error',
                'message': 'Please correct the errors below.',
                'errors': formset.errors
            }, status=400)

    def form_invalid(self, form):
        if self.request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'status': 'error',
                'message': '입력하신 내용을 확인해주세요.',
                'errors': form.errors
            }, status=400)
        messages.error(self.request, '입력하신 내용을 확인해주세요.', extra_tags='danger')
        return self.render_to_response(self.get_context_data(form=form))

    def get_success_url(self):
        return reverse_lazy('cs_inquiry_list')
