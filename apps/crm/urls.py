# -*- encoding: utf-8 -*-
"""
Copyright (c) 2023 Theragen Genome Care ( k<PERSON>hna )
"""

from django.urls import path, include

from apps.crm.views import FAQ_ListView, FAQ_DetailView, CREATE_FAQ_View

from apps.crm.views.cs_inquiry import (
                            CS_Inquiry_ListView,
                            CS_Inquiry_DetailView,
                            CS_Inquiry_CreateView,
                            CSInquiryDeleteView,
                            CS_Inquiry_UpdateView,
                            add_response_view,
                            submit_response,
)


from apps.crm.views.marketing_visit import (
    MarketingVisitListView,
    MarketingVisitCreateView,
    MarketingVisitUpdateView,
    MarketingVisitDetailView,
    MarketingVisitDeleteView,
    MarketingVisitListDetailView,
    LocationCreatePopupView,
    MarketingVisitDetailAjaxView,
    add_file_ajax,
    delete_file_ajax,
)
from apps.crm.views.marketing_visit.popup_detail_view import MarketingVisitPopupDetailView

from apps.crm.views import LocationAutocomplete
from django.urls import path
from apps.crm.views import marketing_visit_comment as comment_views

urlpatterns = [
    # FAQ URLs
    path("faq/list/", FAQ_ListView.as_view(), name="faq_list"),
    path("faq/<int:pk>/", FAQ_DetailView.as_view(), name="faq_detail"),
    path("faq/create/", CREATE_FAQ_View.as_view(), name="faq_create"),




    # CS Inquiry URLs
    path("cs_inquiry/list/", CS_Inquiry_ListView.as_view(), name="cs_inquiry_list"),
    path("cs_inquiry/<int:pk>/", CS_Inquiry_DetailView.as_view(), name="cs_inquiry_detail"),
    path("cs_inquiry/create/", CS_Inquiry_CreateView.as_view(), name="cs_inquiry_create"),
    path("cs_inquiry/<int:pk>/update/", CS_Inquiry_UpdateView.as_view(), name="cs_inquiry_update"),
    path("cs_inquiry/<int:pk>/delete/", CSInquiryDeleteView.as_view(), name="cs_inquiry_delete"),
    path('cs-inquiry/<int:pk>/add-response/',add_response_view, name='add_response'),
    path('cs_inquiry/<int:pk>/submit-response/', submit_response, name='submit_response'),




    # Marketing Visit URLs
    path("marketing-visit/", MarketingVisitListDetailView.as_view(), name="marketing_visit_list_detail"),
    path("marketing-visit/list/", MarketingVisitListView.as_view(), name="marketing_visit_list"),
    path("marketing-visit/create/", MarketingVisitCreateView.as_view(), name="marketing_visit_create"),
    path("marketing-visit/<int:pk>/", MarketingVisitDetailView.as_view(), name="marketing_visit_detail"),
    path("marketing_visit/ajax/detail/", MarketingVisitDetailAjaxView.as_view(), name="marketing_visit_detail_ajax"),
    path("marketing-visit/<int:pk>/update/", MarketingVisitUpdateView.as_view(), name="marketing_visit_update"),
    path("marketing-visit/<int:pk>/delete/", MarketingVisitDeleteView.as_view(), name="marketing_visit_delete"),

    # File Upload URLs
    path("marketing-visit/<int:visit_id>/add-file/", add_file_ajax, name="add_file_ajax"),
    path("marketing-visit/file/<int:file_id>/delete/", delete_file_ajax, name="delete_file_ajax"),

    # Comment URLs
    path('comments/<int:pk>/update/', comment_views.CommentUpdateView.as_view(), name='comment_update'),
    path('comments/<int:pk>/delete/', comment_views.CommentDeleteView.as_view(), name='comment_delete'),
    path("marketing-visit/<int:visit_id>/comment/create/", comment_views.CommentCreateView.as_view(), name="create_comment"),

    # Location URLs
    path("marketing_visit/location/create/", LocationCreatePopupView.as_view(), name="location_create_popup"),
    path("location/autocomplete/", LocationAutocomplete.as_view(), name="location_autocomplete"),
    path('marketing_visit/popup-detail/<int:pk>/',  MarketingVisitPopupDetailView.as_view(), name='marketing_visit_popup_detail'),
]
