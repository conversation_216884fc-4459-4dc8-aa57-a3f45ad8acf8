{% extends "layouts/base.html" %}
{% load local_time_filters %}

{% block title %} CS 상세 {% endblock %}

{% block stylesheets %}


<style >
/* Timeline Styles */
.timeline {
    position: relative;
    padding: 15px 0;
    width: 100%;
}
.timeline-item {
    display: flex;
    width: 100%;
    margin-bottom: 20px;
    position: relative;
}
.timeline-dot-wrapper {
    width: 60px;
    position: relative;
}
.timeline-dot-wrapper::before {
    content: '';
    position: absolute;
    left: 29px;
    top: -20px; /* Add top and bottom padding to cover spacing */
    bottom: -20px;
    width: 3px;
    background: linear-gradient(to bottom, #5e72e4, #2dce89);
    border-radius: 3px;
    z-index: 0;
}


.timeline-item::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 29px; /* Align with timeline-dot center */
    width: 3px;
    background: linear-gradient(to bottom, #5e72e4, #2dce89);
    border-radius: 3px;
    z-index: 0;
}






.timeline-dot {
    position: absolute;
    top: 12px;
    left: 23px;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: #5e72e4;
    border: 2px solid #fff;
    box-shadow: 0 0 0 4px rgba(94, 114, 228, 0.2);
    z-index: 1;
}
.timeline-content {
    flex: 1;
    max-width: calc(100% - 60px);
    box-sizing: border-box;

    border-radius: 0.375rem;
}

/* Card Styles */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}
.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
.card-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.card-body {
    padding: 1.5rem;
}

/* Badge Styles */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 600;
    font-size: 0.875rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.badge-response-type {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Form Styles */
.form-group {
    margin-bottom: 1.25rem;
}
.form-control {
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}
.form-control:focus {
    border-color: #5e72e4;
    box-shadow: 0 0 0 0.2rem rgba(94, 114, 228, 0.25);
}
.form-control-label {
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #525f7f;
}

/* Layout Adjustments */
.header.pb-6 {
    padding-bottom: 1.5rem !important;
}
.container-fluid.mt--6 {
    margin-top: -1.5rem !important;
}

/* Typography */
.h4 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}
.text-muted {
    font-size: 0.875rem;
    color: #8898aa !important;
}

/* Inquiry Question */
.inquiry-question {
    font-size: 1.25rem;
    font-weight: 500;
    line-height: 1.7;
    color: #32325d;
    background-color: #f8f9fe;
    border-left: 5px solid #5e72e4;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: inset 0 0 0 1px rgba(94, 114, 228, 0.1);
}

/* Info Sections */
.info-label {
    color: #8898aa;
    font-size: 0.95rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}
.info-value {
    color: #32325d;
    font-size: 1.1rem;
    font-weight: 500;
}

/* Button Styles */
.btn {
    font-weight: 600;
    padding: 0.625rem 1.25rem;
    border-radius: 0.375rem;
    transition: all 0.15s ease;
    box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
}
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}
.btn:active {
    transform: translateY(1px);
    box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
}

/* Response Form Container */
#response-forms-container {
    position: relative;
    width: 100%;
}
#response-forms-container:empty:before {
    content: '새 응답 양식이 여기에 추가됩니다';
    display: block;
    text-align: center;
    padding: 2rem;
    margin-bottom: 2rem;
    background-color: #f8f9fe;
    border-radius: 0.5rem;
    color: #8898aa;
    border: 2px dashed #e9ecef;
    font-weight: 500;
    width: 100%;
}


</style>










{% endblock stylesheets %}

{% block content %}
<div class="header pb-6">
    <div class="container-fluid">
        <div class="header-body">
            <div class="row align-items-center py-2">
                <div class="col-lg-6 col-7">
                    <h6 class="h2 d-inline-block mb-0">CS 상세 내역</h6>
                    <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                        <ol class="breadcrumb breadcrumb-links m-0">
                            <li class="breadcrumb-item"><a href="/"><i class="fas fa-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="{% url 'cs_inquiry_list' %}">CS List</a></li>
                            <li class="breadcrumb-item active" aria-current="page">CS #{{ cs_inquiry.id }}</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-lg-6 col-5 text-right">
                    <a href="{% url 'cs_inquiry_update' cs_inquiry.id %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-edit"></i> 수정
                    </a>
                    <a href="{% url 'cs_inquiry_list' %}" class="btn btn-sm btn-neutral">
                        <i class="fas fa-list"></i> 목록
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid mt--6">
    <div class="row">
        <div class="col-xl-8">
            <!-- Sample and Common Information -->
            <div class="card shadow mb-4">
                <div class="card-header bg-gradient-info text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <h3 class="mb-0">
                                <i class="fas fa-info-circle mr-2"></i>검체 및 기본 정보
                            </h3>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <!-- Sample ID Information -->
                        
                        <div class="col-md-6 mb-3">
                            <div class="info-label d-flex align-items-center">
                                <i class="fas fa-vial text-primary mr-2"></i>
                                <span>차트/ 검체 접수번호</span>
                            </div>
                            <div class="info-value font-weight-bold mt-1 pl-4">
                               {{ cs_inquiry.hospital_chart }} / {{ cs_inquiry.sample_id }}
                            </div>
                        </div>
                        

                        <!-- Inquiry Date -->
                        <div class="col-md-6 mb-3">
                            <div class="info-label d-flex align-items-center">
                                <i class="fas fa-calendar-alt text-success mr-2"></i>
                                <span>문의 접수일</span>
                            </div>
                            <div class="info-value font-weight-bold mt-1 pl-4">
                                {{ cs_inquiry.inquiry_date|date:"Y-m-d" }}
                            </div>
                        </div>

                        <!-- Priority -->
                        <div class="col-md-6 mb-3">
                            <div class="info-label d-flex align-items-center">
                                <i class="fas fa-flag text-warning mr-2"></i>
                                <span>처리 우선순위</span>
                            </div>
                            <div class="info-value font-weight-bold mt-1 pl-4">
                                <span class="badge badge-pill {% if cs_inquiry.priority == 'high' %}badge-danger{% elif cs_inquiry.priority == 'medium' %}badge-warning{% else %}badge-info{% endif %} px-3 py-2">
                                    {{ cs_inquiry.get_priority_display }}
                                </span>
                            </div>
                        </div>

                        <!-- Inquiry Source -->
                        <div class="col-md-6 mb-3">
                            <div class="info-label d-flex align-items-center">
                                <i class="fas fa-route text-info mr-2"></i>
                                <span>접수 경로</span>
                            </div>
                            <div class="info-value font-weight-bold mt-1 pl-4">
                                {{ cs_inquiry.get_inquiry_source_display }}
                            </div>
                        </div>

                        <!-- CS Class -->
                        <div class="col-md-6 mb-3">
                            <div class="info-label d-flex align-items-center">
                                <i class="fas fa-shield-alt text-danger mr-2"></i>
                                <span>보안 등급</span>
                            </div>
                            <div class="info-value font-weight-bold mt-1 pl-4">
                                {{ cs_inquiry.get_cs_class_display }}
                            </div>
                        </div>

                        <!-- Recorded By -->
                        <div class="col-md-6 mb-3">
                            <div class="info-label d-flex align-items-center">
                                <i class="fas fa-user-edit text-primary mr-2"></i>
                                <span>담당 직원</span>
                            </div>
                            <div class="info-value font-weight-bold mt-1 pl-4">
                                {{ cs_inquiry.recorded_by.full_name }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Inquiry Information -->
            <div class="card shadow mb-4">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h3 class="mb-0">
                                <i class="fas fa-question-circle mr-2"></i>고객 문의 내용
                            </h3>
                        </div>
                        <div class="col-4 text-right">
                            <span class="badge badge-lg {% if cs_inquiry.status == 'open' %}badge-danger{% elif cs_inquiry.status == 'in_progress' %}badge-warning{% else %}badge-success{% endif %}">
                                <i class="fas {% if cs_inquiry.status == 'open' %}fa-exclamation-circle{% elif cs_inquiry.status == 'in_progress' %}fa-spinner fa-spin{% else %}fa-check-circle{% endif %} mr-1"></i>
                                {{ cs_inquiry.get_status_display }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="inquiry-question">
                        {{ cs_inquiry.inquiry_question }}
                    </div>
                </div>
            </div>

            <!-- Response Timeline -->

            <!-- Response Timeline -->
            <div class="card shadow mb-4">
                <div class="card-header bg-gradient-success text-white">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h3 class="mb-0">
                                <i class="fas fa-comments mr-2"></i>고객 응대 내역
                            </h3>
                        </div>
                        <div class="col-md-3 text-center">
                            <span class="badge badge-pill badge-light">
                                <i class="fas fa-list-ul mr-1"></i>
                                {{ responses.count }}개의 응답
                            </span>
                        </div>


                        <!-- <div class="col-md-3 text-right">
                            <button type="button" id="add-response-timeline" class="btn btn-sm btn-light">
                                <i class="fas fa-plus-circle mr-1"></i> 새 답변 추가
                            </button>
                        </div> -->


                    </div>
                </div>
                <div class="card-body pt-4 px-4" >
                    <div class="timeline w-100">
                        {% for response in responses %}

                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="card shadow mb-0 w-100 timeline-card">
                                    <div class="card-header bg-light py-3">
                                        <div class="row align-items-center">
                                            <div class="col-md-7">
                                                <span
                                                    class="badge badge-response-type {% if response.response_type == 'short' %}badge-info{% else %}badge-primary{% endif %}">
                                                    <i
                                                        class="fas {% if response.response_type == 'short' %}fa-comment{% else %}fa-file-alt{% endif %} mr-1"></i>
                                                    {{ response.get_response_type_display }}
                                                </span>
                                                <span class="ml-2 font-weight-bold text-primary">
                                                    <i class="fas fa-user mr-1"></i>
                                                    {{ response.answered_by }}
                                                </span>
                                            </div>
                                            <div class="col-md-5 text-right">
                                                <span class="text-muted">
                                                    <i class="fas fa-calendar-alt mr-1"></i>
                                                    {{ response.response_date|date:"Y-m-d" }}
                                                </span>
                                                <span class="ml-2 text-muted">
                                                    <i class="fas fa-clock mr-1"></i>
                                                    {{ response.created_at|local_kst }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body bg-white p-4">
                                        <div class="response-content">
                                            {{ response.answer|safe }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="text-center py-5 bg-light rounded w-100 mb-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="mb-0 text-muted font-weight-bold">아직 등록된 응답이 없습니다.</p>
                            <p class="text-muted small mt-2">오른쪽의 "새 답변 추가" 버튼을 클릭하여 응답을 등록해보세요.</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>






















        </div>

        <div class="col-xl-4">
            <!-- Contact Information -->
            <div class="card shadow mb-4">
                <div class="card-header bg-gradient-info text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-user-circle mr-2"></i>문의자 정보
                    </h3>
                </div>
                <div class="card-body p-4">
                    <div class="mb-4">
                        <div class="info-label d-flex align-items-center">
                            <i class="fas fa-user text-info mr-2"></i>
                            <span>발신자 정보</span>
                        </div>
                        <div class="info-value font-weight-bold mt-1 pl-4">{{ cs_inquiry.inquiry_person }}</div>
                    </div>

                    <div class="mb-4">
                        <div class="info-label d-flex align-items-center">
                            <i class="fas fa-building text-info mr-2"></i>
                            <span>소속 기관 정보</span>
                        </div>
                        <div class="table-responsive mt-1 pl-4">
                            <table class="table table-sm table-hover mb-0">
                                {% if cs_inquiry.jedan %}
                                <tr>
                                    <td class="pl-0 border-top-0">
                                        <i class="fas fa-hospital text-primary mr-2"></i>재단
                                    </td>
                                    <td class="text-right border-top-0 font-weight-bold">{{ cs_inquiry.jedan.jedan_name }}</td>
                                </tr>
                                {% endif %}

                                {% if cs_inquiry.jedan_branch %}
                                <tr>
                                    <td class="pl-0 {% if forloop.first %}border-top-0{% endif %}">
                                        <i class="fas fa-code-branch text-success mr-2"></i>지점
                                    </td>
                                    <td class="text-right {% if forloop.first %}border-top-0{% endif %} font-weight-bold">{{ cs_inquiry.jedan_branch }}</td>
                                </tr>
                                {% endif %}

                                {% if cs_inquiry.hospital %}
                                <tr>
                                    <td class="pl-0 {% if forloop.first %}border-top-0{% endif %}">
                                        <i class="fas fa-clinic-medical text-info mr-2"></i>병원
                                    </td>
                                    <td class="text-right {% if forloop.first %}border-top-0{% endif %} font-weight-bold">{{ cs_inquiry.hospital.hospital_name }}</td>
                                </tr>
                                {% endif %}

                                {% if cs_inquiry.other_org %}
                                <tr>
                                    <td class="pl-0 {% if forloop.first %}border-top-0{% endif %}">
                                        <i class="fas fa-building text-warning mr-2"></i>기타 기관
                                    </td>
                                    <td class="text-right {% if forloop.first %}border-top-0{% endif %} font-weight-bold">{{ cs_inquiry.other_org }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="info-label d-flex align-items-center">
                            <i class="fas fa-address-book text-info mr-2"></i>
                            <span>연락처</span>
                        </div>
                        <div class="info-value font-weight-bold mt-1 pl-4">
                            <i class="fas fa-phone-alt text-primary mr-2"></i>
                            {{ cs_inquiry.inquiry_phone }}
                        </div>
                        {% if cs_inquiry.inquiry_email %}
                        <div class="text-muted mt-1 pl-4">
                            <i class="fas fa-envelope text-secondary mr-2"></i>
                            {{ cs_inquiry.inquiry_email }}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-0">
                        <div class="info-label d-flex align-items-center">
                            <i class="fas fa-info-circle text-info mr-2"></i>
                            <span>서비스 정보</span>
                        </div>
                        <div class="table-responsive mt-1 pl-4">
                            <table class="table table-sm table-hover mb-0">
                                <tr>
                                    <td class="pl-0 border-top-0">
                                        <i class="fas fa-cube text-primary mr-2"></i>서비스
                                    </td>
                                    <td class="text-right border-top-0 font-weight-bold">{{ cs_inquiry.get_service_type_display }}</td>
                                </tr>
                                <tr>
                                    <td class="pl-0">
                                        <i class="fas fa-tag text-success mr-2"></i>구분
                                    </td>
                                    <td class="text-right font-weight-bold">{{ cs_inquiry.get_category1_display }}</td>
                                </tr>
                                <tr>
                                    <td class="pl-0">
                                        <i class="fas fa-users text-info mr-2"></i>담당
                                    </td>
                                    <td class="text-right font-weight-bold">{{ cs_inquiry.get_related_department_display }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Response Card -->
            <div class="card shadow mb-4">
                <div class="card-header bg-gradient-success text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-reply mr-2"></i>새로운 고객 응대 등록
                    </h3>
                </div>
                <div class="card-body p-4">


                    <form method="post" action="{% url 'submit_response' cs_inquiry.pk %}" id="response-form">
                        {% csrf_token %}
                        <div class="form-group">
                            <label class="font-weight-bold d-flex align-items-center">
                                <i class="fas fa-comment-dots text-success mr-2"></i>
                                답변 내용
                                <span class="text-danger ml-1">*</span>
                            </label>
                            {{ response_form.answer }}
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label class="font-weight-bold d-flex align-items-center">
                                    <i class="fas fa-list-alt text-primary mr-2"></i>
                                    응답 유형
                                    <span class="text-danger ml-1">*</span>
                                </label>
                                {{ response_form.response_type }}
                            </div>
                            <div class="form-group col-md-6">
                                <label class="font-weight-bold d-flex align-items-center">
                                    <i class="fas fa-calendar-day text-info mr-2"></i>
                                    응답일
                                    <span class="text-danger ml-1">*</span>
                                </label>
                                {{ response_form.response_date }}
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary btn-lg btn-block mt-4">
                            <i class="fas fa-paper-plane mr-2"></i> 응답 등록
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Response Forms Container -->
        <div id="response-forms-container" class="mt-4 col-xl-8 px-0">
            <!-- AJAX loaded response forms will appear here -->
        </div>


    </div>

    {% include 'includes/footer.html' %}
</div>
{% endblock content %}

{% block javascripts %}
<script src="/static/assets/vendor/chart.js/dist/Chart.min.js"></script>
<script src="/static/assets/vendor/chart.js/dist/Chart.extension.js"></script>
<script src="/static/assets/vendor/jvectormap-next/jquery-jvectormap.min.js"></script>
<script src="/static/assets/js/vendor/jvectormap/jquery-jvectormap-world-mill.js"></script>
<!-- SweetAlert2 for better alerts -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


<script>
    $(document).ready(function () {
        // Initialize Summernote for the main response form
        $('#response-form textarea').summernote({
            height: 200,
            placeholder: '응답 내용을 입력해주세요...',
            toolbar: [
                ['style', ['style', 'bold', 'italic', 'underline', 'clear']],
                ['font', ['strikethrough']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['insert', ['link', 'picture', 'table']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks: {
                onImageUpload: function(files) {
                    // You can implement image upload functionality here if needed
                    alert('이미지 업로드 기능은 현재 지원되지 않습니다.');
                }
            }
        });

        // Set default date for response_date if empty


        // Add new response form via AJAX with animation - for both buttons
        $('#add-response, #add-response-timeline').click(function (e) {
            e.preventDefault();
            $(this).addClass('disabled').html('<i class="fas fa-spinner fa-spin"></i> 처리중...');

            // Count current response forms
            var currentCount = $('.response-form').length;

            // Limit maximum responses if desired
            if (currentCount >= 3) {
                Swal.fire({
                    title: '추가 불가!',
                    text: '최대 3개의 응답만 추가할 수 있습니다.',
                    icon: 'warning',
                    confirmButtonText: '확인'
                });
                $(this).removeClass('disabled').html('<i class="fas fa-plus-circle mr-1"></i> 새 답변 양식 추가');
                return;
            }

            $.ajax({
                url: "{% url 'add_response' cs_inquiry.id %}",
                type: "POST",
                data: {
                    'current_count': currentCount,
                    'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                success: function (data) {
                    // Create element and hide it initially
                    var newForm = $(data.html).hide();

                    // Append to container
                    $('#response-forms-container').append(newForm);

                    // Fade in with animation
                    newForm.fadeIn(500);

                    // Scroll to the new form
                    $('html, body').animate({
                        scrollTop: newForm.offset().top - 100
                    }, 500);

                    // Initialize Summernote for the new form
                    $('#response-form-' + currentCount + ' textarea').summernote({
                        height: 150,
                        placeholder: '응답 내용을 입력해주세요...',
                        toolbar: [
                            ['style', ['style', 'bold', 'italic', 'underline', 'clear']],
                            ['font', ['strikethrough']],
                            ['para', ['ul', 'ol', 'paragraph']],
                            ['insert', ['link']]
                        ]
                    });


                    // Re-enable the button
                    $('#add-response').removeClass('disabled').html('<i class="fas fa-plus-circle mr-1"></i> 새 답변 양식 추가');
                },
                error: function (xhr, errmsg, err) {
                    console.error("응답 추가 오류:", errmsg);
                    Swal.fire({
                        title: '오류 발생!',
                        text: '응답 양식을 추가하는 중 오류가 발생했습니다.',
                        icon: 'error',
                        confirmButtonText: '확인'
                    });
                    $('#add-response').removeClass('disabled').html('<i class="fas fa-plus-circle mr-1"></i> 새 답변 양식 추가');
                }
            });
        });

        // Handle delete response button with confirmation
        $(document).on('click', '.delete-response', function() {
            var responseForm = $(this).closest('.response-form');

            Swal.fire({
                title: '응답 삭제',
                text: '이 응답 양식을 삭제하시겠습니까?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: '삭제',
                cancelButtonText: '취소',
                confirmButtonColor: '#dc3545'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Fade out with animation
                    responseForm.fadeOut(300, function() {
                        $(this).remove();
                    });
                }
            });
        });

        // Handle form submission via AJAX with loading state
        $(document).on('submit', '.ajax-response-form', function(e) {
            e.preventDefault();
            var form = $(this);
            var submitBtn = form.find('button[type="submit"]');
            var originalBtnText = submitBtn.html();

            // Disable button and show loading state
            submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i> 저장중...');

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                success: function(response) {
                    // Show success message
                    Swal.fire({
                        title: '성공!',
                        text: '응답이 성공적으로 저장되었습니다.',
                        icon: 'success',
                        confirmButtonText: '확인'
                    }).then(() => {
                        // Reload the page to show the new response
                        location.reload();
                    });
                },
                error: function(xhr, errmsg, err) {
                    console.error("응답 저장 오류:", errmsg);

                    // Show error message
                    Swal.fire({
                        title: '오류 발생!',
                        text: '응답 저장 중 오류가 발생했습니다. 다시 시도해주세요.',
                        icon: 'error',
                        confirmButtonText: '확인'
                    });

                    // Re-enable submit button
                    submitBtn.prop('disabled', false).html(originalBtnText);
                }
            });
        });

        // Handle main form submission with loading state
        $('#response-form').on('submit', function(e) {
            e.preventDefault();
            var form = $(this);
            var submitBtn = form.find('button[type="submit"]');
            var originalBtnText = submitBtn.html();

            // Disable button and show loading state
            submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i> 저장중...');

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                success: function(response) {
                    // Show success message
                    Swal.fire({
                        title: '성공!',
                        text: '응답이 성공적으로 저장되었습니다.',
                        icon: 'success',
                        confirmButtonText: '확인'
                    }).then(() => {
                        // Reload the page to show the new response
                        location.reload();
                    });
                },
                error: function(xhr, errmsg, err) {
                    console.error("응답 저장 오류:", errmsg);

                    // Show error message
                    Swal.fire({
                        title: '오류 발생!',
                        text: '응답 저장 중 오류가 발생했습니다. 다시 시도해주세요.',
                        icon: 'error',
                        confirmButtonText: '확인'
                    });

                    // Re-enable submit button
                    submitBtn.prop('disabled', false).html(originalBtnText);
                }
            });
        });
    });
</script>




{% endblock javascripts %}