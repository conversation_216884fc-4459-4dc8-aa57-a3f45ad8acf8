{% load static %}
{% block content %}
<style>
    /* Custom styles for response forms */
    .response-form {
        transition: all 0.3s ease;
        position: relative;
    }

    .response-form.to-be-deleted {
        background-color: #ffeeee !important;
        border-color: #ffcccc !important;
    }

    .response-header {
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
        margin-bottom: 15px !important;
    }

    .form-check-inline {
        display: inline-flex;
        align-items: center;
    }

    .delete-response {
        margin-left: 10px;
    }

    .responses-container {
        border-top: 1px solid #eee;
        padding-top: 15px;
    }
</style>
<!-- Simple Combined CS Inquiry and Response Form -->
<div class="card mb-2">
    <div class="card-body">

                <!-- Hidden hospital field -->
                <input type="hidden" id="id_hospital_name" name="hospital_name"
                    value="{% if form.hospital_name %}{{ form.hospital_name.id }}{% endif %}">



        <div id="selectedLocationDisplay" class="d-flex align-items-center mb-3">
            <h3 class="mb-2 text-primary">CS 문의 및 응답 양식
                <strong class="text-danger">*</strong>
            </h3>
            <span class="location-name text-success h2 mb-0">
                {% if form.hospital_name %}
                {{ form.hospital_name }}
                {% else %}
                <span class="text-muted">[선택된 병원이 없습니다]</span> {% endif %}
            </span>
        </div>


        <!-- Essential Fields (Always Visible) -->
        <div class="essential-fields m-0">
            <!-- First Row: Most Important Fields -->
            <div class="form-row m-0">


                <div class="form-group col-md-1">
                    <label class="font-weight-bold text-dark mb-1">
                        {{ form.inquiry_person.label }}
                        <span style="color: red;">★</span>
                    </label> {{ form.inquiry_person }}
                </div>
                <div class="form-group col-md-1">
                    <label class="font-weight-bold text-dark mb-1">
                        {{ form.inquiry_phone.label }}
                    </label> {{ form.inquiry_phone }}
                </div>






                <div class="col-md-2">
                    <div class="form-group location-search-wrapper">
                        <div class="d-flex align-items-center">
                            <label for="hospital_search" class="font-weight-bold mb-0 mr-3">
                                <i class="fas fa-hospital-alt mr-2"></i>
                                병원 검색
                            </label>
                        </div>
                        <div class="input-group">
                            <input type="text" id="hospital_search" class="form-control" placeholder="병원명을 입력하세요..."
                                autocomplete="off">
                        </div>


                        <!-- Expand Button for Additional Fields -->

                        <button type="button" class="btn btn-sm btn-outline-secondary"
                            onclick="toggleAdditionalFields()">
                            <i class="fas fa-chevron-down"></i>
                            추가 필드 표시
                        </button>


                    </div>
                </div>
                <div class="form-group col-md-1">
                    <label class="font-weight-bold text-dark mb-1">
                        {{ form.jedan.label }}
                    </label> {{ form.jedan }}
                </div>
                <div class="form-group col-md-1">
                    <label class="font-weight-bold text-dark mb-1">
                        {{ form.service_type.label }}
                        <span style="color: red;">★</span>
                    </label> {{ form.service_type }}
                </div>
                <div class="form-group col-md-1">
                    <label class="font-weight-bold text-dark mb-1">
                        {{ form.category1.label }}
                        <span style="color: red;">★</span>
                    </label> {{ form.category1 }}
                </div>

                <div class="form-group col-md-1">
                    <label class="font-weight-bold text-dark mb-1">
                        {{ form.sample_id.label }}
                    </label> {{ form.sample_id }}
                </div>



                <div class="form-group col-md-1">
                    <label class="font-weight-bold text-dark mb-1">
                        {{ form.hospital_chart.label }}
                    </label> {{ form.hospital_chart }}
                </div>





            </div>
            <!-- Additional Fields (Hidden by Default) -->
            <div id="additional-fields" style="display: none;" class="m-0">
                <!-- Additional Row 1: Organization Info -->
                <div class="form-row">
                    <div class="form-group col-md-1">
                        <label class="font-weight-bold text-dark mb-1">
                            {{ form.jedan_branch.label }}
                        </label> {{ form.jedan_branch }}
                    </div>
                    <div class="form-group col-md-1">
                        <label class="font-weight-bold text-dark mb-1">
                            {{ form.other_org.label }}
                        </label> {{ form.other_org }}
                    </div>
                    <div class="form-group col-md-1">
                        <label class="font-weight-bold text-dark mb-1">
                            {{ form.cs_class.label }}
                        </label> {{ form.cs_class }}
                    </div>
                    <div class="form-group col-md-1">
                        <label class="font-weight-bold text-dark mb-1">
                            {{ form.status.label }}
                        </label> {{ form.status }}
                    </div>
                    <div class="form-group col-md-1">
                        <label class="font-weight-bold text-dark mb-1">
                            {{ form.related_department.label }}
                        </label> {{ form.related_department }}
                    </div>
                    <div class="form-group col-md-1">
                        <label class="font-weight-bold text-dark mb-1">
                            {{ form.inquiry_source.label }}
                        </label> {{ form.inquiry_source }}
                    </div>

                    <div class="form-group col-md-1">
                        <label class="font-weight-bold text-dark mb-1">
                            {{ form.inquiry_date.label }}
                        </label> {{ form.inquiry_date }}
                    </div>
                    <div class="form-group col-md-1">
                        <label class="font-weight-bold text-dark mb-1">
                            {{ form.resolution_date.label }}
                        </label> {{ form.resolution_date }}
                    </div>
                    <div class="form-group col-md-1">
                        <label class="font-weight-bold text-dark mb-1">
                            {{ form.priority.label }}
                        </label> {{ form.priority }}
                    </div>
                </div>
            </div>
            <!-- Inquiry Question (Full Width) -->
            <div class="form-group">
                <label class="font-weight-bold text-dark mb-1">
                    {{ form.inquiry_question.label }}
                    <span style="color: red;">★</span>
                </label> {{ form.inquiry_question }}
            </div>



            <!-- Response Section -->
            <div class="responses-container mt-4">
                <h4 class="mb-3 text-primary">CS 응답 관리</h4>

                <!-- Include the response form part -->
                {% include "crm/cs_inquiry_forms/response_form_part.html" %}
            </div>




        </div>
    </div>
    <!-- Hidden Fields -->
    {{ form.internal_notes.as_hidden }} {% endblock content %} {% block javascripts %}
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Debug check for jQuery
            if (typeof jQuery !== 'undefined') {
                console.log('jQuery is loaded');
            } else {
                console.error('jQuery is not loaded');
            }

            // Initialize Select2 for hospital search
            $('#hospital_search').select2({
                theme: 'bootstrap4',
                placeholder: '병원을 검색하세요...',
                ajax: {
                    url: '/crm/location/autocomplete/',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            q: params.term
                        };
                    },
                    processResults: function (data) {
                        if (data.results.length === 0) {
                            data.results.push({
                                id: 'create_new',
                                text: '➕ 새 병원 등록하기',
                                create: true
                            });
                        }
                        return {
                            results: data.results
                        };
                    },
                    cache: true
                }
            });

            // Handle hospital selection
            $('#hospital_search').on('select2:select', function (e) {
                const data = e.params.data;
                if (data.id === 'create_new') {
                    $.get('/crm/marketing_visit/location/create/', function (html) {
                        $('#locationModal .modal-content').html(html);
                        $('#locationModal').modal('show');
                    });
                    $(this).val(null).trigger('change');
                    return;
                }
                // Update hidden field and show selected location
                $('#id_hospital_name').val(data.id);
                $('#selectedLocationDisplay .location-name').text(data.text);
                $('#selectedLocationDisplay').addClass('show');
            });
        });

        // Toggle additional fields visibility
        function toggleAdditionalFields() {
            const additionalFields = document.getElementById('additional-fields');
            const button = event.target.tagName === 'BUTTON' ? event.target : event.target.parentElement;

            if (additionalFields.style.display === 'none') {
                additionalFields.style.display = 'block';
                button.innerHTML = '<i class="fas fa-chevron-up"></i> 추가 필드 숨기기';
            } else {
                additionalFields.style.display = 'none';
                button.innerHTML = '<i class="fas fa-chevron-down"></i> 추가 필드 표시';
            }
        }
    </script>

    <!-- Include response form handlers -->
    <script src="{% static 'crm/js/response_form_handlers.js' %}"></script>
    {% endblock javascripts %}

    <!-- Location Create Modal -->
    <div class="modal fade" id="locationModal" tabindex="-1" role="dialog" aria-labelledby="locationModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <!-- Modal content will be loaded via AJAX -->
            </div>
        </div>
    </div>
    {% block extra_css %}

    {% endblock extra_css %}

    <!-- JavaScript for toggle functionality -->
    <script>




    </script>