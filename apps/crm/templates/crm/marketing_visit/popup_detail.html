<!-- 모바일 전용 상단 뒤로가기 (PC에서는 숨김) -->
<div class="position-sticky top-0 bg-white px-3 py-2 border-bottom d-md-none z-10 mb-3">
    <a href="{% url 'marketing_visit_list' %}" class="text-decoration-none text-primary">
        <i class="fas fa-arrow-left me-2"></i> 목록으로
    </a>
</div>

<!-- 방문 상세 정보 헤더 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="text-primary mb-0">
        <i class="fas fa-info-circle me-2"></i> 방문 상세 정보
    </h4>
    <!-- <a href="{% url 'marketing_visit_list' %}" class="btn btn-sm btn-outline-primary">
        <i class="fas fa-list me-1"></i> 목록으로
    </a> -->
</div>

<!-- 방문 정보 -->
<div class="mb-4">
    
    <div class="table-responsive bg-white rounded shadow-sm">
        <table class="table table-borderless mb-0 text-sm" style="table-layout: fixed;">
            <colgroup>
                <col style="width: 25%;">
                <col style="width: 25%;">
                <col style="width: 25%;">
                <col style="width: 25%;">
            </colgroup>
            <tbody>
                <tr class="align-middle ">
                    <th class="fw-bold text-nowrap text-primary ">
                        <i class="fas fa-hospital me-1 text-danger"></i> 병원명
                    </th>
                    <td class="fw-medium text-truncate">{{ selected_visit.location_name.hospital_name }}</td>
                    <th class="fw-bold text-nowrap text-primary">
                        <i class="fas fa-calendar-alt me-1 text-success"></i> 방문 일자
                    </th>
                    <td class="fw-medium text-nowrap">{{ selected_visit.visit_date|date:"Y/m/d" }}</td>
                </tr>
                <tr class="align-middle">
                    <th class="fw-bold text-nowrap text-primary">
                        <i class="fas fa-user-tie me-1 text-info"></i> 면담 대상
                    </th>
                    <td class="fw-medium text-truncate">{{ selected_visit.person_met }}</td>
                    <th class="fw-bold text-nowrap text-primary">
                        <i class="fas fa-folder-open me-1 text-warning"></i> 자료 제공
                    </th>
                    <td class="fw-medium text-truncate">{{ selected_visit.files_provided }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- 제목 및 상세내용 -->
<div class="card shadow-sm mb-4 border-0">
    <div class="card-header bg-light text-dark py-3 px-4 rounded-top">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h5 class="mb-1 fs-5 fs-md-4">
                    📝 제목: <strong>{{ selected_visit.main_topic }}</strong>
                </h5>
                <div class="small mt-1">
                    <span class="fw-bold text-primary">
                        <i class="fas fa-user-circle me-1"></i> 작성자:
                    </span>
                    <span class="fw-semibold text-dark">
                        {{ selected_visit.marketing_manager|default:"<span class='badge bg-secondary'>미입력</span>"|safe }}
                    </span>
                    <span class="text-muted"> · {{ selected_visit.created_at|date:"Y.m.d" }}</span>
                </div>
            </div>
            <button onclick="window.print()" class="btn btn-sm btn-outline-secondary d-print-none">
                <i class="fas fa-print"></i>
            </button>
        </div>
    </div>
    <div class="card-body px-4 py-3 bg-white">
        <div class="small text-body lh-lg">
            {{ selected_visit.discussion_details|safe }}
        </div>
    </div>
</div>

<!-- 댓글 -->
<div class="border-top pt-4 mt-4">
    <h6 class="text-muted fw-bold mb-3">
        <i class="fas fa-comments me-2 text-primary"></i> 댓글
    </h6>
    {% if comments %}
    {% for comment in comments %}
    {% include 'crm/marketing_visit/comment_content.html' %}
    {% endfor %}
    {% else %}
    <div class="text-center text-muted small py-3 border rounded">등록된 댓글이 없습니다.</div>
    {% endif %}
</div>

<!-- 첨부 파일 -->
{% if selected_visit.attachment %}
<div class="border-top pt-4 mt-4">
    <h6 class="text-muted fw-bold mb-3">
        <i class="fas fa-paperclip me-2 text-primary"></i> 첨부 파일
    </h6>
    <div class="card shadow-sm border-0">
        <div class="card-body d-flex align-items-center">
            <i class="fas fa-file-alt fa-lg me-3 text-primary"></i>
            <div>
                <p class="mb-1 fw-medium">{{ selected_visit.attachment_description }}</p>
                <a href="{{ selected_visit.attachment.url }}" class="btn btn-sm btn-outline-primary shadow-sm"
                    target="_blank">
                    <i class="fas fa-download me-1"></i> 다운로드
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}