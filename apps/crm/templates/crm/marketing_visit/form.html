{% extends "layouts/base.html" %} {% load static %} {% block extra_css %}
{% comment %} <style>
    /* Card Styling */

    .card {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .card-header {
        background: linear-gradient(45deg, #1a237e, #0d47a1) !important;
        border-bottom: none;
        padding: 1.25rem;
    }

    .card-header h4 {
        font-weight: 600;
        letter-spacing: 0.5px;
    }

    /* Location Section Styling */

    .selected-location {
        background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
        border: none;
        border-radius: 15px;
        padding: 20px;
        margin: 15px 0;
        position: relative;
        transition: all 0.3s ease;
    }

    .selected-location.show {
        transform: translateY(0);
        opacity: 1;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .selected-location strong {
        color: #37474f;
        font-size: 0.9em;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .selected-location .location-name {
        color: #1976d2;
        font-size: 1.4em;
        margin: 10px 0;
        font-weight: 600;
    }

    .selected-location .remove-location {
        position: absolute;
        top: 15px;
        right: 15px;
        background: #fff;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #e53935;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .selected-location .remove-location:hover {
        background: #ffebee;
        transform: scale(1.1);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
    }

    /* Form Section Styling */

    .form-section {
        background: linear-gradient(45deg, #ffffff, #f8f9fa);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        border: 1px solid #e0e0e0;
    }

    .form-section-title {
        color: #1a237e;
        font-weight: 700;
        font-size: 1.2em;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 2px solid #e3f2fd;
        display: flex;
        align-items: center;
    }

    .form-section-title i {
        margin-right: 10px;
        color: #1976d2;
    }

    /* Form Controls */

    .form-group label {
        color: #2c3e50;
        font-weight: 600;
        font-size: 0.95em;
        margin-bottom: 8px;
    }

    .form-control {
        border: 1px solid #bdbdbd;
        border-radius: 6px;
        padding: 0.6rem 1rem;
        transition: all 0.2s ease;
    }

    .form-control:focus {
        border-color: #1976d2;
        box-shadow: 0 0 0 0.2rem rgba(25, 118, 210, 0.15);
    }

    /* Select2 Customization */

    .select2-container--bootstrap4 .select2-selection--single {
        border: 1px solid #bdbdbd;
        border-radius: 6px;
        height: calc(2.5rem + 2px);
        padding: 0.5rem 1rem;
    }

    .select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
        line-height: 1.5;
        color: #2c3e50;
    }

    .select2-container--bootstrap4 .select2-results__option--highlighted {
        background-color: #1976d2;
        color: white;
    }

    /* Buttons */

    .btn {
        padding: 0.6rem 1.5rem;
        font-weight: 600;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .btn-primary {
        background: linear-gradient(45deg, #1976d2, #1565c0);
        border: none;
    }

    .btn-primary:hover {
        background: linear-gradient(45deg, #1565c0, #0d47a1);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .btn-secondary {
        background: linear-gradient(45deg, #6c757d, #5a6268);
        border: none;
    }

    .btn-secondary:hover {
        background: linear-gradient(45deg, #5a6268, #4a525a);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Animations */

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Required Field Indicator */

    .text-danger {
        color: #d32f2f !important;
        font-weight: bold;
    }

    /* Help Text */

    .form-text {
        color: #546e7a;
        font-size: 0.85em;
        margin-top: 5px;
    }

    /* Error Messages */

    .invalid-feedback {
        color: #d32f2f;
        font-size: 0.85em;
        margin-top: 5px;
    }

    /* Responsive Adjustments */

    @media (max-width: 768px) {
        .form-section {
            padding: 20px;
        }

        .card-header {
            padding: 1rem;
        }

        .btn {
            width: 100%;
            margin-bottom: 10px;
        }
    }

    /* Enhanced Location Search Styling */

    .location-search-wrapper {
        position: relative;
        margin-bottom: 25px;
    }

    .location-search-wrapper .select2-container {
        width: 100% !important;
    }

    .location-search-wrapper .select2-container--bootstrap4 .select2-selection--single {
        height: 50px;
        padding: 10px 15px;
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .location-search-wrapper .select2-container--bootstrap4 .select2-selection--single:hover {
        border-color: #90caf9;
    }

    .location-search-wrapper .select2-container--bootstrap4 .select2-selection--single:focus {
        border-color: #1976d2;
        box-shadow: 0 0 0 0.2rem rgba(25, 118, 210, 0.25);
    }

    /* Select2 Dropdown Customization */

    .select2-container--bootstrap4 .select2-results__option {
        padding: 10px 15px;
        transition: all 0.2s ease;
    }

    .select2-container--bootstrap4 .select2-results__option[aria-selected=true] {
        background-color: #e3f2fd;
        color: #1976d2;
    }

    .select2-container--bootstrap4 .select2-results__option--highlighted {
        background: linear-gradient(45deg, #1976d2, #2196f3);
    }

    /* Create New Location Option */

    .select2-results__option[data-select2-id="create_new"] {
        color: #4caf50;
        font-weight: 600;
        border-top: 1px solid #e0e0e0;
        margin-top: 5px;
        padding-top: 15px;
    }
</style>
 {% endcomment %}



{% endblock extra_css %} {% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-11 mt-4">
            <div class="card shadow-sm rounded ">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">마케팅 방문 기록</h4>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" novalidate>
                        {% csrf_token %}

                        <!-- Location Section -->
                        <div class="form-section">
                            <div class="col-md-12">
                                <div class="selected-location" id="selectedLocationDisplay">
                                    <strong class="text-dark"> {{ request.user }} 님 께서 방문 하신 병원 </strong>
                                    <strong class="location-name text-success h1"> &nbsp;&nbsp;
                                        {% if form.instance.location_name %}
                                        {{ form.instance.location_name.hospital_name }}
                                        {% else %}
                                        ..........................
                                        {% endif %}
                                        &nbsp;&nbsp; </strong>
                                    <strong class="text-dark"> &nbsp;&nbsp; 방문 정보 등록 중입니다 . </strong>
                                </div>
                            </div>
                            <hr>
                            <!-- Hidden location field -->
                            <input type="hidden" id="id_location_name" name="location_name"
                                value="{% if form.instance.location_name %}{{ form.instance.location_name.id }}{% endif %}">
                        </div>

                        <!-- Basic Information Section -->
                        <div class="form-section">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group location-search-wrapper">

                                        <div class="d-flex align-items-center">
                                            <label for="location_search" class="font-weight-bold mb-0 mr-3">
                                                <i class="fas fa-hospital-alt mr-2"></i>병원 검색
                                            </label>

                                            <div class="selected-location" id="selectedLocationDisplay">
                                                <strong class="location-name text-danger"> </strong>
                                            </div>
                                        </div>

                                        <div class="input-group">
                                            <input type="text" id="location_search" class="form-control"
                                                placeholder="병원명을 입력하세요..." autocomplete="off">

                                        </div>

                                    </div>
                                </div>

                                <!-- Visit Date -->
                                <div class="col-md-3 text-center"><i class="fas fa-calendar-day"></i>
                                    <label for="{{ form.visit_date.id_for_label }}" class="form-label">
                                        {{ form.visit_date.label }}{% if form.visit_date.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.visit_date }}
                                    {% if form.visit_date.errors %}
                                        <div class="invalid-feedback d-block">{{ form.visit_date.errors|striptags }}</div>
                                    {% endif %}
                                </div>

                                <!-- Visit Person -->
                                <div class="col-md-3 text-center"> <i class="fas fa-users"></i>
                                    <label for="{{ form.person_met.id_for_label }}" class="form-label">
                                        {{ form.person_met.label }}{% if form.person_met.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.person_met }}
                                    {% if form.person_met.errors %}
                                        <div class="invalid-feedback d-block">{{ form.person_met.errors|striptags }}</div>
                                    {% endif %}
                                </div>

                                <!-- Visit Person Files Provided files_provided -->
                                <div class="col-md-3 text-center"> <i class="fas fa-file-pdf"></i>
                                    <label for="{{ form.files_provided.id_for_label }}" class="form-label">
                                        {{ form.files_provided.label }}{% if form.files_provided.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.files_provided }}
                                    {% if form.files_provided.errors %}
                                        <div class="invalid-feedback d-block">{{ form.files_provided.errors|striptags }}</div>
                                    {% endif %}
                                </div>

                            </div>
                        </div>

                        <!-- Discussion Details Section -->

                        <h4 class="form-section-title ">
                            <i class="fas fa-book-open"></i> </i> 방문 목적 (제목 )
                        </h5>

                        <div class="col-md-12">
                            <!-- <label for="{{ form.main_topic.id_for_label }}" class="form-label">
                                    {{ form.main_topic.label }}{% if form.main_topic.field.required %}<span
                                        class="text-danger">*</span>{% endif %} </label> -->

                            {{ form.main_topic }} {% if form.main_topic.errors %}
                            <div class="invalid-feedback d-block">{{ form.main_topic.errors|striptags }}</div>
                            {% endif %}

                            <!-- {% if form.main_topic.help_text %}
                                <div class="form-text">{{ form.main_topic.help_text }}</div>
                                {% endif %} -->
                        </div>

                        <!-- Discussion Details -->
                        <h4 class="form-section-title mt-2 ">
                            <i class="fas fa-comments mr-2"></i> 방문 내용 자세히 기록
                        </h4>

                        <div class="col-md-12">

                            <!-- <label for="{{ form.discussion_details.id_for_label }}" class="form-label">
                                    {{ form.discussion_details.label }}{% if form.discussion_details.field.required %}
                                    <span class="text-danger">*</span>{% endif %}
                                </label> -->

                            {{ form.discussion_details }} {% if form.discussion_details.errors %}
                            <div class="invalid-feedback d-block">{{ form.discussion_details.errors|striptags }}
                            </div>
                            {% endif %}
                            <!--
                            {% if form.discussion_details.help_text %}
                            <div class="form-text">{{ form.discussion_details.help_text }}</div>
                            {% endif %} -->
                        </div>
                        <br>

                        <div class="row mt-2">

                            <div class="col-md-6">
                                <label for="{{ form.attachment.id_for_label }}" class="form-label">
                                    {{ form.attachment.label }}
                                </label> {{ form.attachment }} {% if form.attachment.errors %}
                                <div class="invalid-feedback d-block">{{ form.attachment.errors|striptags }}</div>
                                {% endif %}

                                 {% comment %} {% if form.attachment.help_text %}
                                <div class="form-text">{{ form.attachment.help_text }}</div>
                                {% endif %} {% endcomment %}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.attachment_description.id_for_label }}" class="form-label">
                                    {{ form.attachment_description.label }}
                                </label> {{ form.attachment_description }} {% if form.attachment_description.errors %}
                                <div class="invalid-feedback d-block">{{ form.attachment_description.errors|striptags }}
                                </div>
                                {% endif %}

                                 {% comment %} {% if form.attachment_description.help_text %}
                                <div class="form-text">{{ form.attachment_description.help_text }}</div>
                                {% endif %}  {% endcomment %}


                            </div>

                        </div>

                        <!-- Additional Files Section (Only for Edit Mode and Original Author) -->
                        {% if form.instance.id and can_edit %}
                        <div class="form-section mt-4">
                            <h4 class="form-section-title">
                                <i class="fas fa-file-upload mr-2"></i>추가 파일 관리
                            </h4>

                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">현재 파일 목록</h5>
                                        <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#addFileModal">
                                            <i class="fas fa-plus mr-1"></i>파일 추가
                                        </button>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div id="files-container">
                                        {% if files %}
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead class="thead-light">
                                                    <tr>
                                                        <th>파일명</th>
                                                        <th>설명</th>
                                                        <th>업로드 일시</th>
                                                        <th>작업</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for file in files %}
                                                    <tr id="file-{{ file.id }}">
                                                        <td>
                                                            <i class="fas fa-file mr-2 text-primary"></i>
                                                            {{ file.get_file_name }}
                                                        </td>
                                                        <td>{{ file.description }}</td>
                                                        <td>{{ file.created_at|date:"Y-m-d H:i" }}</td>
                                                        <td>
                                                            <a href="{{ file.file.url }}" class="btn btn-sm btn-outline-primary mr-1" target="_blank">
                                                                <i class="fas fa-download"></i>
                                                            </a>
                                                            {% if can_edit %}
                                                            <button type="button" class="btn btn-sm btn-outline-danger delete-file" data-file-id="{{ file.id }}">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                        {% else %}
                                        <div class="alert alert-light text-center">
                                            <i class="fas fa-info-circle mr-2"></i>추가 파일이 없습니다. 위의 "파일 추가" 버튼을 클릭하여 파일을 추가하세요.
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        <br>

                        <!-- Form Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" name="save_and_exit" class="btn btn-primary">
                                    저장 후 나가기
                                </button>
                                <button type="submit" name="save_and_new" class="btn btn-secondary">
                                    저장 후 새로 작성
                                </button>

                                <a href="{% url 'marketing_visit_list' %}" class="btn btn-secondary">
                                    <i class="fas fa-times mr-2"></i>취소
                                </a>

                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Location Create Modal -->
<div class="modal fade" id="locationModal" tabindex="-1" role="dialog" aria-labelledby="locationModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <!-- Modal content will be loaded via AJAX -->
        </div>
    </div>
</div>

<!-- Add File Modal -->
<div class="modal fade" id="addFileModal" tabindex="-1" role="dialog" aria-labelledby="addFileModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addFileModalLabel">파일 추가</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="fileUploadForm" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="file">파일 선택</label>
                        <input type="file" class="form-control" id="file" name="file" required>
                        <small class="form-text text-muted">최대 10MB, 허용 형식: PDF, DOC, DOCX, XLS, XLSX, JPG, JPEG, PNG</small>
                    </div>
                    <div class="form-group">
                        <label for="description">파일 설명</label>
                        <input type="text" class="form-control" id="description" name="description" placeholder="파일에 대한 간단한 설명을 입력해주세요">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">취소</button>
                <button type="button" class="btn btn-primary" id="uploadFileBtn">업로드</button>
            </div>
        </div>
    </div>
</div>
{% endblock content %} {% block javascripts %}
<script>
    $(document).ready(function() {
        // Initialize Select2 for location search
        $('#location_search').select2({
            theme: 'bootstrap4',
            placeholder: '장소를 검색하세요...',
            ajax: {
                url: '/crm/location/autocomplete/',
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        q: params.term
                    };
                },
                processResults: function(data) {
                    if (data.results.length === 0) {
                        data.results.push({
                            id: 'create_new',
                            text: '➕ 새 장소 등록하기',
                            create: true
                        });
                    }
                    return {
                        results: data.results
                    };
                },
                cache: true
            }
        });
        // Handle location selection
        $('#location_search').on('select2:select', function(e) {
            const data = e.params.data;
            if (data.id === 'create_new') {
                $.get('/crm/marketing_visit/location/create/', function(html) {
                    $('#locationModal .modal-content').html(html);
                    $('#locationModal').modal('show');
                });
                $(this).val(null).trigger('change');
                return;
            }
            // Update hidden field and show selected location
            $('#id_location_name').val(data.id);
            $('#selectedLocationDisplay .location-name').text(data.text);
            $('#selectedLocationDisplay').addClass('show');
        });

        // File upload functionality
        const uploadBtn = document.getElementById('uploadFileBtn');
        const fileForm = document.getElementById('fileUploadForm');

        if (uploadBtn && fileForm) {
            uploadBtn.addEventListener('click', function() {
                const formData = new FormData(fileForm);

                // Show loading state
                uploadBtn.disabled = true;
                uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>업로드 중...';

                fetch('/crm/marketing-visit/{{ form.instance.id }}/add-file/', {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Reset form
                        fileForm.reset();

                        // Close modal
                        $('#addFileModal').modal('hide');

                        // Update file list
                        const filesContainer = document.getElementById('files-container');
                        const noFilesAlert = filesContainer.querySelector('.alert');

                        if (noFilesAlert) {
                            // Replace the "no files" alert with a file table
                            filesContainer.innerHTML = `
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="thead-light">
                                            <tr>
                                                <th>파일명</th>
                                                <th>설명</th>
                                                <th>업로드 일시</th>
                                                <th>작업</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr id="file-${data.file.id}">
                                                <td>
                                                    <i class="fas fa-file mr-2 text-primary"></i>
                                                    ${data.file.name}
                                                </td>
                                                <td>${data.file.description}</td>
                                                <td>${data.file.created_at}</td>
                                                <td>
                                                    <a href="/media/marketing_visits/${data.file.id}/files/${data.file.name}" class="btn btn-sm btn-outline-primary mr-1" target="_blank">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                    {% if can_edit %}
                                                    <button type="button" class="btn btn-sm btn-outline-danger delete-file" data-file-id="${data.file.id}">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            `;
                        } else {
                            // Add to existing file table
                            const fileTableBody = filesContainer.querySelector('tbody');
                            if (fileTableBody) {
                                const newRow = document.createElement('tr');
                                newRow.id = `file-${data.file.id}`;
                                newRow.innerHTML = `
                                    <td>
                                        <i class="fas fa-file mr-2 text-primary"></i>
                                        ${data.file.name}
                                    </td>
                                    <td>${data.file.description}</td>
                                    <td>${data.file.created_at}</td>
                                    <td>
                                        <a href="/media/marketing_visits/${data.file.id}/files/${data.file.name}" class="btn btn-sm btn-outline-primary mr-1" target="_blank">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        {% if can_edit %}
                                        <button type="button" class="btn btn-sm btn-outline-danger delete-file" data-file-id="${data.file.id}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </td>
                                `;
                                fileTableBody.prepend(newRow);

                                // Add event listener to the new delete button
                                const newDeleteBtn = newRow.querySelector('.delete-file');
                                newDeleteBtn.addEventListener('click', function() {
                                    const fileId = this.getAttribute('data-file-id');
                                    if (confirm('이 파일을 삭제하시겠습니까?')) {
                                        deleteFile(fileId);
                                    }
                                });
                            }
                        }
                    } else {
                        // Show error message
                        alert(data.message || '파일 업로드 중 오류가 발생했습니다.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('파일 업로드 중 오류가 발생했습니다.');
                })
                .finally(() => {
                    // Reset button state
                    uploadBtn.disabled = false;
                    uploadBtn.innerHTML = '업로드';
                });
            });
        }

        // File delete functionality
        const deleteButtons = document.querySelectorAll('.delete-file');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const fileId = this.getAttribute('data-file-id');
                if (confirm('이 파일을 삭제하시겠습니까?')) {
                    deleteFile(fileId);
                }
            });
        });

        function deleteFile(fileId) {
            fetch(`/crm/marketing-visit/file/${fileId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    const fileRow = document.getElementById(`file-${fileId}`);
                    if (fileRow) {
                        fileRow.remove();
                    }

                    // Check if there are no more files
                    const filesContainer = document.getElementById('files-container');
                    const fileRows = filesContainer.querySelectorAll('tbody tr');
                    if (fileRows.length === 0) {
                        filesContainer.innerHTML = `
                            <div class="alert alert-light text-center">
                                <i class="fas fa-info-circle mr-2"></i>추가 파일이 없습니다. 위의 "파일 추가" 버튼을 클릭하여 파일을 추가하세요.
                            </div>
                        `;
                    }
                } else {
                    alert(data.message || '파일 삭제 중 오류가 발생했습니다.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('파일 삭제 중 오류가 발생했습니다.');
            });
        }

        // Helper function to get CSRF token from cookies
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        // Handle location removal
        $('.remove-location').click(function() {
            $('#location_search').val(null).trigger('change');
            $('#id_location_name').val('');
            $('#selectedLocationDisplay').removeClass('show');
        });
        // After modal form submit success
        $(document).on('submit', '#location-form', function(e) {
            e.preventDefault();
            var form = $(this);
            var submitBtn = $('#save-location');
            // Disable button and show loading state
            submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>저장 중...');
            // Try the popup endpoint first
            $.ajax({
                url: form.attr('action'),
                method: 'POST',
                data: form.serialize(),
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        handleLocationSuccess(response);
                    } else {
                        // If popup endpoint fails, try the API endpoint
                        $.ajax({
                            url: '/crm/api/locations/create/',
                            method: 'POST',
                            data: form.serialize(),
                            success: function(apiResponse) {
                                if (apiResponse.status === 'success') {
                                    handleLocationSuccess(apiResponse);
                                } else {
                                    alert('장소 등록 중 오류가 발생했습니다.');
                                }
                            },
                            error: function() {
                                alert('장소 등록 중 오류가 발생했습니다.');
                            }
                        });
                    }
                },
                error: function() {
                    // If popup endpoint fails, try the API endpoint
                    $.ajax({
                        url: '/crm/api/locations/create/',
                        method: 'POST',
                        data: form.serialize(),
                        success: function(apiResponse) {
                            if (apiResponse.status === 'success') {
                                handleLocationSuccess(apiResponse);
                            } else {
                                alert('장소 등록 중 오류가 발생했습니다.');
                            }
                        },
                        error: function() {
                            alert('장소 등록 중 오류가 발생했습니다.');
                        }
                    });
                },
                complete: function() {
                    submitBtn.prop('disabled', false).html(
                        '<i class="fas fa-save mr-2"></i>저장');
                }
            });
        });
        // Helper function to handle successful location creation
        function handleLocationSuccess(response) {
            // Add to Select2 and select it
            let newOption = new Option(response.text, response.id, true, true);
            $('#location_search').append(newOption).trigger('change');
            $('#id_location_name').val(response.id);
            $('#selectedLocationDisplay .location-name').text(response.text);
            $('#selectedLocationDisplay').addClass('show');
            $('#locationModal').modal('hide');
        }
    });
</script>
{% endblock javascripts %}
