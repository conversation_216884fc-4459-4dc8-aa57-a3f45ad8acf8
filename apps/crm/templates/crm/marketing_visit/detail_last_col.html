<div class="card-body h-100 overflow-auto">
    <!-- Visit Details Section -->
    <div class="visit-details mb-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4 class="mb-0">
                <i class="fas fa-info-circle text-primary mr-2"></i>방문 상세 정보
            </h4>


            {% if request.user.is_superuser %}

            <div>
                <a href="{% url 'marketing_visit_update' selected_visit.id %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-edit"></i> 수정
                </a>
            </div>

            {% endif %}



        </div>

        <div class="detail-item mb-3">
            <h6 class="text-muted mb-2">방문 정보</h6>

            <!-- Visit Information Table -->
            <div class="table-responsive mb-4 bg-white card">
                <table class="table table-borderless mb-0" style="font-size: 14px;">
                    <tbody>
                        <tr>
                            <th class="font-weight-bold" style="width: 30%;">병원명</th>
                            <td style="width: 70%; font-weight: 500;">{{ selected_visit.location_name.hospital_name }}</td>

                            <th class="font-weight-bold">방문 일자</th>
                            <td style="font-weight: 500;">{{ selected_visit.visit_date|date:"Y/m/d" }}</td>
                        </tr>
                        <tr>
                            <th class="font-weight-bold">면담 대상자</th>
                            <td style="font-weight: 500;">{{ selected_visit.person_met }}</td>

                            <th class="font-weight-bold">자료 제공</th>
                            <td style="font-weight: 500;">{{ selected_visit.files_provided }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>

        </div>



        <div class="card shadow-sm mb-3">
    <div class="card-header bg-white border-bottom py-3 px-4">
        <h5 class="mb-2 text-primary">
            📝 제목: <strong class="text-dark">{{ selected_visit.main_topic }}</strong>
        </h5>
        <div class="d-flex flex-wrap small text-muted">

            {% if selected_visit.marketing_manager %}
            <strong class="text-primary me-3">작성자: </strong> &nbsp;&nbsp; <strong class="text-info" >{{ selected_visit.marketing_manager }} </strong>
            {% endif %}
            &nbsp;&nbsp;&nbsp;

            <span class="me-3">작성일: {{ selected_visit.created_at|date:"Y.m.d" }}</span>

            {% if selected_visit.edit_history  %}
                &nbsp;        <span class="me-3 text-danger">마지막  수정일: {{ selected_visit.updated_at|date:"Y.m.d" }}</span>
            {% endif %}



        </div>
    </div>

    <div class="card-body px-4 py-3" style="min-height: 220px; max-height: 400px; overflow-y: auto; border-top: 1px solid #e5e5e5;">
        <div class="small text-body" style="line-height: 1.6;">
            {{ selected_visit.discussion_details|safe }}
        </div>
    </div>
</div>
        {% if selected_visit.attachment %}
        <div class="detail-item mb-3">
            <h6 class="text-muted mb-2">첨부 파일</h6>
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-file mr-2"></i>
                        <div>
                            <p class="mb-1">{{ selected_visit.attachment_description }}</p>
                            <a href="{{ selected_visit.attachment.url }}" class="btn btn-sm btn-outline-primary"
                                target="_blank">
                                <i class="fas fa-download mr-1"></i>다운로드
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Additional Files Section (Read-only) -->
        <div class="detail-item">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="text-muted mb-0">추가 파일 목록</h6>
                {% if selected_visit.marketing_manager == request.user.user_employee or request.user.is_superuser %}
                <a href="{% url 'marketing_visit_update' selected_visit.id %}" class="btn btn-sm btn-outline-info">
                    <i class="fas fa-edit mr-1"></i>수정 페이지에서 파일 관리
                </a>
                {% endif %}
            </div>

            <div id="files-container">
                {% if files %}
                <div class="card">
                    <div class="card-body p-0">
                        <ul class="list-group list-group-flush">
                            {% for file in files %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file mr-2 text-primary"></i>
                                    <div>
                                        <p class="mb-1 font-weight-bold">{{ file.get_file_name }}</p>
                                        <small class="text-muted">{{ file.description }}</small>
                                        <small class="d-block text-muted">{{ file.created_at|date:"Y-m-d H:i" }}</small>
                                    </div>
                                </div>
                                <div>
                                    <a href="{{ file.file.url }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </div>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% else %}
                <div class="alert alert-light text-center">
                    <i class="fas fa-info-circle mr-2"></i>추가 파일이 없습니다.
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Comments Section -->
    {% include 'crm/marketing_visit/comments_with_scrollable.html' %}





</div>

<!-- Add this where you want to show the edit history -->
{% if selected_visit.edit_history %}
<div class="card mt-2">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-history"></i> 수정 기록
        </h5>
    </div>


    <div class="card-body">

            {% for edit in selected_visit.edit_history reversed %}
                    <strong class="text-success"> {{ edit.editor_name }} </strong>    -<small class="text-danger"></small> {{ edit.edited_at }}</small>,&nbsp;&nbsp;&nbsp;

            {% endfor %}

    </div>
</div>
{% endif %}


     <script>
            document.addEventListener('DOMContentLoaded', function() {
                const middleColumn = document.getElementById('middle-column');
                const rightColumn = document.getElementById('right-column');
                const toggleButton = document.getElementById('toggle-middle-column');
                const toggleIcon = toggleButton?.querySelector('i');
                let isCollapsed = false;  // Start expanded by default

                // Function to handle collapse/expand
                function toggleColumn(collapse) {
                    if (collapse) {
                        middleColumn.classList.add('collapsed');
                        rightColumn.classList.add('expanded');
                        toggleIcon.className = 'fas fa-chevron-right';
                        toggleButton.classList.add('collapsed');
                        toggleButton.setAttribute('title', '병원 정보 보이기');
                    } else {
                        middleColumn.classList.remove('collapsed');
                        rightColumn.classList.remove('expanded');
                        toggleIcon.className = 'fas fa-chevron-left';
                        toggleButton.classList.remove('collapsed');
                        toggleButton.setAttribute('title', '병원 정보 숨기기');
                    }
                }

                if (toggleButton) {
                    toggleButton.addEventListener('click', function() {
                        isCollapsed = !isCollapsed;
                        toggleColumn(isCollapsed);
                    });
                }
            });
        </script>
