{% extends "layouts/base.html" %}
{% load static %}
{% block extra_css %}
<style>
    /* Enhanced table styles */
    .table-excel {
        border-collapse: collapse;
        width: 100%;
    }
    
    .table-excel thead th {
        background-color: #f8f9fa;
        position: sticky;
        top: 0;
        z-index: 10;
        border-bottom: 2px solid #dee2e6;
        padding: 12px 8px;
        font-weight: 600;
        color: #495057;
    }
    
    .table-excel tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }
    
    .table-excel tbody td {
        padding: 10px 8px;
        vertical-align: middle;
        border-bottom: 1px solid #e9ecef;
    }
    
    /* Sortable headers */
    .sortable {
        cursor: pointer;
        position: relative;
        padding-right: 18px !important;
    }
    
    .sortable:hover {
        background-color: #e9ecef;
    }
    
    .sortable::after {
        content: "\f0dc";
        font-family: "Font Awesome 5 Free";
        font-weight: 900;
        position: absolute;
        right: 5px;
        color: #adb5bd;
    }
    
    .sortable.asc::after {
        content: "\f0de";
        color: #007bff;
    }
    
    .sortable.desc::after {
        content: "\f0dd";
        color: #007bff;
    }
    
    /* Filter styles */
    .search-filters {
        margin-bottom: 1.5rem;
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .search-filters .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 0.75rem 1.25rem;
    }
    
    .search-filters .form-control,
    .search-filters .form-select {
        border-radius: 0.25rem;
        border: 1px solid #ced4da;
        padding: 0.375rem 0.75rem;
        height: calc(1.5em + 0.75rem + 2px);
        font-size: 0.875rem;
    }
    
    .search-filters .form-control:focus,
    .search-filters .form-select:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .search-filters .form-group {
        margin-bottom: 1rem;
    }
    
    .search-filters .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        color: #495057;
    }
    
    .search-filters .form-check-input {
        margin-top: 0.25rem;
    }
    
    .search-filters .form-check-label {
        margin-left: 0.25rem;
    }
    
    .search-filters .input-group {
        flex-wrap: nowrap;
    }
    
    .search-filters .input-group .btn {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
    
    /* Active filters */
    .filter-badge {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        padding: 0.5rem 0.75rem;
        font-weight: normal;
        font-size: 0.875rem;
    }
    
    .filter-badge a {
        margin-left: 0.5rem;
        color: white;
        text-decoration: none;
    }
    
    /* Person info */
    .person-name {
        font-weight: 500;
        color: #212529;
    }
    
    .person-designation {
        color: #6c757d;
        display: block;
        font-size: 0.875rem;
    }
    
    /* Action buttons */
    .action-buttons .btn {
        margin-right: 0.25rem;
    }
    
    /* Clickable cells */
    .clickable-cell {
        cursor: pointer;
    }
    
    .clickable-cell:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }
    
    /* Modal styles */
    .modal-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
    
    /* Pagination styles */
    .pagination {
        margin-bottom: 0;
    }
    
    .page-item.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
    }
    
    .page-link {
        color: #007bff;
    }
    
    .page-link:hover {
        color: #0056b3;
        background-color: #e9ecef;
    }
    
    /* Form row styling */
    .form-row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -5px;
        margin-left: -5px;
    }
    
    .form-row > .col,
    .form-row > [class*="col-"] {
        padding-right: 5px;
        padding-left: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Page Header -->
    {% include "crm/marketing_visit/marketing_visit_header.html" %}

    <!-- Search and Filters -->
    <div class="card search-filters mb-3">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-filter"></i> 필터 및 검색</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                {% csrf_token %}
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="{{ filter.form.search.id_for_label }}" class="form-label">
                            <i class="fas fa-search"></i> 검색
                        </label>
                        <div class="input-group">
                            {{ filter.form.search }}
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <small class="form-text text-muted">방문장소, 면담자, 주요 논의 검색</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="{{ filter.form.date_range.id_for_label }}" class="form-label">
                            <i class="fas fa-calendar-alt"></i> 방문일자 이후
                        </label>
                        {{ filter.form.date_range }}
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="{{ filter.form.end_date.id_for_label }}" class="form-label">
                            <i class="fas fa-calendar-alt"></i> 방문일자 이전
                        </label>
                        {{ filter.form.end_date }}
                    </div>
                </div>

                {%  if request.user.is_superuser or request.user.user_employee.team in "marketing" %}
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="{{ filter.form.marketing_manager.id_for_label }}" class="form-label">
                            <i class="fas fa-user"></i> 담당자
                        </label>
                        {{ filter.form.marketing_manager }}
                    </div>
                </div>
                {% endif %}
                
                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-paperclip"></i> 첨부파일
                        </label>
                        <div class="form-check mt-2">
                            {{ filter.form.has_attachment }}
                            <label class="form-check-label" for="{{ filter.form.has_attachment.id_for_label }}">
                                첨부파일 있음
                            </label>
                        </div>
                    </div>
                </div>

                <div class="col-md-1">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> 필터
                    </button>
                    <a href="{% url 'marketing_visit_list' %}" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-times"></i> 초기화
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Active Filters Display -->
    {% if filter.form.search.value or filter.form.date_range.value or filter.form.end_date.value or filter.form.marketing_manager.value or filter.form.has_attachment.value %}
    <div class="card mb-3">
        <div class="card-body py-2">
            <div class="d-flex flex-wrap align-items-center">
                <span class="me-2 fw-bold"><i class="fas fa-tag"></i> 활성 필터:</span>
                {% if filter.form.search.value %}
                <span class="badge bg-info filter-badge">
                    <i class="fas fa-search"></i> 검색: {{ filter.form.search.value }}
                    <a href="?{% for key, value in request.GET.items %}{% if key != 'search' %}{{ key }}={{ value }}&{% endif %}{% endfor %}" class="text-white">
                        <i class="fas fa-times-circle"></i>
                    </a>
                </span> &nbsp;
                {% endif %}
                
                {% if filter.form.date_range.value %}
                <span class="badge bg-danger filter-badge">
                    <i class="fas fa-calendar-alt"></i> 방문일자 이후: {{ filter.form.date_range.value  }}
                    <a href="?{% for key, value in request.GET.items %}{% if key != 'date_range' %}{{ key }}={{ value }}&{% endif %}{% endfor %}" class="text-white">
                        <i class="fas fa-times-circle"></i>
                    </a>
                </span> &nbsp;
                {% endif %}
                
                {% if filter.form.end_date.value %}
                <span class="badge bg-danger filter-badge">
                    <i class="fas fa-calendar-alt"></i> 방문일자 이전: {{ filter.form.end_date.value  }}
                    <a href="?{% for key, value in request.GET.items %}{% if key != 'end_date' %}{{ key }}={{ value }}&{% endif %}{% endfor %}" class="text-white">
                        <i class="fas fa-times-circle"></i>
                    </a>
                </span> &nbsp;
                {% endif %}
                
                {% if filter.form.marketing_manager.value %}
                <span class="badge bg-info filter-badge">
                    <i class="fas fa-user"></i> 담당자: {{ filter.form.marketing_manager.value }}
                    <a href="?{% for key, value in request.GET.items %}{% if key != 'marketing_manager' %}{{ key }}={{ value }}&{% endif %}{% endfor %}" class="text-white">
                        <i class="fas fa-times-circle"></i>
                    </a>
                </span> &nbsp;
                {% endif %}
                
                {% if filter.form.has_attachment.value %}
                <span class="badge bg-warning filter-badge">
                    <i class="fas fa-paperclip"></i> 첨부파일 있음
                    <a href="?{% for key, value in request.GET.items %}{% if key != 'has_attachment' %}{{ key }}={{ value }}&{% endif %}{% endfor %}" class="text-white">
                        <i class="fas fa-times-circle"></i>
                    </a>
                </span>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Main Content -->
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table align-items-center table-flush table-excel">
                    <thead>
                        <tr>
                            <th class="sortable {% if sort_by == 'id' %}asc{% elif sort_by == '-id' %}desc{% endif %}" 
                                data-sort="id">
                                <i class="fas fa-hashtag"></i> ID
                            </th>
                            <th>
                                <i class="fas fa-user-tie"></i> 방문자 매니저
                            </th>
                            <th class="sortable {% if sort_by == 'visit_date' %}asc{% elif sort_by == '-visit_date' %}desc{% endif %}" 
                                data-sort="visit_date">
                                <i class="fas fa-calendar-day"></i> 방문일자
                            </th>
                            <th class="sortable {% if sort_by == 'location_name__hospital_name' %}asc{% elif sort_by == '-location_name__hospital_name' %}desc{% endif %}" 
                                data-sort="location_name__hospital_name">
                                <i class="fas fa-map-marker-alt"></i> 방문장소
                            </th>
                            <th class="sortable {% if sort_by == 'person_met' %}asc{% elif sort_by == '-person_met' %}desc{% endif %}" 
                                data-sort="person_met">
                                <i class="fas fa-user-md"></i> 면담자
                            </th>
                            <th>
                                <i class="fas fa-comments"></i> 주요 논의
                            </th>
                            <th>
                                <i class="fas fa-file-alt"></i> 자료 제공
                            </th>
                            <th class="sortable {% if sort_by == 'created_at' %}asc{% elif sort_by == '-created_at' %}desc{% endif %}" 
                                data-sort="created_at">
                                <i class="fas fa-clock"></i> 작성일시
                            </th>
                            <th>
                                <i class="fas fa-edit"></i> 수정
                            </th>
                            <th>
                                <i class="fas fa-paperclip"></i> 첨부파일
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for visit in visits %}
                        <tr>
                            <td>
                                <a href="{% url 'marketing_visit_popup_detail' visit.id %}" target="_blank" class="text-primary">
                                    {{ visit.id }}
                                </a>
                            </td>
                            <td class="clickable-cell"
                                onclick="window.location.href='{% url 'marketing_visit_list_detail' %}?person={{ visit.marketing_manager.id }}'; return false;">
                                <div class="person-name">{{ visit.marketing_manager.full_name }}</div>
                            </td>
                            <td>
                                <span class="font-weight-bold">{{ visit.visit_date|date:"Y-m-d" }}</span>
                            </td>
                            <td class="font-weight-bold clickable-cell"
                                onclick="window.location.href='{% url 'marketing_visit_list_detail' %}?place={{ visit.location_name.id }}&selected_visit={{ visit.pk }}'; return false;">
                                <span class="text-muted">[ {{ visit.location_name.jedan }} ]</span>
                                <strong>{{ visit.location_name.hospital_name }}</strong>
                            </td>
                            <td>
                                <div class="person-name">{{ visit.person_met|slice:30 }}</div>
                                <small class="person-designation">{{ visit.person_designation }}</small>
                            </td>
                            <td class="clickable-cell">
                                <div style="display: flex; flex-direction: row; align-items: center; gap: 8px;">
                                    <button type="button" class="btn btn-sm btn-outline-primary show-popup-detail"
                                        data-visit-id="{{ visit.pk }}" title="Quick View">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <a href="{% url 'marketing_visit_list_detail' %}?selected_visit={{ visit.pk }}"
                                        class="main-topic-link" style="text-decoration: underline; color: #0d6efd;">
                                        {{ visit.main_topic|truncatechars:100 }}
                                    </a>
                                </div>
                            </td>
                            <td>
                                {{ visit.files_provided|slice:30 }}
                            </td>
                            <td>{{ visit.created_at|date:"Y-m-d H:i" }}</td>
                            <td>
                                <a href="{% url 'marketing_visit_update' visit.pk %}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                            <td>
                                {% if visit.attachment %}
                                <a href="{{ visit.attachment.url }}" class="btn btn-sm btn-light">
                                    <i class="fas fa-paperclip"></i>
                                </a>
                                {% else %}
                                없음
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="10" class="text-center py-5">
                                <div class="text-muted">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <p>등록된 방문 기록이 없습니다. 새로운 방문 기록을 추가해주세요.</p>
                                    <a href="{% url 'marketing_visit_create' %}" class="btn btn-primary mt-3">
                                        <i class="fas fa-plus"></i> 새 방문 기록 추가
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    {% include 'crm/marketing_visit/pagination.html'  %}

    <!-- Add this at the end of the body but before any scripts -->
    <div class="modal fade" id="visitDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">방문 상세 정보</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="visitDetailContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

</div>

<script src="/static/assets/vendor/jquery/dist/jquery.min.js"></script>
<script src="/static/assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="/static/assets/vendor/js-cookie/js.cookie.js"></script>
<script src="/static/assets/vendor/jquery.scrollbar/jquery.scrollbar.min.js"></script>
<script src="/static/assets/vendor/jquery-scroll-lock/dist/jquery-scrollLock.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const modal = new bootstrap.Modal(document.getElementById('visitDetailModal'));
        document.querySelectorAll('.show-popup-detail').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const visitId = this.getAttribute('data-visit-id');
                // Add CSRF token to headers
                const csrftoken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                fetch(`/crm/marketing_visit/popup-detail/${visitId}/`, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRFToken': csrftoken
                        },
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.text();
                    })
                    .then(html => {
                        document.getElementById('visitDetailContent').innerHTML = html;
                        modal.show();
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to load visit details. Please try again.');
                    });
            });
        });

        // Enhanced sorting functionality
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', function() {
                const sortField = this.getAttribute('data-sort');
                let currentSort = '{{ sort_by }}';
                let newSort;
                
                if (currentSort === sortField) {
                    // Currently sorted ascending, switch to descending
                    newSort = '-' + sortField;
                } else if (currentSort === '-' + sortField) {
                    // Currently sorted descending, remove sorting
                    newSort = '';
                } else {
                    // Not currently sorted by this field, default to ascending
                    newSort = sortField;
                }
                
                // Get current URL and parameters
                const url = new URL(window.location.href);
                
                if (newSort) {
                    url.searchParams.set('sort', newSort);
                } else {
                    url.searchParams.delete('sort');
                }
                
                // Preserve other parameters
                const searchParams = new URLSearchParams(window.location.search);
                for (const [key, value] of searchParams.entries()) {
                    if (key !== 'sort' && key !== 'page') {
                        url.searchParams.set(key, value);
                    }
                }
                
                // Navigate to the new URL
                window.location.href = url.toString();
            });
        });
    });
</script>

{% endblock %}
