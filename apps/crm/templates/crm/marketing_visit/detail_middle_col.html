<style>
    tr {
        border-bottom: 1px solid #dee2e6;
    }
</style>

{% load hospital_stats %}

<!-- Middle Column - Hospital Info -->

    <div class="card shadow-sm h-100 border-primary border-opacity-25">
        <div class="card-header d-flex justify-content-between align-items-center p-2 bg-info">
            <h4 class="mb-0">병원 정보 감추기 </h4>
            <button id="toggle-middle-column" class="btn btn-sm btn-link text-primary" title="병원 정보 숨기기">
                <i class="fas fa-chevron-left"></i>
            </button>
        </div>

        <div class="card shadow-sm">
            <div class="card-body h-100 overflow-auto">
                <div style="display: flex; justify-content: center; margin: 10px 0;">
                    <div
                        style="width: 100%; max-width: 800px; border: 1px solid #ccc; border-radius: 10px; padding: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); font-family: '맑은 고딕', sans-serif;">
        
                        <!-- Flex container for header and edit button -->
                        <div
                            style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; margin-bottom: 20px;">
                            <h2 style="margin: 0; font-size: 24px; flex: 1 1 auto;">🏥 병원 정보</h2>
        
                            <a href="{% url 'edit_hospital' selected_visit.location_name.id %}"
                                class="btn btn-outline-primary btn-lg"
                                style="font-weight: bold; white-space: nowrap; margin-left: 10px;">
                                <i class="fas fa-edit"></i> 수정
                            </a>
                        </div>

<table style="width: 100%; border-collapse: collapse; font-size: 14px;">
    <tbody>
        <tr>
            <th style="text-align: left; padding: 8px;">의료재단</th>
            <td style="padding: 8px;">{{ selected_visit.location_name.jedan }}</td>
        </tr>
        <tr>
            <th style="text-align: left; padding: 8px;">지점명 / 지점장 </th>
            <td style="padding: 8px;">{{ selected_visit.location_name.jedan_branch }} <br> {{ selected_visit.location_name.jedan_branch.key_person_info }} </td>
        </tr>
        <tr>
            <th style="text-align: left; padding: 8px;">병원명</th>



            <td style="padding: 8px;">
                <div style="display: flex; align-items: center; gap: 12px;">
                    <a href="{% url 'hospital_report_detail' selected_visit.location_name.id %}"
                        style="font-size: 16px; font-weight: bold; color: #2a4d9b; text-decoration: none;">
                        <i class="fas fa-eye"></i> <i class="fas fa-eye"></i>  {{ selected_visit.location_name.hospital_name }}
                    </a>

                    <!-- <a href="{% url 'edit_hospital' selected_visit.location_name.id %}"
                        class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-edit"></i> 수정
                    </a> -->
                </div>
            </td>




        </tr>

        <tr>
            <th style="text-align: left; padding: 8px;">전화번호</th>
            <td style="padding: 8px;">{{ selected_visit.location_name.phone_number }}</td>
        </tr>
        <tr>
            <th style="text-align: left; padding: 8px;">주소 (한글)</th>
            <td style="padding: 8px;">
                {{ selected_visit.location_name.do_address }}
                {{ selected_visit.location_name.si_address }}
                {{ selected_visit.location_name.last_address }}
            </td>
        </tr>
        <tr>
            <th style="text-align: left; padding: 8px;">사업자 번호 </th>
            <td style="padding: 8px;">{{ selected_visit.location_name.pan_number }}</td>
        </tr>

        <!-- <tr>
            <th style="text-align: left; padding: 8px;"> 전화 번호 </th>
            <td style="padding: 8px;">{{ selected_visit.location_name.phone_number }}</td>
        </tr>

        <tr>
            <th style="text-align: left; padding: 8px;">사업자 번호</th>
            <td style="padding: 8px;">{{ selected_visit.location_name.pan_number|default:"-" }}
            </td>
        </tr> -->
        <tr>
            <th style="text-align: left; padding: 8px;"> CNV 추가 보고</th>
            <td style="padding: 8px;">
                {{ selected_visit.location_name.extra_report|yesno:"허용,거부" }}</td>
        </tr>

        <tr>
            <th style="text-align: left; padding: 8px;"> KEYMAN 정보</th>
            <td style="padding: 8px;">{{ selected_visit.location_name.key_person_info }}
            </td>
        </tr>



        <tr>
            <th style="text-align: left; padding: 8px;">등록 정보</th>
            <td style="padding: 8px;">
                {{ selected_visit.location_name.added_by|default:"-" }} <br>
                {{ selected_visit.location_name.created_at|date:"Y-m-d H:i" }}</td>
        </tr>
        <tr>
            <th style="text-align: left; padding: 8px;">최종수정정보</th>
            <td style="padding: 8px;">
                {{ selected_visit.location_name.last_edited_by|default:"-" }} <br>
                {{selected_visit.location_name.last_edited|date:"Y-m-d H:i" }}</td>
        </tr>


    </tbody>
</table>
        
                       
              
            </div>
        </div>






                 
              
                <!-- Service Statistics Section -->
                <div class="mb-4">
                    {% if selected_visit.location_name.is_hospital %}
                    <h5 class="text-primary border-bottom pb-2 mb-3">
                        <i class="fas fa-chart-bar mr-2"></i> 서비스 정보 (지난달)  <strong class="text-info">  (NIPT= 제노맘, GBN = 제노베넷 , GFN = 제노파인드 , PGT= 지노브로  )</strong>
                    </h5>

                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" style="font-size: 14px;">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 25%;">서비스명</th>
                                    <th style="width: 15%;">NIPT</th>
                                    <th style="width: 15%;">GFN</th>
                                    <th style="width: 15%;">GBN</th>
                                    <th style="width: 15%;">PGT</th>
                                    <th style="width: 15%;">ORA</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <th>지난달 건수</th>
                                    {% get_previous_month_stats selected_visit.location_name.id as stats %}
                                    <td>{{ stats.nipt|default:"0" }}</td>
                                    <td>{{ stats.genofind|default:"0" }}</td>
                                    <td>{{ stats.genobenet|default:"0" }}</td>
                                    <td>{{ stats.pgt|default:"0" }}</td>
                                    <td>{{ stats.ora|default:"0" }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Doctor Statistics Section -->
                    <h5 class="text-primary border-bottom pb-2 mb-3 mt-4">
                        <i class="fas fa-user-md mr-2"></i> 의료진별 서비스 현황 (지난달)
                    </h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" style="font-size: 14px;">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 25%;">의료진</th>
                                    <th style="width: 15%;">NIPT</th>
                                    <th style="width: 15%;">GFN</th>
                                    <th style="width: 15%;">GBN</th>
                                    <th style="width: 15%;">PGT</th>
                                    <th style="width: 15%;">ORA</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% get_doctor_service_stats selected_visit.location_name.id as doctor_stats %}
                                {% for dept, doctors in doctor_stats.items %}
                                    {% for doctor in doctors %}
                                    <tr>
                                        <td>{{ doctor.name }}</td>
                                        <td>{{ doctor.nipt_count|default:"0" }}</td>
                                        <td>{{ doctor.genofind_count|default:"0" }}</td>
                                        <td>{{ doctor.genobenet_count|default:"0" }}</td>
                                        <td>{{ doctor.pgt_count|default:"0" }}</td>
                                        <td>{{ doctor.ora_count|default:"0" }}</td>
                                    </tr>
                                    {% endfor %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Total Statistics Section -->
                    <h5 class="text-primary border-bottom pb-2 mb-3 mt-4">
                        <i class="fas fa-users mr-2"></i> 의료진별 총 건수
                    </h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" style="font-size: 14px;">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 25%;">의료진</th>
                                    <th style="width: 15%;">NIPT</th>
                                    <th style="width: 15%;">GFN</th>
                                    <th style="width: 15%;">GBN</th>
                                    <th style="width: 15%;">PGT</th>
                                    <th style="width: 15%;">ORA</th>
                                    <th style="width: 15%;">총계</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% get_doctor_total_stats selected_visit.location_name.id as doctor_stats %}
                                {% for doctor in doctor_stats %}
                                <tr>
                                    <td>{{ doctor.name }}</td>
                                    <td>{{ doctor.nipt_count|default:"0" }}</td>
                                    <td>{{ doctor.genofind_count|default:"0" }}</td>
                                    <td>{{ doctor.genobenet_count|default:"0" }}</td>
                                    <td>{{ doctor.pgt_count|default:"0" }}</td>
                                    <td>{{ doctor.ora_count|default:"0" }}</td>
                                    <td><strong>{{ doctor.total }}</strong></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Price Information Section -->
                    <h5 class="text-primary border-bottom pb-2 mb-3 mt-4">
                        <i class="fas fa-tags mr-2"></i> 서비스 가격 정보
                    </h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" style="font-size: 14px;">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 20%;">서비스</th>
                                    <th style="width: 20%;">Lite</th>
                                    <th style="width: 20%;">Standard</th>
                                    <th style="width: 20%;">Plus</th>
                                    <th style="width: 20%;">Twin</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <th>NIPT</th>
                                    <td>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>₩{{ selected_visit.location_name.price_lite|floatformat:0 }}</span>
                                            <small class="text-muted">{{ selected_visit.location_name.code_lite|default:"-" }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>₩{{ selected_visit.location_name.price_std|floatformat:0 }}</span>
                                            <small class="text-muted">{{ selected_visit.location_name.code_std|default:"-" }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>₩{{ selected_visit.location_name.price_plus|floatformat:0 }}</span>
                                            <small class="text-muted">{{ selected_visit.location_name.code_plus|default:"-" }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>₩{{ selected_visit.location_name.price_twin|floatformat:0 }}</span>
                                            <small class="text-muted">{{ selected_visit.location_name.code_twin|default:"-" }}</small>
                                        </div>
                                    </td>
                                </tr>
                                <!-- Add more service rows here as needed -->
                            </tbody>
                        </table>
                    </div>

                {% else %}
                <div class="alert alert-warning" role="alert">
                    <strong>⚠️ 이 기관은 병원으로 설정된 기관이 아닙니다.</strong><br>
                    실제로 병원이라면 수정 부탁드립니다.
                </div>
                {% endif %}
                </div>


            </div>
        </div>

    </div>


