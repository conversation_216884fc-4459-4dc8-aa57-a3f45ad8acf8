<style>
    tr {
        border-bottom: 1px solid #dee2e6;
    }
</style>










<!-- Middle Column - Hospital Info -->

    <div class="card shadow-sm h-100 border-primary border-opacity-25">
        <div class="card-header d-flex justify-content-between align-items-center p-2 bg-info">
            <h4 class="mb-0">병원 정보 감추기 </h4>
            <button id="toggle-middle-column" class="btn btn-sm btn-link text-primary" title="병원 정보 숨기기">
                <i class="fas fa-chevron-left"></i>
            </button>
        </div>

        <div class="card shadow-sm">
            <div class="card-body h-100 overflow-auto">
                <div style="display: flex; justify-content: center; margin: 10px 0;">
                    <div
                        style="width: 100%; max-width: 800px; border: 1px solid #ccc; border-radius: 10px; padding: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); font-family: '맑은 고딕', sans-serif;">
        
                        <!-- Flex container for header and edit button -->
                        <div
                            style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; margin-bottom: 20px;">
                            <h2 style="margin: 0; font-size: 24px; flex: 1 1 auto;">🏥 병원 정보</h2>
        
                            <a href="{% url 'edit_hospital' selected_visit.location_name.id %}"
                                class="btn btn-outline-primary btn-lg"
                                style="font-weight: bold; white-space: nowrap; margin-left: 10px;">
                                <i class="fas fa-edit"></i> 수정
                            </a>
                        </div>

<table style="width: 100%; border-collapse: collapse; font-size: 14px;">
    <tbody>
        <tr>
            <th style="text-align: left; padding: 8px;">의료재단</th>
            <td style="padding: 8px;">{{ selected_visit.location_name.jedan }}</td>
        </tr>
        <tr>
            <th style="text-align: left; padding: 8px;">지점명 / 지점장 </th>
            <td style="padding: 8px;">{{ selected_visit.location_name.jedan_branch }} <br> {{ selected_visit.location_name.jedan_branch.key_person_info }} </td>
        </tr>
        <tr>
            <th style="text-align: left; padding: 8px;">병원명</th>



            <td style="padding: 8px;">
                <div style="display: flex; align-items: center; gap: 12px;">
                    <a href="{% url 'hospital_report_detail' selected_visit.location_name.id %}"
                        style="font-size: 16px; font-weight: bold; color: #2a4d9b; text-decoration: none;">
                        <i class="fas fa-eye"></i> <i class="fas fa-eye"></i>  {{ selected_visit.location_name.hospital_name }}
                    </a>

                    <!-- <a href="{% url 'edit_hospital' selected_visit.location_name.id %}"
                        class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-edit"></i> 수정
                    </a> -->
                </div>
            </td>




        </tr>

        <tr>
            <th style="text-align: left; padding: 8px;">전화번호</th>
            <td style="padding: 8px;">{{ selected_visit.location_name.phone_number }}</td>
        </tr>
        <tr>
            <th style="text-align: left; padding: 8px;">주소 (한글)</th>
            <td style="padding: 8px;">
                {{ selected_visit.location_name.do_address }}
                {{ selected_visit.location_name.si_address }}
                {{ selected_visit.location_name.last_address }}
            </td>
        </tr>
        <tr>
            <th style="text-align: left; padding: 8px;">사업자 번호 </th>
            <td style="padding: 8px;">{{ selected_visit.location_name.pan_number }}</td>
        </tr>

        <!-- <tr>
            <th style="text-align: left; padding: 8px;"> 전화 번호 </th>
            <td style="padding: 8px;">{{ selected_visit.location_name.phone_number }}</td>
        </tr>

        <tr>
            <th style="text-align: left; padding: 8px;">사업자 번호</th>
            <td style="padding: 8px;">{{ selected_visit.location_name.pan_number|default:"-" }}
            </td>
        </tr> -->
        <tr>
            <th style="text-align: left; padding: 8px;"> CNV 추가 보고</th>
            <td style="padding: 8px;">
                {{ selected_visit.location_name.extra_report|yesno:"허용,거부" }}</td>
        </tr>

        <tr>
            <th style="text-align: left; padding: 8px;"> KEYMAN 정보</th>
            <td style="padding: 8px;">{{ selected_visit.location_name.key_person_info }}
            </td>
        </tr>



        <tr>
            <th style="text-align: left; padding: 8px;">등록 정보</th>
            <td style="padding: 8px;">
                {{ selected_visit.location_name.added_by|default:"-" }} <br>
                {{ selected_visit.location_name.created_at|date:"Y-m-d H:i" }}</td>
        </tr>
        <tr>
            <th style="text-align: left; padding: 8px;">최종수정정보</th>
            <td style="padding: 8px;">
                {{ selected_visit.location_name.last_edited_by|default:"-" }} <br>
                {{selected_visit.location_name.last_edited|date:"Y-m-d H:i" }}</td>
        </tr>


    </tbody>
</table>
        
                       
              
            </div>
        </div>






                 
              
                <!-- 가격 정보 섹션 -->
                <div class="mb-4">
                    {% if selected_visit.location_name.is_hospital %}
                    <h5 class="text-primary border-bottom pb-2 mb-3">
                        <i class="fas fa-tags mr-2"></i> 서비스 정보
                    </h5>


<table style="width: 100%; font-size: 15px; border-collapse: collapse; text-align: center;">
    <tr>
        <th style="padding: 8px; text-align: left;">서비스명</th>
        <th style="padding: 8px;">NIPT</th>
        <th style="padding: 8px;">PGT</th>
        <th style="padding: 8px;">ORA</th>
        <th style="padding: 8px;">GenoFind</th>
        <th style="padding: 8px;">Genobenet</th>
    </tr>
    <tr>
        <th style="padding: 8px; text-align: left;">건수</th>
        <td style="padding: 8px;">{{ selected_visit.location_name.number_of_patients }}</td>
        <td style="padding: 8px;">{{ selected_visit.location_name.number_of_pgt|default:"-" }}</td>
        <td style="padding: 8px;">{{ selected_visit.location_name.number_of_ora|default:"-" }}</td>
        <td style="padding: 8px;">{{ selected_visit.location_name.number_of_genobro|default:"-" }}</td>
        <td style="padding: 8px;">{{ selected_visit.location_name.number_of_genofind|default:"-" }}</td>
    </tr>
</table>



                    <div class="table-responsive">
                        <table class="table text-center" style="font-size: 15px; border-collapse: collapse;">
                            <thead>
                                <tr style="border-bottom: 2px solid #ccc;">
                                    <th style="font-weight: bold; padding: 10px;"></th>
                                    <th style="font-weight: bold; padding: 10px;">Lite</th>
                                    <th style="font-weight: bold; padding: 10px;">Standard</th>
                                    <th style="font-weight: bold; padding: 10px;">Plus</th>
                                    <th style="font-weight: bold; padding: 10px;">Twin</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="border-bottom: 1px solid #eee;">
                                    <th style="font-weight: bold; text-align: left; padding: 8px;"> NIPT 가격 </th>
                                    <td style="font-weight: bold;">
                                        ₩{{ selected_visit.location_name.price_lite|floatformat:0 }}</td>
                                    <td style="font-weight: bold;">
                                        ₩{{ selected_visit.location_name.price_std|floatformat:0 }}</td>
                                    <td style="font-weight: bold;">
                                        ₩{{ selected_visit.location_name.price_plus|floatformat:0 }}</td>
                                    <td style="font-weight: bold;">
                                        ₩{{ selected_visit.location_name.price_twin|floatformat:0 }}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #eee;">
                                    <th style="font-weight: bold; text-align: left; padding: 8px;">코드</th>
                                    <td>{{ selected_visit.location_name.code_lite|default:"-" }}</td>
                                    <td>{{ selected_visit.location_name.code_std|default:"-" }}</td>
                                    <td>{{ selected_visit.location_name.code_plus|default:"-" }}</td>
                                    <td>{{ selected_visit.location_name.code_twin|default:"-" }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                   

                <table>
                    <tr>
                        <!-- <th style="text-align: left; padding: 8px;">원장 정보</th> -->
                        <td style="padding: 8px; background-color: #f9f9f9;">
                            <div style="margin-bottom: 6px;">
                                총 <strong>{{ selected_visit.location_name.number_of_doctors }}명</strong>의 원장이 등록되어 있으며, 각 원장의 누적 검체 수는 다음과
                                같습니다:
                            </div>
                            <div style="white-space: normal; word-break: break-word; line-height: 1.6;">
                                {% for doctor in selected_visit.location_name.doctor_info.all %}
                                <span style="margin-right: 10px;">
                                    <strong>{{ doctor.full_name }}</strong> - {{ doctor.number_of_patients }}건,
                                </span>
                                {% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </div>
                        </td>
                    </tr>



                </table>

                {% else %}
                <div class="alert alert-warning" role="alert">
                    <strong>⚠️ 이 기관은 병원으로 설정된 기관이 아닙니다.</strong><br>
                    실제로 병원이라면 수정 부탁드립니다.
                </div>
                {% endif %}
                </div>


            </div>
        </div>

    </div>


