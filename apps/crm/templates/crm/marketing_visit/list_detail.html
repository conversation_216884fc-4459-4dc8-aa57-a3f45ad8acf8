{% extends "layouts/base.html" %}

{% block content %}
{% include "crm/marketing_visit/marketing_visit_header.html" %}

<h1>list_detail</h1>


<!-- Three Column Layout -->
<div class="container-fluid vh-100 px-0" ">
    <div class="row h-100 mx-0">

        <!-- Left Column - Visit List -->
        <div class="col-3 h-100 px-2">


            <div class="card shadow-sm h-100 border-primary border-opacity-25">
                <div class="card-body d-flex flex-column p-0">



                    <!-- Search and Filter Section -->
                    {% include "crm/marketing_visit/marketin_visit_search_filter.html" %}

                    <!-- Visits List with visits=all_visits_in_same_place   -->
                    {% include "crm/marketing_visit/hospital_visit_list_current.html"  %}



                    <!-- Pagination -->
                    {% include "crm/marketing_visit/hospital_visit_pagination.html" %}
                </div>


                <!-- Past Visits List with visits=all_visits_in_same_place   -->
                <!-- Past Visits List -->
                <div id="past-visit-list">
                    {% include "crm/marketing_visit/hospital_visit_list_past.html" with visits=all_visits_in_same_place %}
                </div>



            </div>
        </div>

        
        <style>
            #middle-column {
                transition: all 0.3s ease-in-out;
                flex: 0 0 55%;
                width: 55%;
                position: relative;
            }
            #middle-column.collapsed {
                flex: 0 0 0%;
                width: 0;
                padding: 0;
                overflow: hidden;
            }
            #right-column {
                transition: all 0.3s ease-in-out;
                flex: 0 0 45%;
                width: 45%;
                position: relative;
            }
            #right-column.expanded {
                width: 100%;
            }
            .main-content-area {
                display: flex;
                width: 100%;
                padding: 0 0.5rem;
                gap: 0.5rem;
            }
            #toggle-middle-column {
                transition: all 0.3s ease-in-out;
                padding: 0.5rem;
                border-radius: 50%;
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #fff;
                border: 1px solid #dee2e6;
            }
            #toggle-middle-column:hover {
                transform: scale(1.1);
                background: #f8f9fa;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            #toggle-middle-column.collapsed {
                position: absolute;
                left: -16px;
                top: 50%;
                transform: translateY(-50%);
                z-index: 10;
                box-shadow: -2px 0 4px rgba(0,0,0,0.1);
            }
            #toggle-middle-column.collapsed:hover {
                transform: translateY(-50%) scale(1.1);
            }
            .card-header {
                border-bottom: 1px solid rgba(0,0,0,.125);
            }
        </style>

        <!-- Main Content Area - Flex Container -->
        <div class="col-9 h-100 p-0">
            <div class="main-content-area h-100" >

                <!-- Middle Column -->
                <div id="middle-column" class="h-100">
                    {% if selected_visit %}
                    {% include "crm/marketing_visit/detail_middle_col.html" with visit=selected_visit %}
                    {% endif %}
                </div>



                <!-- Right Column -->
                <div id="right-column" class="h-100">
                    {% if selected_visit %}
                    <div class="card">
                        {% include "crm/marketing_visit/detail_last_col.html" with selected_visit=selected_visit %}
                    </div>
                    {% endif %}
                </div>

            </div>
        </div>

   


    </div>
</div>








{% block javascripts %}


<script>
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.load-visit-detail').forEach(function (el) {
            el.addEventListener('click', function (e) {
                e.preventDefault();

                const visitId = this.dataset.visitId;
                if (!visitId) return;

                fetch(`/crm/marketing_visit/ajax/detail/?visit_id=${visitId}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                    .then(response => response.json())
                    .then(data => {
                        // Replace Middle Column
                        document.getElementById('middle-column').innerHTML = data.middle_html;
                        //document.querySelector('#middle-column ').outerHTML = data.middle_html;


                        // Replace Right Column
                        document.getElementById('right-column').innerHTML = data.right_html;

                        // Replace Past Visits (optional)
                        const pastListContainer = document.getElementById('past-visit-list');

                        if (data.past_html && pastListContainer) {
                            pastListContainer.innerHTML = data.past_html;
                        }
                    })
                    .catch(error => {
                        console.error('AJAX load error:', error);
                    });
            });
        });
    });
</script>











{% endblock %}

{% endblock %}