{% extends "layouts/base.html" %}
{% load static %}
{% block extra_css %}

{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Page Header -->
    {% include "crm/marketing_visit/marketing_visit_header.html" %}

    <!-- Search and Filters -->
    <div class="card search-filters">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="{{ filter.form.search.id_for_label }}" class="form-label">검색</label>
                        {{ filter.form.search }}
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="{{ filter.form.date_range.id_for_label }}" class="form-label">방문일자 이후</label>
                        {{ filter.form.date_range }}
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="{{ filter.form.end_date.id_for_label }}" class="form-label">방문일자 이전</label>
                        {{ filter.form.end_date }}
                    </div>
                </div>

                {%  if request.user.is_superuser or request.user.user_employee.team in "marketing" %}

                <div class="col-md-3">
                    <div class="form-group">
                        <label for="{{ filter.form.marketing_manager.id_for_label }}" class="form-label">담당자</label>
                        {{ filter.form.marketing_manager }}
                    </div>
                </div>
                {% endif %}

                <div class="col-md-1 d-flex align-items-center">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> 검색
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Main Content -->
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table align-items-center table-flush">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>방문자 매니저 </th>
                            <th>방문일자</th>
                            <th>방문장소</th>
                            <th>면담자</th>
                            <th>주요 논의</th>
                            <th> 차료 제공 </th>
                            <th>작성일시</th>
                            <th> 수정 </th>
                            <th> 첨부파일 </th>

                        </tr>
                    </thead>
                    <tbody>
                        {% for visit in visits %}
                        <tr>
                            <td>
                                <a href="{% url 'marketing_visit_popup_detail' visit.id %}" target="_blank">
                                    {{ visit.id }}
                                </a>
                            </td>


                            <td class="clickable-cell"
                                onclick="window.location.href='{% url 'marketing_visit_list_detail' %}?person={{ visit.marketing_manager.id }}'; return false;"
                                style="cursor: pointer;size: 14px;">
                                <div class="person-name">{{ visit.marketing_manager.full_name }} </div>

                            </td>

                            <td>
                                <span class="font-weight-bold">{{ visit.visit_date|date:"Y-m-d" }}</span>
                            </td>

                            <td class="font-weight-bold clickable-cell"
                                onclick="window.location.href='{% url 'marketing_visit_list_detail' %}?place={{ visit.location_name.id }}&selected_visit={{ visit.pk }}'; return false;"
                                style="cursor: pointer;">
                                <span class="text-muted"> [ {{ visit.location_name.jedan }} ] </span>
                                <strong>{{ visit.location_name.hospital_name }}</strong>

                            </td>

                            <td>
                                <div class="person-name">{{ visit.person_met|slice:30 }}</div>
                                <small class="person-designation">{{ visit.person_designation }}</small>
                            </td>

                            <td class="clickable-cell">
                                <div style="display: flex; flex-direction: row; align-items: center; gap: 8px;">
                                    <button type="button" class="btn btn-sm btn-outline-primary show-popup-detail"
                                        data-visit-id="{{ visit.pk }}" title="Quick View">
                                        <i class="fas fa-eye"></i> <i class="fas fa-eye"></i>
                                    </button>

                                    <a href="{% url 'marketing_visit_list_detail' %}?selected_visit={{ visit.pk }}"
                                        class="main-topic-link" style="text-decoration: underline; color: #0d6efd;">
                                        {{ visit.main_topic|truncatechars:100 }}
                                    </a>

                                </div>
                            </td>

                            <td>

                                {{ visit.files_provided|slice:30 }}

                            </td>

                            <td>{{ visit.created_at|date:"Y-m-d H:i" }}</td>

                            <td class="action-buttons">
                                <a href="{% url 'marketing_visit_update' visit.pk %}" class="btn btn-sm btn-warning"
                                    data-toggle="tooltip" title="수정">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if user.is_superuser %}
                                <a href="{% url 'marketing_visit_delete' visit.pk %}" class="btn btn-sm btn-danger"
                                    data-toggle="tooltip" title="삭제">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </td>

                            <td>
                                {% if visit.attachment %}
                                <a href="{{ visit.attachment.url }}" class="btn btn-sm btn-light"
                                    title="{{ visit.get_file_name }}">
                                    <i class="fas fa-paperclip"></i>
                                </a>

                                {% else %}
                                없음

                                {% endif %}

                            </td>

                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="text-center py-5">
                                <div class="text-muted">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <p>등록된 방문 기록이 없습니다.</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    {% include 'crm/marketing_visit/pagination.html'  %}

    <!-- Add this at the end of the body but before any scripts -->
    <div class="modal fade" id="visitDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">방문 상세 정보</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="visitDetailContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

</div>

<script src="/static/assets/vendor/jquery/dist/jquery.min.js"></script>
<script src="/static/assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="/static/assets/vendor/js-cookie/js.cookie.js"></script>
<script src="/static/assets/vendor/jquery.scrollbar/jquery.scrollbar.min.js"></script>
<script src="/static/assets/vendor/jquery-scroll-lock/dist/jquery-scrollLock.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const modal = new bootstrap.Modal(document.getElementById('visitDetailModal'));
        document.querySelectorAll('.show-popup-detail').forEach(button => {
            button.addEventListener('click', function(e) {
                console.log('Button clicked'); // Debug log
                e.preventDefault();
                const visitId = this.getAttribute('data-visit-id');
                console.log('Visit ID:', visitId); // Debug log
                // Add CSRF token to headers
                const csrftoken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                fetch(`/crm/marketing_visit/popup-detail/${visitId}/`, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRFToken': csrftoken
                        },
                    })
                    .then(response => {
                        console.log('Response status:', response.status); // Debug log
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.text();
                    })
                    .then(html => {
                        console.log('Received HTML response'); // Debug log
                        document.getElementById('visitDetailContent').innerHTML = html;
                        modal.show();
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to load visit details. Please try again.');
                    });
            });
        });
    });
</script>

{% endblock %}