{% extends "layouts/base.html" %}
{% load static %}

{% block title %} Create CS Inquiry {% endblock %}

{% block extra_css %}
<style>
    /* Filter toggle styles */
    .filter-section {
        margin-bottom: 1.5rem;
    }
    
    .filter-toggle {
        cursor: pointer;
        padding: 10px 15px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .filter-toggle:hover {
        background-color: #e9ecef;
    }
    
    .filter-toggle i {
        transition: transform 0.3s;
    }
    
    .filter-toggle.collapsed i {
        transform: rotate(180deg);
    }
    
    .filter-content {
        padding: 15px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        background-color: #fff;
    }
    
    /* Form control consistent styling */
    .form-control, 
    select.form-select,
    input[type="date"] {
        display: block;
        width: 100%;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    .form-control:focus,
    select.form-select:focus,
    input[type="date"]:focus {
        border-color: #86b7fe;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
    
    /* Ensure date inputs have consistent height */
    input[type="date"].form-control {
        height: calc(1.5em + 0.75rem + 2px);
    }
</style>
{% endblock %}

{% block content %}
<div class="mt-4">
    <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        
        <!-- Filter Toggle Button -->
        <div class="filter-section">
            <div class="filter-toggle" id="filterToggle" onclick="toggleFilters()">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i> 필터 및 검색 옵션</h5>
                <i class="fas fa-chevron-up" id="toggleIcon"></i>
            </div>
            
            <!-- Filter Content (Initially Hidden) -->
            <div class="filter-content" id="filterContent" style="display: none;">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="searchInput" class="form-label">검색어</label>
                        <input type="text" class="form-control" id="searchInput" placeholder="검색어 입력...">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="dateFrom" class="form-label">시작일</label>
                        <input type="date" class="form-control" id="dateFrom">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="dateTo" class="form-label">종료일</label>
                        <input type="date" class="form-control" id="dateTo">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="statusFilter" class="form-label">상태</label>
                        <select class="form-control form-select" id="statusFilter">
                            <option value="">모든 상태</option>
                            <option value="open">진행중</option>
                            <option value="closed">완료</option>
                            <option value="pending">대기중</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="categoryFilter" class="form-label">카테고리</label>
                        <select class="form-control form-select" id="categoryFilter">
                            <option value="">모든 카테고리</option>
                            <option value="technical">기술 문의</option>
                            <option value="billing">결제 문의</option>
                            <option value="general">일반 문의</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="priorityFilter" class="form-label">우선순위</label>
                        <select class="form-control form-select" id="priorityFilter">
                            <option value="">모든 우선순위</option>
                            <option value="high">높음</option>
                            <option value="medium">중간</option>
                            <option value="low">낮음</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 text-end">
                        <button type="button" class="btn btn-primary" id="applyFilters">
                            <i class="fas fa-filter me-1"></i> 필터 적용
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="resetFilters">
                            <i class="fas fa-times me-1"></i> 초기화
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Include the original form content -->
        {% include "crm/cs_inquiry_forms/simple_combined_form.html" %}
        
        <div class="card">
            <div class="card-body d-flex justify-content-center align-items-center">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Create Inquiry
                </button>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form submission handling
    $('form').on('submit', function(e) {
        // Remove empty response forms before submission
        $('.border.p-3.mb-3.rounded').each(function() {
            var $form = $(this);
            var $answer = $form.find('textarea[name$="-answer"]');

            // If the form is empty
            if (!$answer.val() || !$answer.val().trim()) {
                // Decrease the total forms count
                var totalForms = parseInt($('#id_form-TOTAL_FORMS').val());
                $('#id_form-TOTAL_FORMS').val(totalForms - 1);

                // Remove the empty form
                $form.remove();
            }
        });
    });

    // Initialize any select2 dropdowns if you're using them
    if ($.fn.select2) {
        $('.select2').select2({
            theme: 'bootstrap4'
        });
    }

    // Enable tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Filter toggle functionality
    $('#filterToggle').click(function() {
        toggleFilters();
    });
    
    // Apply filters button
    $('#applyFilters').click(function() {
        // Implement your filter logic here
        console.log('Applying filters...');
        // Close the filter panel after applying
        toggleFilters();
    });
    
    // Reset filters button
    $('#resetFilters').click(function() {
        // Clear all filter inputs
        $('#searchInput').val('');
        $('#dateFrom').val('');
        $('#dateTo').val('');
        $('#statusFilter').val('');
        $('#categoryFilter').val('');
        $('#priorityFilter').val('');
        console.log('Filters reset');
    });
});

// Toggle filter visibility
function toggleFilters() {
    var filterContent = document.getElementById('filterContent');
    var toggleIcon = document.getElementById('toggleIcon');
    
    if (filterContent.style.display === 'none') {
        filterContent.style.display = 'block';
        toggleIcon.classList.remove('fa-chevron-down');
        toggleIcon.classList.add('fa-chevron-up');
    } else {
        filterContent.style.display = 'none';
        toggleIcon.classList.remove('fa-chevron-up');
        toggleIcon.classList.add('fa-chevron-down');
    }
}
</script>
{% endblock %}
