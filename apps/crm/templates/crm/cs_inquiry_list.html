{% extends "layouts/base.html" %}
{% load static %}

{% block title %} CS 리스트 {% endblock %}

<!-- Specific Page CSS goes HERE  -->
{% block stylesheets %}
<style>
    .alert {
        margin-bottom: 1.5rem;
        border-radius: 0.5rem;
    }
    .alert-success {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }
    .alert-danger {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }
    .table thead th {
        font-size: 1rem !important;
        font-weight: 700 !important;
        color: #32325d !important;
        text-transform: none !important;
        letter-spacing: normal !important;
        padding: 15px !important;
        background-color: #f8f9fe !important;
        border-bottom: 2px solid #e9ecef !important;
        vertical-align: middle !important;
    }

    .table thead th:hover {
        background-color: #f4f6ff !important;
    }

    .table thead th.sorting:before,
    .table thead th.sorting:after,
    .table thead th.sorting_asc:before,
    .table thead th.sorting_asc:after,
    .table thead th.sorting_desc:before,
    .table thead th.sorting_desc:after {
        font-size: 1rem !important;
        opacity: 0.5;
        right: 0.5em;
    }

    .table thead th.sorting_asc:before,
    .table thead th.sorting_desc:after {
        opacity: 1;
        color: #5e72e4 !important;
    }
</style>
{% endblock stylesheets %}


{% block content %}

<!-- Header -->
<div class="header pb-6">
    <div class="container-fluid">
        <div class="header-body">
            <div class="row align-items-center py-4">
                <div class="col-lg-6 col-7">
                    <h6 class="h2 d-inline-block mb-0">CS 이력 기록</h6>
                    <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                        <ol class="breadcrumb breadcrumb-links">
                            <li class="breadcrumb-item"><a href="#"><i class="fas fa-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="#">TGC-CRM</a></li>
                            <li class="breadcrumb-item active" aria-current="page">CS 이력 기록</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-lg-6 col-5 text-right">

                    <a href="{% url 'cs_inquiry_create' %}" class="btn btn-sm btn-neutral">New CS 상담  </a>
                    <a href="#" class="btn btn-sm btn-neutral">Filters</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Page content -->
<div class="container-fluid mt--6">
    <!-- Search Filters Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h3 class="mb-0">검색 필터</h3>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <div class="form-group">
                        <label>날짜 범위</label>
                        <input type="date" class="form-control" id="dateFrom">
                        <input type="date" class="form-control mt-2" id="dateTo">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label>구분1</label>
                        <select class="form-control" id="filterInquiryOrg">
                            <option value="">전체</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label>지점</label>
                        <select class="form-control" id="filterBranch">
                            <option value="">전체</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label>상태</label>
                        <select class="form-control" id="filterStatus">
                            <option value="">전체</option>
                            <option value="open">미해결</option>
                            <option value="in_progress">진행 중</option>
                            <option value="resolved">해결됨</option>
                        </select>
                    </div>
                </div>
            </div>
            <button class="btn btn-primary" id="applyFilters">필터 적용</button>
            <button class="btn btn-secondary" id="resetFilters">초기화</button>
        </div>
    </div>



    <button type="button" class="btn btn-primary btn-lg btn-block"> 상담 내용 </button>

    <!-- Main Data Table -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h3 class="mb-0">Customer Service / Relation History</h3>
                </div>
                <div class="col text-right">
                    <button class="btn btn-sm btn-primary" id="refreshTable">
                        <i class="fas fa-sync"></i> 새로고침 (작성일순 정렬)
                    </button>
                    <span class="ml-2 text-muted small">(최신 작성순으로 정렬됩니다)</span>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive py-4">
                <table class="table table-flush" id="datatable-buttons">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>구분1</th>
                            <th>서비스</th>
                            <th>병원</th>
                            <th> 재단/지점/기타 </th>
                            <th>관련 부서</th>
                            <th> 차트/검체 번호 </th>

                            <!-- <th>지점</th> -->
                            <th>상태</th>
                            <th>발신자</th>

                            <th>연락처</th>
                            <th>문의 사항</th>

                            <th>출처</th>
                            <th>Actions</th>
                            <th>작성자</th>
                            <th>작성 일자</th>

                        </tr>
                    </thead>
                    <tbody>
                        <!-- DataTables will populate this -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Footer -->
    {% include 'includes/footer.html' %}

</div>

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<!-- Load required scripts first -->
<script src="/static/assets/vendor/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="/static/assets/vendor/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="/static/assets/vendor/datatables.net-buttons/js/dataTables.buttons.min.js"></script>
<script src="/static/assets/vendor/datatables.net-buttons-bs4/js/buttons.bootstrap4.min.js"></script>
<script src="/static/assets/vendor/datatables.net-buttons/js/buttons.html5.min.js"></script>
<script src="/static/assets/vendor/datatables.net-buttons/js/buttons.flash.min.js"></script>
<script src="/static/assets/vendor/datatables.net-buttons/js/buttons.print.min.js"></script>
<script src="/static/assets/vendor/datatables.net-select/js/dataTables.select.min.js"></script>

<!-- Enhanced DataTable initialization -->
<script>
$(document).ready(function () {
    // Destroy existing instance if it exists
    if ($.fn.DataTable.isDataTable('#datatable-buttons')) {
        $('#datatable-buttons').DataTable().destroy();
    }

    // Initialize enhanced DataTable
    var table = $('#datatable-buttons').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{% url 'cs_inquiry_list' %}",
            "type": "GET",
            "data": function(d) {
                // Add all filter parameters
                return $.extend({}, d, {
                    "draw": d.draw || 1,
                    "status": $('#filterStatus').val() || $('select.form-control-sm').val(), // Get status from either filter
                    "service_type": $('#filterService').val(),
                    "date_from": $('#dateFrom').val(),
                    "date_to": $('#dateTo').val(),
                    // Ensure order parameters are always sent - default to created_at (column 13)
                    "order[0][column]": d.order && d.order[0] ? d.order[0].column : 13,
                    "order[0][dir]": d.order && d.order[0] ? d.order[0].dir : "desc"
                });
            },
            "dataSrc": function(json) {
                if (!json.data) return [];
                return json.data;
            },
            "error": function(xhr, error, thrown) {
                console.error('DataTables error:', error);
                toastr.error('데이터를 불러오는 중 오류가 발생했습니다.');
            }
        },
        "columns": [
            {
                "data": "id",
                "render": function(data) {
                    // Make ID column more visible and clickable
                    return `<strong class="text-primary">${data}</strong>`;
                }
            },
            { "data": "category1" },
            { "data": "service_type" },
            { "data": "hospital" },
            { "data": "inquiry_org" },
            
            { "data": "related_department" },
            { "data": "sample_id" },
            




            {
                "data": "status",
                "render": function (data) {
                    const statusConfig = {
                        'open': { class: 'bg-secondary text-white', text: '미해결' },
                        'in_progress': { class: 'bg-warning text-dark', text: '진행 중' },
                        'resolved': { class: 'bg-success text-white', text: '해결됨' }
                    };
                    const status = statusConfig[data] || { class: 'bg-secondary text-white', text: '미정' };

                    return `
            <span
                class="badge ${status.class}"
                style="font-size: 1rem; padding: 6px 12px; border-radius: 0.5rem; box-shadow: 0 1px 3px rgba(0,0,0,0.1);"
                title="${data}"
            >
                ${status.text}
            </span>
        `;
                }
            },



            { "data": "inquiry_person" },
            { "data": "inquiry_phone" },
            {
                "data": "inquiry_question",
                "render": function(data, type, row) {
                    if (!data) return '';
                    const truncatedText = data.length > 30 ? data.substring(0, 30) + '...' : data;
                    return `<a href="/crm/cs_inquiry/${row.id}/"
                              class="text-primary"
                              data-toggle="tooltip"
                              title="${data.replace(/"/g, '&quot;')}">
                            ${truncatedText}
                        </a>`;
                }
            },
            { "data": "inquiry_source" },
            {
                "data": "id",
                "orderable": false,
                "render": function(data) {
                    return `
                    <div class="dropdown">
                        <button class="btn btn-sm btn-icon-only text-light" type="button" data-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <div class="dropdown-menu dropdown-menu-right">
                            <a class="dropdown-item" href="/crm/cs_inquiry/${data}/update/">
                                <i class="fas fa-edit"></i> 수정
                            </a>
                            <a class="dropdown-item" href="/crm/cs_inquiry/${data}/">
                                <i class="fas fa-eye"></i> 상세보기
                            </a>
                            <a class="dropdown-item" href="/crm/cs_inquiry/${data}/delete/">
                                <i class="fas fa-trash"></i> 삭제
                            </a>
                        </div>
                    </div>`;
                }
            },
            { "data": "author" },
            { "data": "created_at" },
        ],
        // Enhanced features - Order by created_at (column 13) in descending order to show newest first
        "order": [[13, "desc"]],
        "orderCellsTop": true,
        "fixedHeader": true,
        "pageLength": 100,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "전체"]],
        "dom": "<'row'<'col-sm-12 col-md-6'B><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "buttons": [
            {
                extend: 'copy',
                text: '<i class="fas fa-copy"></i> 복사',
                className: 'btn btn-sm btn-default',
                exportOptions: {
                    columns: ':not(:last-child)'
                }
            },
            {
                extend: 'excel',
                text: '<i class="fas fa-file-excel"></i> Excel',
                className: 'btn btn-sm btn-success',
                title: 'CS Inquiry List_' + new Date().toISOString().split('T')[0],
                exportOptions: {
                    columns: ':not(:last-child)'
                }
            },
            {
                extend: 'pdf',
                text: '<i class="fas fa-file-pdf"></i> PDF',
                className: 'btn btn-sm btn-danger',
                title: 'CS Inquiry List_' + new Date().toISOString().split('T')[0],
                exportOptions: {
                    columns: ':not(:last-child)'
                }
            },
            {
                extend: 'print',
                text: '<i class="fas fa-print"></i> 인쇄',
                className: 'btn btn-sm btn-info',
                exportOptions: {
                    columns: ':not(:last-child)'
                }
            }
        ],
        "select": {
            style: 'multi',
            selector: 'td:not(:last-child)'
        },
        "language": {
            "processing": '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span>',
            "search": "검색:",
            "lengthMenu": "_MENU_ 개씩 보기",
            "info": "전체 _TOTAL_개 중 _START_ - _END_",
            "infoEmpty": "데이터가 없습니다",
            "infoFiltered": "(전체 _MAX_ 개 중 검색결과)",
            "emptyTable": "데이터가 없습니다.",
            "zeroRecords": "일치하는 데이터가 없습니다.",
            "paginate": {
                "first": "처음",
                "last": "마지막",
                "next": "다음",
                "previous": "이전"
            },
            "select": {
                "rows": {
                    "_": "%d개 선택됨",
                    "0": "클릭하여 행 선택",
                    "1": "1개 선택됨"
                }
            }
        },
        "initComplete": function() {
            // Status filter in header - corrected version
            this.api().columns([7]).every(function() {  // Status column index
                var column = this;
                var header = $(column.header());

                // Create filter div
                var filterDiv = $('<div class="mt-2"></div>');
                var select = $('<select class="form-control form-control-sm">' +
                    '<option value="">전체 상태</option>' +
                    '<option value="open">미해결</option>' +
                    '<option value="in_progress">진행 중</option>' +
                    '<option value="resolved">해결됨</option>' +
                    '</select>')
                    .appendTo(filterDiv)
                    .on('change', function() {
                        var val = $(this).val();
                        // Update both the column search and the main status filter
                        column.search(val).draw();
                        $('#filterStatus').val(val); // Sync with main filter
                    });

                header.append(filterDiv);
            });
        }
    });

    // Enable tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Add row highlighting on hover
    $('#datatable-buttons tbody').on('mouseenter', 'tr', function() {
        $(this).addClass('bg-light');
    }).on('mouseleave', 'tr', function() {
        $(this).removeClass('bg-light');
    });

    // Error handling
    table.on('error.dt', function(e, settings, techNote, message) {
        console.error('DataTables error:', message);
        toastr.error('테이블 처리 중 오류가 발생했습니다.');
    });

    // Sync filters
    $('#applyFilters').on('click', function() {
        var statusVal = $('#filterStatus').val();
        // Update both filters and redraw
        table.column(8).search(statusVal).draw();
        $('select.form-control-sm').val(statusVal);
    });

    $('#resetFilters').on('click', function() {
        // Reset both status filters
        $('#filterStatus').val('');
        $('select.form-control-sm').val('');
        table.column(8).search('').draw();
    });

    // Add refresh functionality
    $('#refreshTable').on('click', function() {
        table.ajax.reload(null, false);
    });

    // Add export filename customization
    var today = new Date().toISOString().slice(0, 10);
    $.extend(true, $.fn.dataTable.defaults, {
        "buttons": [
            {
                extend: 'excel',
                title: 'CS_Inquiry_List_' + today,
                className: 'btn btn-sm btn-success',
                exportOptions: {
                    columns: ':visible'
                }
            },
            // ... other export buttons ...
        ]
    });

    // Add error handling for failed AJAX requests
    $(document).ajaxError(function(event, jqxhr, settings, thrownError) {
        toastr.error('서버 연결에 실패했습니다. 잠시 후 다시 시도해주세요.');
    });
});
</script>

<!-- Other scripts -->
<script src="/static/assets/vendor/chart.js/dist/Chart.min.js"></script>
<script src="/static/assets/vendor/chart.js/dist/Chart.extension.js"></script>
<script src="/static/assets/vendor/jvectormap-next/jquery-jvectormap.min.js"></script>
<script src="/static/assets/js/vendor/jvectormap/jquery-jvectormap-world-mill.js"></script>

{% endblock javascripts %}