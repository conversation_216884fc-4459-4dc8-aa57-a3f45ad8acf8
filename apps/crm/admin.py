from django_summernote.admin import SummernoteModelAdmin

from django.contrib import admin

# Register your models here.
from .models import FAQ, CS_Inquiry, CS_Response, MarketingVisitFile
from .models.marketing_visit import MarketingVisit
from apps.crm.models.marketing_visit_comment import MarketingVisitComment


class FAQAdmin(SummernoteModelAdmin):
    list_display = ["question","created_at"]
    list_filter = ("service_type", )
    serach_filter = ("question", "answer")
    summernote_fields = ('answer',)
    #summernote_fields = '__all__'


class CS_ResponseInline(admin.TabularInline):
    model = CS_Response
    extra = 1
    fields = ['response_type', 'response_date', 'answered_by', 'answer']
    classes = ['collapse']


class CS_InquiryAdmin(admin.ModelAdmin):
    list_display = ["id", "inquiry_person", "service_type", "category1", "inquiry_question", "status", "created_at"]
    list_filter = ("service_type", "status", "category1")
    search_fields = ("inquiry_question", "inquiry_person", "other_org", "sample_id")
    readonly_fields = ("created_at", "updated_at")
    inlines = [CS_ResponseInline]
    list_display_links = ["id", "inquiry_question"]
    date_hierarchy = 'created_at'
    ordering = ["-id"]


class CS_ResponseAdmin(SummernoteModelAdmin):
    list_display = ["id", "cs_inquiry", "answered_by",
                    "response_type", "response_date"]
    list_filter = ("response_type",)
    search_fields = ("answer", "answered_by__full_name", "cs_inquiry__inquiry_question")
    readonly_fields = ("created_at", "updated_at")
    summernote_fields = ('answer',)
    list_display_links = ["id", "cs_inquiry"]
    date_hierarchy = 'response_date'
    ordering = ["-id"]


admin.site.register(FAQ, FAQAdmin)  #!

admin.site.register(CS_Inquiry, CS_InquiryAdmin)

admin.site.register(CS_Response, CS_ResponseAdmin)

# @admin.register(Location)
# class LocationAdmin(admin.ModelAdmin):
#     list_display = ('name', 'address', 'phone')
#     search_fields = ('name', 'address', 'phone')
#     ordering = ('name',)

# @admin.register(Person)
# class PersonAdmin(admin.ModelAdmin):
#     list_display = ('name', 'designation', 'location', 'phone')
#     list_filter = ('location',)
#     search_fields = ('name', 'designation', 'phone')
#     ordering = ('name',)
#     autocomplete_fields = ['location'] #! 자동 완성 필드



@admin.register(MarketingVisit)
class MarketingVisitAdmin(admin.ModelAdmin):
    list_display = ('marketing_manager', 'visit_date', 'visit_type', 'location_name', 'person_met')
    list_filter = ('visit_type', 'visit_date', 'marketing_manager')
    search_fields = ('location_name__name', 'person_met', 'main_topic')
    ordering = ('-visit_date',)

@admin.register(MarketingVisitComment)
class MarketingVisitCommentAdmin(admin.ModelAdmin):
    list_display = ('author', 'marketing_visit', 'content', 'created_at', 'is_edited')
    list_filter = ('created_at', 'is_edited', 'author')
    search_fields = ('content', 'author__first_name', 'author__last_name', 'marketing_visit__location_name__name')

@admin.register(MarketingVisitFile)
class MarketingVisitFileAdmin(admin.ModelAdmin):
    list_display = ('marketing_visit', 'description', 'get_file_name', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('description', 'marketing_visit__location_name__hospital_name')
    readonly_fields = ('created_at', 'updated_at')  # , 'is_edited'
