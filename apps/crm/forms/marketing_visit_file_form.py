from django import forms
from apps.crm.models.marketing_visit_file import MarketingVisitFile

class MarketingVisitFileForm(forms.ModelForm):
    """
    Form for uploading files related to a marketing visit
    """
    class Meta:
        model = MarketingVisitFile
        fields = ['file', 'description']
        widgets = {
            'description': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '파일에 대한 간단한 설명을 입력해주세요'
            }),
            'file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png'
            })
        }
        
    def clean_file(self):
        file = self.cleaned_data.get('file')
        if file:
            # Limit file size to 10MB
            if file.size > 10 * 1024 * 1024:
                raise forms.ValidationError("파일 크기는 10MB 이하여야 합니다.")
            
            # Check file extension
            allowed_extensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png']
            ext = file.name.split('.')[-1].lower()
            if ext not in allowed_extensions:
                raise forms.ValidationError(f"허용된 파일 형식: {', '.join(allowed_extensions)}")
        return file
