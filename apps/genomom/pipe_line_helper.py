# -*- encoding: utf-8 -*-
"""
Copyright (c) 2021 - present Theragen Genome Care
this file is designed to support task.py celery 
"""


#print("after  helper  1 ")

import os, sys, glob , fnmatch
from celery import shared_task

import subprocess
import logging

import zipfile

from datetime import datetime
from apps.common.log import path_check

import pandas as pd

from apps.genomom.models import (
    SampleInfo, FF_info, Logo_Sign, Ppv_Npv, Trisomy, Read_Counts_GC,
    Read_Ratio, Z_score, Pff_Info, Ppv_Npv
)

from celery import current_app


SCRIPT_DIR = "/BiO/genomom_ref/"  #! This is not a github repo
REFERENCE = SCRIPT_DIR + "UCSC_hg19/index" #! reference for bowtie2




def sample_step_add(sample, step_size):
    """
    add every time step size, this function help to reduce code line, 
    using this function 2 lines code will done in a single line 
    default = 0, which is when start 
    """
    sample.pipeline_progress += step_size
    sample.save()
    

@shared_task
def run_pipeline_with_error_handling(function, args, kwargs, function_name, result_folder, sample,
                                     step_size=10,arg_save=True):
    """
    Runs a function with error handling and returns success status.

    Args:
        function (function): The function to be executed.
        args (tuple): Arguments for the function.
        kwargs (dict): Keyword arguments for the function.
        function_name (str): Name of the function for logging.
        result_folder (str): Path to the Log File .
        sample (object): Sample object.
        step_size (int, optional): Step size for sample update. Default is 10.

    Returns:
        bool: True if the function executed successfully, False otherwise.
    """
    text = "*"*20 
    text += f"\nRunning function {function_name} at {datetime.now()} with parameters \n"
    if arg_save:
        text += str(kwargs) + "\n"
        
    path_check(text=text, root_dir=result_folder)

    try:
        function(*args, **kwargs)
                
        text = f"Function {function_name} successfully ended at {datetime.now()} \n\n"
        path_check(text=text, root_dir=result_folder)
        
        sample_step_add(sample=sample, step_size=step_size)
        
        
        
        return True
    
    except Exception as err:        
        text = f"{function_name} Failed at {datetime.now()}\n {err} \n"
        path_check(text=text, root_dir=result_folder)
        # Change Flag if Error Occurred
        sample.pipeline_status = '3'  # Error
        sample.save()
        
        text = f"{function_name} Failed at {datetime.now()}\n {err} \n"
        path_check(text=text, root_dir=result_folder)
               
        
        current_app.control.revoke( sample.celery_task, terminate=True)
        current_app.control.revoke( sample.celery_task, terminate=True,signal='SIGKILL' )
        
        return False
    

def info_for_fastq_download(sample):
    """
    to see code more clear we will break down code.
    information required to downlaod fastaq file.
    
    """
    analysis_run_number = sample.analysis.run_number
    ts_url = sample.analysis.ts_server.http_path + str(analysis_run_number) + '/'  # http://**************/report/774/
    ts_run = sample.analysis.run_name
    ts_id = sample.analysis.ts_server.user_id  # ! torrent server user id
    ts_pw = sample.analysis.ts_server.password  # ! password for
    barcode = "IonXpress_" + str(sample.sample_dna_qc.bar_code_3digit())
    
    sample_id = sample.tgc_s_id  # ! Theragen Genome care internal id
    
    #! celery doesnot know django path so add more path here
    result_folder = os.path.join('/BiO/github/genomom_lims/media', sample.sample_path()  )
    
    #! Write Log File 
    text = f"Before Pipeline Running information obtained from database\n"
    text += f"TS URL={ts_url}, TS_Run = {ts_run}, ts_id = {ts_id}, Barcode ={barcode}, data_dir={sample.sample_path() }, sample ID={sample_id} \n"
    text += f"Sample Path={sample.sample_path()  }"
    path_check(text=text, root_dir=result_folder)
    
    return ts_url, ts_run, ts_id, ts_pw, barcode, sample_id, result_folder
    

def rc_gc_db_update(sample, unique_csv):
    """

    Create or Update Read_Counts_GC model
    unique_csv must be the our Excel file from pipeline
    
    """


    # RC GC
    try:
        # Create objects without saving to the database
        rc_gc_dict = {"sample": sample}

        for i in range(24):
            #! To Create a New
            rc_gc_dict[f'gc{i+1}'] = unique_csv.loc[i, "GC"]
            rc_gc_dict[f'read{i+1}'] = unique_csv.loc[i, "Count"]
        
        rc_gc = Read_Counts_GC(**rc_gc_dict)
        
        rc_gc.save()
        
        rc_gc.total_reads =  int(unique_csv.loc[unique_csv.Info == "Total_Raw_Read", "Value" ][0])
        rc_gc.wgc =  float(unique_csv.loc[unique_csv.Info == "Sample_GC", "Value" ][2])
        
        ts_result_path = "./TS_result"
        
        try:
            df = pd.read_table(ts_result_path)
            rc_gc.barcode   = df.loc[0, "Barcode"]            
            rc_gc.total_bases =  df.loc[0, "Total_Bases"]
            rc_gc.q20_bases  = df.loc[0, "Q20_Bases"]
            rc_gc.reads      = df.loc[0, "Reads"]
            rc_gc.mrl        = df.loc[0, "Mean_Read_Length"]
            rc_gc.ts_report  = df.loc[0, "TS_Report"]
            rc_gc.save()
        except :
            #! later write log as a reason to fail 
            pass
            

    except:

        rc_gc, created = Read_Counts_GC.objects.get_or_create(sample=sample)
        for i in range(24):
            setattr(rc_gc, f'read{i+1}', unique_csv.loc[i, "Count"])

        setattr(rc_gc, 'total_reads', unique_csv.loc[unique_csv.Info == "Total_Raw_Read", "Value" ][0].astype(int)  )
        
        setattr(rc_gc, 'wgc', float(unique_csv.loc[unique_csv.Info == "Sample_GC", "Value" ][2]) )
        
        rc_gc.save()
        
        ts_result_path = "./TS_result"
        try:
            df = pd.read_table(ts_result_path)
            rc_gc.barcode   = df.loc[0, "Barcode"]
            rc_gc.total_bases =  df.loc[0, "Total_Bases"]
            rc_gc.q20_bases  = df.loc[0, "Q20_Bases"]
            rc_gc.reads      = df.loc[0, "Reads"]
            rc_gc.mrl        = df.loc[0, "Mean_Read_Length"]
            rc_gc.ts_report  = df.loc[0, "TS_Report"]
            rc_gc.save()
        except :
            pass
            #! write log later 
            
def z_score_update(sample, unique_csv, z_score_column = "0.001_Z" ) :
    """
    in data base we are saving only one type of Z-Score.
    but in excels we are calculating in 4 differents types
        
    """
    # Z Score
    try:
        # Create objects without saving to the database
        z_score_dict = {"sample": sample}
        for i in range(24):
            #! To Create a New
            z_score_dict[f'zscore{i+1}'] = unique_csv.loc[i, "0.001_Z"]
        z_score = Z_score(**z_score_dict)
        z_score.save()

    except:
        z_score, created = Z_score.objects.get_or_create(sample=sample)
        for i in range(24):
            setattr(z_score, f'zscore{i+1}', unique_csv.loc[i, z_score_column ])
        
        z_score.save()
        
        

def ff_info_update(sample, ff_y):
    """
    Update FF_info based on Excel data and conditionally update patient information.
    """
    # Find .tff file from seq Program
    file_tff = glob.glob(f"{sample.tgc_s_id}*.FF.csv")

    if not file_tff:
        logging.warning("No .tff file found for sample %s", sample.tgc_s_id)
        return

    try:
        df = pd.read_csv(file_tff[0])
        # Convert first row to dictionary
        info_value_dict = df.iloc[0].to_dict()
    except Exception as e:
        logging.error("Error reading .tff file: %s", str(e))
        return

    try:
        # Get or create FF_info object
        ff_info, created = FF_info.objects.get_or_create(sample=sample, defaults={
            'geno_ai': info_value_dict.get("GenoAI", ""),
            'geno_ff': info_value_dict.get("GenoFF", ""),
            "pca_ai": info_value_dict.get("PCA_AI", ""),
            'seq1': info_value_dict.get("Seq1", ""),
            'seq2': info_value_dict.get("SeqFF", ""),
            'ff_y': ff_y,
            'enet': info_value_dict.get("Enet", ""),
            'wrsc': info_value_dict.get("WRSC", ""),
        })

        # Save the object
        ff_info.save()

        # Change Sample and Patient info when ff_y is Greater than 3
        # and is Single Baby
        # print("Male Femael ")
        
        #if sample.patient.fetus_number.lower() in ["single", 'singleton', "단태아"]:
        #! 2025-05-08 이유진 주임 곽환종 이사님 요청 사항 성별 3 이상 설정 요청 , wehago messenger
        #! before this 5 , better to set on the basis of genomom or pippin , but lab member cant understand this so ignore 
        
        if ff_y > 3 :
            sample.patient.fetus_sex = '+'
            sample.patient.save()
            logging.info("Fetus is Single for sample %s", sample.tgc_s_id)
            # print("Male ")
        else:
            sample.patient.fetus_sex = '-'
            sample.patient.save()
            logging.info("Fetus is Single for sample %s", sample.tgc_s_id)
                # print("FeMale ")
                

    except Exception as e:
        logging.error("Error updating FF_info: %s", str(e))
        
        
        
        
def read_ratio_update(sample, unique_csv):
    """
    Although Read Ratio can be calculated using Read Counts

    
    """
    # RC GC
    try:
        
        # Create objects without saving to the database
        rr_dict = {"sample":sample}
        for i in range(24):    
            #! To Create a New
            rr_dict[f'rr{i+1}'] = unique_csv.loc[i, "Ratio"]
            
        rr = Read_Ratio(**rr_dict)
        rr.save()
        
    except :
        
        rr, created   = Read_Ratio.objects.get_or_create(sample = sample )
        for i in range(24):
            setattr( rr, f'rr{i+1}', unique_csv.loc[i, "Ratio"] )
        
        rr.save()
        
        

def pff_update(sample, info_value_dict) :
    """
    Calculated PFF from Excel File will be update.
    remove < > from database no need to store this
    manage this inside template. in pipeline this was used so
    < indication for less than 
    """
    try:
        #! Create objects without saving to the database
        pff_info = Pff_Info.objects.create(sample=sample)
        pff_info.t21_pop_risk = info_value_dict["t21_pop_risk"].strip('<').strip('>')
        pff_info.t21_sample_risk = info_value_dict["t21_sample_risk"].strip('<').strip('>')
        pff_info.t13_pop_risk = info_value_dict["t13_pop_risk"].strip('<').strip('>')
        pff_info.t13_sample_risk = info_value_dict["t13_sample_risk"].strip('<').strip('>')
        pff_info.t18_pop_risk = info_value_dict["t18_pop_risk"].strip('<').strip('>')
        pff_info.t18_sample_risk = info_value_dict["t18_sample_risk"].strip('<').strip('>')
        pff_info.save()

    except:

        pff_info, created = Pff_Info.objects.get_or_create(sample=sample)
        pff_info.t21_pop_risk = info_value_dict["t21_pop_risk"].strip('<').strip('>')
        pff_info.t21_sample_risk = info_value_dict["t21_sample_risk"].strip('<').strip('>')
        pff_info.t13_pop_risk = info_value_dict["t13_pop_risk"].strip('<').strip('>')
        pff_info.t13_sample_risk = info_value_dict["t13_sample_risk"].strip('<').strip('>')
        pff_info.t18_pop_risk = info_value_dict["t18_pop_risk"].strip('<').strip('>')
        pff_info.t18_sample_risk = info_value_dict["t18_sample_risk"].strip('<').strip('>')

        pff_info.save()


def file_size_check(file_path,result_folder ,sample, size=0 ):
    """
    this function check weather given file exist or not in given path 
    """
    
    if os.path.exists(file_path):
        file_size = os.stat(file_path).st_size   
        # Convert bytes to megabytes (1 MB = 1024 * 1024 bytes)
        file_size_mb = file_size / (1024 * 1024)

        if file_size_mb > size:
            text = f"{file_path} exist which size is {file_size_mb}MB \n"
            path_check(text=text, root_dir=result_folder)
            return True
        else:    
            sample.pipeline_status = '3'  # Error
            sample.save()
            
                   
            text = f"The file '{file_path}' exists and its size is {file_size_mb}  Cant proceed ahead \n"
            path_check(text=text, root_dir=result_folder)
            
            
            current_app.control.revoke( sample.celery_task, terminate=True)
            current_app.control.revoke( sample.celery_task, terminate=True,signal='SIGKILL' )
            

            
            sys.exit()
          
    else:
        text = f"The file '{file_path}' doesnot  exists \n"
        path_check(text=text, root_dir=result_folder)
        
        current_app.control.revoke( sample.celery_task, terminate=True)
        sys.exit()
            

def delete_files_matching_patterns(folder_path, delete_patterns):
    """
    Delete all files in folder_path matching any of the delete_patterns.
    Errors on individual files are caught and logged, rest continue.
    """
    try:
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                matched = False
                for pattern in delete_patterns:
                    if fnmatch.fnmatch(file, pattern):
                        matched = True
                        break
                if matched:
                    file_path = os.path.join(root, file)
                    try:
                        os.remove(file_path)
                        #print(f"✅ Deleted: {file_path}")
                    except Exception as e:
                        print(f"❌ Failed to delete {file_path}: {e}")
    except Exception as e:
        print(f"❌ Error traversing directory: {e}")
        
        
        
        


def delete_files_matching_patterns_(folder_path, delete_patterns):
    """
    Delete files in folder_path (and subdirectories) that match any of the patterns in delete_patterns.
    
    This is useful after a successful pipeline run to clean up large unnecessary files.
    """
    try:
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                for pattern in delete_patterns:
                    if fnmatch.fnmatch(file, pattern):
                        file_path = os.path.join(root, file)
                        try:
                            os.remove(file_path)
                            print(f"Deleted file: {file_path}")
                        except Exception as e:
                            print(f"Error deleting file: {file_path} - {e}")
                        break  # Stop checking more patterns for this file
    except Exception as e:
        print(f"Error: {e}")



def zip_files_matching_patterns(folder_path, zip_extension='.zip', file_patterns=[]):
    try:
        # Iterate over the specified directory and its subdirectories
        for root, dirs, files in os.walk(folder_path):
            # Iterate over each file in the current directory
            for file in files:
                # Check if the file matches any of the specified patterns
                for pattern in file_patterns:
                    if fnmatch.fnmatch(file, pattern):
                        # Construct the full path of the file
                        file_path = os.path.join(root, file)
                        # Construct the name for the zip file
                        zip_file_path = file_path + zip_extension
                        # Create a zip file
                        with zipfile.ZipFile(zip_file_path, 'w') as zipf:
                            # Add the file to the zip file
                            zipf.write(
                                file_path, arcname=os.path.basename(file_path))
                        # Remove the original file after zipping
                        os.remove(file_path)
                        #print(f"Zipped and removed original file: {file_path}")
    except Exception as e:
        print(f"Error: {e}")











@shared_task
def run_command_task(command,  log_path):
    try:
        # Construct the full command
        # Run the command using subprocess
        out = subprocess.run(command, capture_output=True, text=True)
        
        if out.returncode != 0:
            # Write error log
            text = f"Command {command} could not be executed successfully\n{out.stderr}"
            path_check(text=text, root_dir=log_path)
            return False  # Return False on error
        
        text = f"Command {command} executed successfully"
        path_check(text=text, root_dir=log_path)
        
        return True  # Return True on success
    except Exception as e:
        path_check(text=str(e), root_dir=log_path)
        return False  # Return False on exception
        
@shared_task
def data_analysis_ff(Fastq_ID, result_folder):

    """
    Returns
    -------
    This Function will Run shell script Code where Reference File is important,
    Reference File require for bowtie2 analysis,
    FASTQ ID is same so we will use Global ID and Reference file is always fixed
    so i will pass Full path which is directly editable in shell script
    """
    cmd = ["sh", SCRIPT_DIR + "Data_analysis_FF.sh", Fastq_ID, REFERENCE]
    result = run_command_task(cmd, result_folder )
    
    
    return True if result  else  False
    
@shared_task
def data_analysis_Fastq( Fastq_ID , result_folder  ):

    """
    Returns
    -------
    This Function will Run shell script Code where Reference File is important,
    Reference File require for bowtie2 analysis,
    FASTQ ID is same so we will use Global ID and Reference file is always fixed
    so i will pass Full path which is directly editable in shell script
    """
    cmd = ["sh", SCRIPT_DIR + "Data_analysis_Fastq.sh", Fastq_ID, REFERENCE]
    result = run_command_task(cmd, result_folder )
    
    return True if result  else  False



@shared_task
def cnv_plot(cnv_input_file, result_folder, ref_type ):
    """
    for NIPT pipeline for cnv parts, only calling function is easy to debug and 
    easy to understand in lims also so 

    Args:
        cnv_input_file (_type_): either .unique or .ext file obtained from pipeline
        result_folder (_type_): where to store result 
        ref_type (_type_): pippin or default old 
        log_path (_type_): log will written in this path
    """
    
    cnv_cmd = ['/BiO/github/genomom/cnv_plot.py', "--in", cnv_input_file, "--win",  ref_type, '--out', result_folder]
    
    result = run_command_task(cnv_cmd, result_folder)
    
    return True if result  else  False


@shared_task
def genoAI_rc( unique_file , result_folder  ):
    """
    #! function  is copy paste from cnv plot so argument doc string is not updated now
    for NIPT pipeline for cnv parts, only calling function is easy to debug and 
    easy to understand in lims also so 

    Args:
        cnv_input_file (_type_): either .unique or .ext file obtained from pipeline
        result_folder (_type_): where to store result 
        ref_type (_type_): pippin or default old 
        log_path (_type_): log will written in this path
    """
    # print("Unique_File" ,unique_file, "Result_folder : ", result_folder )
    genoAI_cmd = ['/BiO/github/genomom/genoai_predict.py', "--in_file", unique_file , "--in_dir",  result_folder]
    
    result = run_command_task(genoAI_cmd , result_folder)
    
    return True if result  else  False






@shared_task
def fastq_download(sample_id ,barcode, data_dir,ts_url, user_id, user_pw ):
    """
    fastq  file download from torrent server 
    
    fastq_down_kwrgs = {'sample_id': sample_id,'barcode': barcode, "data_dir" : result_folder, 'ts_url': ts_url, 'user_id': ts_id,'user_pw': ts_pw,  }
    
    fastq_download_barcode.py --sample_id AGC2309N6089_R1_V1 --barcode IonXpress_056 --ts_url http://**************/report/2795/

    Args:
        cnv_input_file (_type_): either .unique or .ext file obtained from pipeline
        data_dir  (_type_): where to store result 

       
    """
    
    cnv_cmd = ['/BiO/github/genomom/fastq_download_barcode.py', "--id", sample_id, "--barcode",  barcode, '--ts_url',ts_url , '--data_dir', data_dir, "--user_id", user_id, "--user_pw", user_pw ]
    # print(cnv_cmd)
    
    result = run_command_task(cnv_cmd,data_dir)
    
    return True if result  else  False




@shared_task
def cnv_prediction(unique_input_file, ff_input, result_folder):
    """
    
    for NIPT pipeline for CNV Prediction, only calling function is easy to debug and 
    easy to understand in lims also so 

    Args:
        unique_input_file (_type_): either .unique obtained from pipeline
        ff_input (_type_): GenoAI FF value(recommendation, FF_Y also possible)
        out_dir (_type_): output path
        
    applied from 2024-12-06 
        

    """
    


    
    cnv_prediction_cmd = ['/BiO/github/genomom/cnv_ai_prediction.py', "--in", unique_input_file, "--ff",  ff_input, '--out', result_folder]

   
    
    result = run_command_task(cnv_prediction_cmd, result_folder)
    
    return True if result  else  False