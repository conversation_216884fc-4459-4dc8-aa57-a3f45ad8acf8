{% load tz %}
{% load sample_step_color %}

<!-- Specific Page CSS goes HERE  -->
{% block stylesheets %}

<style>
    .table tbody th {
        padding: 0px;
        line-height: 1;
        /* Adjust the line height as needed */
        margin: 0;
    }


    .table tbody td {
        padding: 2px;
        line-height: 1;
        /* Adjust the line height as needed */
        margin: 1px;

    }

    /* Popup container - can be anything you want */
    .popup {
        display: none;
        /* Hidden by default */
        position: fixed;
        /* Stay in place */
        z-index: 1;
        /* Sit on top */
        left: 20%;
        top: 5%;
        /* Add margin from top */
        width: 50%;
        height: 97%;
        /* Adjust height */
        overflow: auto;
        /* Enable scroll if needed */
        background-color: rgba(255, 255, 255, 0.9);
        /* Semi-transparent white background */
        border: 2px solid #ccc;
        /* Add border for visibility */
        padding: 20px;
        /* Add padding for content */
        box-sizing: border-box;
        /* Include padding in total width/height */
    }

    /* Popup Content (image) */
    .popup-content {
        margin: auto;
        display: block;
        max-width: 100%;
        max-height: 100%;
    }

    /* Close Button */
    .close {
        position: absolute;
        top: 10px;
        right: 10px;
        color: red;
        /* Change close button color to red */
        font-size: 24px;
        font-weight: bold;
        transition: 0.3s;
    }

    .close:hover,
    .close:focus {
        color: darkred;
        /* Darker red on hover/focus */
        text-decoration: none;
        cursor: pointer;
    }
</style>

{% endblock stylesheets %}

<div class="card" style="width:100%">

    <h1>Total Selected Samples : <sapn class="text-primary strong"> {{ total_samples }} </span> </h1>

    <!-- Wrap the modal in a larger div -->
    <div id="modalWrapper" style="display: none;">
        <div class="modal fade" id="loadingModal" tabindex="-1" role="dialog" aria-labelledby="loadingModalLabel"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <h1>Loading...</h1>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="table-responsive text-dark">
        {% comment %} id="datatable-buttons" {% endcomment %}
        <table class="table table-hover table-bordered" id="datatable-buttons">

            <thead class="table-light">
                <tr>
                    <th style="max-width:10px;">
                        <div class="custom-control custom-checkbox">
                            <input class="custom-control-input" name='sample_checkbox' onClick="toggle(this)"
                                id="table-check-all" type="checkbox">
                            <label class="custom-control-label" for="table-check-all"> </label>
                        </div>
                    </th>
                    <th style="max-width:165px;">
                        <h5> GC S ID <i class="fas fa-sort"></i> </h5>
                    </th>

                    <th style="padding-left: 5px; padding-right: 5px;max-width:50px;">
                        <h5> 입고일 <i class="fas fa-sort"></i></h5>
                    </th>
                    <th style="padding-left: 5px; padding-right: 5px; h4">
                        <h5> 출고일 <i class="fas fa-sort"></i> </h5>
                    </th>
                    <th style="padding-left: 5px; padding-right: 5px;">
                        <h5> 단계 <i class="fas fa-sort"></i> </h5>
                    </th>

                    <th style="padding-left: 5px; padding-right: 5px;">
                        <h5> 재단 <i class="fas fa-sort"></i> </h5>
                    </th>
                    <th style="padding-left: 5px; padding-right: 5px;">
                        <h5> 재단접수번호 <i class="fas fa-sort"></i> </h5>
                    </th>

                    <th class="align-middle text-center">
                        <h5> 의료기관명 <i class="fas fa-sort"></i> </h5>
                    </th>
                    <th class="align-middle text-center">
                        <h5> 서비스 <i class="fas fa-sort"></i> </h5>
                    </th>

                    <th class="align-middle text-center">
                        <h5> 판독결과 <i class="fas fa-sort"></i> </h5>
                    </th>
                    <th class="align-middle text-center">
                        <h5> 결과 <i class="fas fa-sort"></i> </h5>
                    </th>

                    <th class="align-middle text-center" style="width: 50px;">
                        <h5> 임신주수 <i class="fas fa-sort"></i> </h5>
                    </th>

                    <th>
                        <h5> 성 <i class="fas fa-sort"></i> </h5> (의사 - 입고 )
                    </th>

                    <th class="align-middle text-center" style="width: 50px;">
                        <h5> FF <i class="fas fa-sort"></i> </h5>
                    </th>

                    <th class="align-middle text-center" style="width: 50px;">
                        <h5> FF_Y <i class="fas fa-sort"></i> </h5>
                    </th>

                    <th class="align-middle text-center">
                        <h5> PDF/JPG <i class="fas fa-sort"></i> </h5>
                    </th>
                    <th> Log / Action </th>

                </tr>
            </thead>

            <tbody>

                {% if sample_list %}

                {% for sample in sample_list %}
                <tr id="{{sample.id}}">
                    <td class="align-middle text-center row">

                        <div class="custom-control custom-checkbox row">
                            <input class="custom-control-input" name='sample_checkbox' value="{{sample.id}}"
                                id="table-check-all-{{sample.id}}" type="checkbox">
                            <label class="custom-control-label" for="table-check-all-{{sample.id}}">
                                {% comment %} {{sample.id}} {% endcomment %}
                                {{forloop.counter}}
                            </label>

                            {% if  sample.report_publish  %}

                            <i class="fa fa-check" style="color:green; font-size: 13px;"></i>

                            {% else %}

                            <i class="fa fa-times" style="color:black; font-size: 13px;"></i>

                            {% endif %}

                        </div>

                    </td>

                    <td style="max-width:190px;padding-left: 5px; padding-right: 5px;">

                        <a href="{% url 'sample_patient_info_edit' sample.id %}" class="table-action "
                            data-toggle="tooltip" data-original-title="Edit product" title="환자, 검체 수정 체이지">
                            <i class="text-red fas fa-pencil-alt"></i>
                        </a>

                        <a target="_blank" class="btn btn-sm btn-outline-info"
                            href="{% url 'sample_detail' sample.id %}">
                            {{sample.tgc_s_id}}
                        </a>
                    </td>
                    <td class="align-middle text-center" style="padding-left: 5px; padding-right: 5px;max-width:80px;">
                        {{ sample.entry_date|date:"m월d일" }}</td>

                    <td class="align-middle text-center" style="padding-left: 5px; padding-right: 5px;max-width:80px;">

                        {% now "Y-m-d H:i:s" as current_time %}

                        {% if sample.result_sent_time|date:'Y-m-d H:i:s' < current_time %}

                        <strong style="color: red;">
                            {{ sample.result_sent_time|date:"m월d일" }}
                        </strong>
                        {% else %}

                        {{ sample.result_sent_time|date:"m월d일" }}

                        {% endif %}
                    </td>

                    {% comment %} <td style="padding-left: 5px; padding-right: 5px;"
                        class={% if  sample.sample_process != '9' and sample.sample_process != '8'  %}"bg-danger
                        font-weight-bold">

                        {{ sample.get_sample_process_display|slice:3 }} {% else %} >

                        {{ sample.get_sample_process_display|slice:3 }}

                        {% if  sample.report_publish  and sample.sample_process == '9' %}

                        <button type="submit" class="btn btn-success btn-sm" disabled> 완료 <i
                                class="fa fa-check"></i></button>
                        {% else %}

                        <button type="submit" class="btn btn-warning btn-sm" disabled> 대기 <i class="fa fa-times-circle"
                                aria-hidden="true"> </i> </button>

                        {% endif %}

                        {% endif %}

                    </td> {% endcomment %}
                    
                    {% comment %} <td style="padding-left: 5px; padding-right: 5px;">

                        {% if  sample.report_publish and sample.sample_process == '9' %}

                        <button type="submit" class="btn btn-success btn-sm" disabled>
                            {{ sample.get_sample_process_display|slice:3 }} <i class="fa fa-check"></i></button>

                        {% elif  sample.sample_process == '8' or sample.sample_process == '9' %}

                        <button type="submit" class="btn btn-primary btn-sm" disabled>
                            {{ sample.get_sample_process_display|slice:3 }} <i class="fa fa-check"></i></button>

                        {% elif  sample.sample_process == '7' or  sample.sample_process == '6'  %}

                        <button type="submit" class="btn btn-info btn-sm" disabled>
                            {{ sample.get_sample_process_display|slice:3 }} <i class="fa fa-check"></i></button>

                        {% elif  sample.sample_process == '0' or  sample.sample_process == '1' or  sample.sample_process == '2'  %}

                        <button type="submit" class="btn btn-secondary btn-sm" disabled>
                            {{ sample.get_sample_process_display|slice:3 }} <i class="fa fa-check"></i></button>

                        {% elif  sample.sample_process == '10' or not sample.is_active  %}

                        <button type="submit" class="btn btn-dark btn-sm" disabled>
                            {{ sample.get_sample_process_display|slice:3 }} <i class="fa fa-check"></i></button>

                        {% else %}

                        <button type="submit" class="btn btn-warning btn-sm" disabled>
                            {{ sample.get_sample_process_display|slice:3 }} <i class="fa fa-times-circle"
                                aria-hidden="true"> </i> </button>

                        {% endif %}

                        </td> {% endcomment %}


                        <td style="padding-left: 5px; padding-right: 5px;">
    <button type="submit" class="btn btn-sm {{ sample|sample_step_color }}" disabled>
        {{ sample.get_sample_process_display|slice:3 }} 
        <i class="fa {% if sample|sample_step_color == 'btn-warning' %}fa-times-circle{% else %}fa-check{% endif %}" aria-hidden="true"></i>
    </button>
</td>
                   <td class="align-middle text-center">{{ sample.patient.jedan.jedan_name  }}</td>

                    <td style="padding-left: 5px; padding-right: 5px;" class="align-middle text-left">
                        <span data-toggle="tooltip" title="{{ sample.jedan_memo }}">
                            <a href="#"
                                class="btn btn-sm {% if sample.jedan_memo|length > 1 %}btn-outline-warning{% endif %}"
                                {% if sample.jedan_memo|length > 3 %} data-toggle="modal"
                                data-target="#memoModal{{ sample.pk }}" {% endif %}>
                                {% if sample.jedan.jedan_name == "SD" %}
                                {{ sample.patient.jedan_registered_number }}
                                {% else %}
                                {{ sample.jedan_number }}
                                {% endif %}
                            </a>
                        </span>

                        {% if sample.jedan_memo|length > 3 %}
                        <!-- Modal -->
                        <div class="modal fade" id="memoModal{{ sample.pk }}" tabindex="-1" role="dialog"
                            aria-labelledby="memoModalLabel{{ sample.pk }}" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="memoModalLabel{{ sample.pk }}"> 전달사항 </h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <!-- Modal Body -->
                                    <div class="modal-body"
                                        style="font-size: 17px; color: navy; overflow-wrap: break-word; white-space: pre-wrap;">

                                        {{ sample.jedan_memo }} <br><br>

                                        {% comment %} 관리 번호 : {{ sample.jedan_number }} [ {{ sample.tgc_s_id }} ]
                                        {% endcomment %}
                                    </div>
                                    <div class="modal-footer">
                                        <h5 class="modal-title text-green" id="memoModalLabel{{ sample.pk }}">최상의 결과를
                                            위해 최선을 다하겠습니다 </h5>
                                        <button type="button" class="btn btn-secondary"
                                            data-dismiss="modal">Close</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </td>

                    <td style="padding-left: 5px; padding-right: 5px;   " class="align-middle text-left">
                        {{ sample.patient.hospital.hospital_name }}
                        {% if sample.patient.hospital.gender_info %}<span style="color: red; font-weight: bold;">(*)</span>{% endif %}
                    </td>
                    <td style="padding-left: 5px; padding-right: 5px;  "
                        class="align-middle text-left {% if  "twin" in  sample.service_type.pdf_name  or 'tw' in sample.patient.fetus_number   %}  h2 text-red {% elif "아이" in sample.service_type.service_name %}  h2 text-primary {% endif %} ">
                        {{ sample.patient.fetus_number|slice:1}}
                        {{ sample.service_type.service_name }}</td>

                    <td class={% if sample.samples_trisomy.final_result2  == '1' %} "bg-danger text-white font-weight-bold"
                        {% elif sample.samples_trisomy.final_result2  == '2'  %} "bg-warning text-white font-weight-bold "
                        {% elif sample.samples_trisomy.final_result2  == '6'  %} "bg-primary text-white font-weight-bold "
                        {% elif sample.samples_trisomy.final_result2  == '7'  %} "bg-dark text-white font-weight-bold "
                        {% elif sample.samples_trisomy.final_result2  != '0'  %} "bg-success text-white font-weight-bold "
                        {% else %} {% endif %}>{{ sample.samples_trisomy.final_result_short2|slice:6 }}</td>

                    <td class="align-middle text-center">

                        {{ sample.samples_trisomy.not_low_aneps }}

                        {% if sample.samples_trisomy.final_result2 == '0' and  sample.samples_trisomy.not_low_aneps != '-'  %}
                        <h4 title="final is Low but any one is in High or Borderline" class="text-red"> *** 판독 확인 !!!
                        </h4>
                        {% endif %}

                    </td>

                    <td class="align-middle text-center" style="width: 50px;">{{ sample.patient_ga}}</td>

                    <td>

                        {% if sample.patient.fetus_number != 'Twin' %}

                        {% if sample.patient.doctor.sex_info   %}
                        <button class="btn btn-info btn-sm " title "성별 표시 안다:Yes "
                            onclick="showPopup(' {% url 'serve_image' image_path=sample.result_first_image %} ')"> O
                        </button>

                        {% else %}

                        <strong style="color:red;" title "성별 표시 안다:No "> &nbsp;&nbsp; X </strong>

                        {% endif %}

                        {% else %}
                        <strong style="color:#000080;" title "성별 표시 안다:Twin "> X </strong>
                        {% endif %}

                        -
                        {% if sample.gender == '1' %}

                        <button class="btn btn-success btn-sm " title "성별 표시 안다:Yes "
                            onclick="showPopup(' {% url 'serve_image' image_path=sample.result_first_image %} ')">
                            {{ sample.get_gender_display|slice:2 }} </button>

                        {% else %}
                        {{ sample.get_gender_display|slice:2 }}

                        {% endif %}

                    </td>

                    <td class="align-middle text-center {% if sample.patient.doctor.sex_info   %} text-red h5  {% endif %}"
                        style="width: 50px;">

                        {{ sample.patient.fetus_sex }} {{ sample.ff_info.apply_ff|floatformat:"0" }} &nbsp;

                        {% if sample.patient.fetus_number != 'Twin'  %}

                        {% if sample.ff_info.ff_y > 3   %}
                            <strong style="color:blue;" title="Male Sex Title">
                            M+ {{ sample.patient.doctor.male_sex }}</strong>
                        {% elif sample.ff_info.ff_y < 3  %}
                            <strong style="color:green;">F-
                            {{ sample.patient.doctor.female_sex }}</strong>
                        {% endif %}

                        {% else %}
                        *
                        {% endif %}

                    </td>

                    <td class="align-middle text-center" style="width: 50px;">
                        {% if sample.patient.fetus_number != 'Twin' and sample.ff_info.ff_y >= 1 and sample.ff_info.ff_y <= 6 %}
                            <strong style="color:#FF6B00;">* {{ sample.ff_info.ff_y|floatformat:2 }}</strong>
                        {% else %}
                            -
                        {% endif %}
                    </td>

                    <td class="align-middle" class="align-middle text-center">
                        <!-- later put pdf and jpg inside if condition -->
                        {{ sample.get_language_type_display|slice:1 }}

                        {% if sample.result_pdf %}

                        <a title="출고 PDF {{ sample.run_time }}" href="{% url 'result_pdf_down' sample.id %}"
                            class="btn btn-success btn-sm ">
                            <i class="fa fa-file-pdf" aria-hidden="true"></i>
                        </a>

                        <a href="{% url 'download_jpg_zip' sample.pk %}" class="btn btn-sm" title="출고 Image">
                            <i class="fa fa-download" style="color:blue;font-size:10px;"></i> </a>

                        <a href="{% url 'text_result_download' sample.id %}" class="btn btn-info btn-sm" title="Text Result">
                            <i class="fa fa-file-text" aria-hidden="true">text</i>
                        </a>

                        {% if sample.result_pdf_option %}
                        <a title="출고 PDF {{ sample.run_time }}" href="{% url 'result_pdf_down_optional' sample.id %}"
                            class="btn btn-warning btn-sm ">
                            <i class="fa fa-file-pdf" aria-hidden="true"></i>
                        </a>
                        {% endif %}

                        {% else %}
                        없음

                        {% endif %}

                    </td>

                    <td>
                        <form method="post" id="{{sample.id}}" class="updateform">
                            {% csrf_token %}
                            {% if user.is_admin %}
                            <a class="btn btn-outline btn-sm" role="button"
                                onclick="location.href='{% url "log_generate" sample.id %}'" download=""
                                style="margin-right: 5px; /*adjust the margin as needed*/">
                                <i class="fa fa-download"></i>
                            </a>
                            {% endif %}

                            {% if  sample.report_publish  and sample.sample_process == '9' %}

                            {{sample.result_sent_time|date:"m월d일H:i" }}
                            <button type="submit" class="btn btn-outline-danger btn-sm" disabled> 완료 <i
                                    class="fa fa-check"></i></button>
                            {% else %}

                            <input type="datetime-local" class="publish_time" name="publish_time"
                                id="time-{{sample.id}}" value='{{ sample.result_sent_time|date:"Y-m-d\TH:i:s" }}'
                                required />

                            <button type="submit" id="submitBtn-{{sample.id}}" value="{{sample.id}}"
                                {% if  sample.sample_process == '8' %} class="btn btn-primary btn-sm"> 출고 {% else %}
                                class="btn btn-warning btn-sm"> 수정
                                {% endif %}

                            </button>

                            {% endif %}

                        </form>
                    </td>

                    {% empty %}
                    <td style="margin-left: 5%;">Samples not found.</td>
                </tr>
                {% endfor %}

                {% endif %}

            </tbody>
        </table>

        <!-- Popup Modal -->
        <div id="imagePopup" class="popup">
            <span class="close" onclick="closePopup()">&times;</span>
            <img class="popup-content" id="popupImage" src="" alt="No image yet">
        </div>

        <br> &nbsp;&nbsp;&nbsp;&nbsp;
        <button type="button" class="btn btn-warning btn-sm" id="send_to_previous_step"> 2-차 판독으로 보내기
            <!-- add download icon  -->
            <i class="fa fa-backward fa-lg"></i>
        </button>

    </div>
</div>