<div class="card" style="width:100%">

    <div class="table-responsive">

        <button type="button" class="row btn btn-primary btn-lg btn-block" style="margin-bottom: 10px;">
            {{sample.tgc_s_id}} -- Fetal Fraction Information & Ref Type </button>
        <table class="table table-bordered">

            <thead class="thead-lite">
                <tr>

                    <th>
                        <h4> Seq1 </h4>
                    </th>
                    <th>
                        <h4> Seq2 </h4>
                    </th>

                    <th>
                        <h4> Enet </h4>
                    </th>
                    <th>
                        <h4> WRSC </h4>
                    </th>

                    <th>
                        <h4> Geno FF </h4>
                    </th>

                    <th class="table-success">
                        <h4> FFY </h4>
                    </th>
                    <th class="table-success">
                        <h4>GenoAI </h4>
                    </th>

                    <th class="table-success">
                        <h4>PCA AI </h4>
                    </th>

                    <th class="table-success">
                        <h4> Applied FF </h4>
                    </th>

                    <th class="">
                        <h4> Ref </h4>
                    </th>

                </tr>
            </thead>

            <tbody>

                <tr>

                    <td>{{ sample.ff_info.seq1|floatformat:2 }}</td>

                    <td>{{ sample.ff_info.seq2|floatformat:2 }}</td>

                    <td>{{ sample.ff_info.enet|floatformat:2 }}</td>
                    <td>{{ sample.ff_info.wrsc|floatformat:2 }} </td>

                    <td>{{ sample.ff_info.geno_ff|floatformat:2 }}</td>

                    <td class="table-success" style="font-weight: bold;"> {{ sample.ff_info.ff_y|floatformat:2 }} </td>
                    <td class="table-success" style="font-weight: bold;">{{ sample.ff_info.geno_ai|floatformat:2 }}</td>
                    <td>{{ sample.ff_info.pca_ai|floatformat:2 }}</td>

                    <td class="table-success"> {{ sample.ff_info.apply_ff|floatformat:2 }} </td>
                    <td class=" {% if 'pippin'  in sample.sample_dna_qc.ref_type %} text-primary {% else %} text-danger {% endif %}  h3">  {{ sample.sample_dna_qc.get_ref_type_display }} </td>

                </tr>

            </tbody>
        </table>

    </div>
</div>