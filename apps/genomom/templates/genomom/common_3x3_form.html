
    <form method="post"  id="create_form">
        {% csrf_token %}
        {{ form.non_field_errors }}

        
        <div class="row mt-5">
            {% for field in form %}
            <div class="form-group col-md-4 mt-2">
                {# Display label in bold #}
                <label for="{{ field.id_for_label }}" class="form-label">
                    <strong>{{ field.label }}</strong>
                    {% if field.field.required %}<span class="text-danger">*</span>{% endif %}
                </label>
                
                {# Display the form field itself #}
                {{ field }}
                
                {# Add Bootstrap help text styling #}
                {% if field.help_text %}
                <small class="form-text text-muted">{{ field.help_text }}</small>
                {% endif %}
                
                {# Display errors in red using Bootstrap #}
                {% if field.errors %}
                <div class="text-danger">
                    {% for error in field.errors %}
                    <small>{{ error }}</small><br>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <button type="submit" class="mt-3 btn btn-primary">
            <i class="fas fa-save"></i> Save
        </button>
    </form>

    <hr><hr>
    <br>


