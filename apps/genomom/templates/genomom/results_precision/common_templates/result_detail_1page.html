<div>

    {% if sample.gender == '1' or sample.patient.hospital.gender_info %}

        <table>
            <tr>
                <th class="bg_green" style="width:20%"> 검 사 결 과 요 약 </th>
                <th style="width:10%"></th>
                <th style="width:40%"></th>
                <th style="width:15%"></th>
                <th style="width:15%"></th>
            </tr>

            <tr>
                <td colspan="2" class="bg_gray">염색체 이수성 </td>
                <td class="bg_gray"> 염 색 체 이상 항목 </td>
                <td class="bg_gray"> Fetal Fraction (%)</td>
                <td class="bg_gray"> {% if sample.patient.fetus_number == 'Twin' %}  Y 염색체  {% else %} Fetal Sex {% endif %}
                </td>

            </tr>

            <tr style="height:18mm; text-align:center;">
                <td colspan="2" class={% if sample.samples_trisomy.final_result2  == '0' %} "main_result_low"
                    {% elif sample.samples_trisomy.final_result2  == '1' %} "main_result_high"
                    {% else %} "main_result_borderline" {% endif %}>

                    <strong> {{sample.samples_trisomy.get_final_result2_display}} </strong> </td>

                <td style="text-align:center;">{{ sample.samples_trisomy.samples_not_low }} </td>

                <td style="text-align:center;"> {{sample.ff_info.apply_ff|floatformat:"2"}} </td>

                <td style="text-align:center;">
                    {% if sample.patient.fetus_sex == '+' %}
                    {% if sample.patient.fetus_number == 'Twin'  %} 검출  {% else %}  {{ sample.patient.doctor.male_sex_new|default_if_none:""  }}   {% endif %}
                    {% elif sample.patient.fetus_sex == '-'  %}
                    {% if  sample.patient.fetus_number == 'Twin' %} 미검출  {% else %}   {{ sample.patient.doctor.female_sex_new|default_if_none:""  }} {% endif %}
                    {% else %}
                    -
                    {% endif %}

                </td>

            </tr>
        </table>

    {% else %}

    <table>
        <tr>
            <th class="bg_green" style="width:20%"> 검 사 결 과 요 약 </th>
            <th style="width:10%"></th>
            <th style="width:40%"></th>
            <th style="width:30%"></th>

        </tr>

        <tr>
            <td colspan="2" class="bg_gray">염색체 이수성 </td>
            <td class="bg_gray"> 염 색 체 이상 항목 </td>
            <td class="bg_gray"> Fetal Fraction (%)</td>

        </tr>

        <tr style="height:18mm; text-align:center;">
            <td colspan="2" class={% if sample.samples_trisomy.final_result2  == '0' %} "main_result_low"
                {% elif sample.samples_trisomy.final_result2  == '1' %} "main_result_high"
                {% else %} "main_result_borderline" {% endif %}>

                <strong> {{sample.samples_trisomy.get_final_result2_display}} </strong> </td>

            <td style="text-align:center;">{{ sample.samples_trisomy.samples_not_low }} </td>
            <td style="text-align:center;">

                <!-- 단태아 일경우 조건에 맞게 XY/XX, +/-, + 표시    biggest_ff changhe to apply_ff-->
                {% if  sample.patient.fetus_number != 'Twin'  %}
                    {% if  sample.patient.doctor.sex_info %}
                        {% if '라헬' in sample.patient.hospital.hospital_name and 'v_twin' in sample.patient.fetus_number %}
                            {{sample.ff_info.apply_ff|floatformat:"2"}}
                        {% else %}
                                {% if sample.patient.doctor.male_sex == '1' or  sample.patient.doctor.female_sex == '2' %}
                                <!--  last digit of FF to indicate male or female  -->
                                    {{sample.ff_info.apply_ff|floatformat:"1"}}{% if sample.patient.fetus_sex == '+' %}{{sample.patient.doctor.male_sex|default_if_none:"" }}
                                    {% elif  sample.patient.fetus_sex == '-' %}{{sample.patient.doctor.female_sex|default_if_none:"" }}
                                    {% else %}
                                    {% endif %}
                                {% else %} 
                                        {{sample.ff_info.apply_ff|floatformat:"2"}}
                                        <!--  Add indicator on the basis of doctor table setting  -->
                                        {% if sample.patient.fetus_sex == '+' %} {{sample.patient.doctor.male_sex|default_if_none:" "  }}
                                        {% elif   sample.patient.fetus_sex == '-' %} {{sample.patient.doctor.female_sex|default_if_none:" "  }}
                                        {% else %} {% endif %}


                                {% endif %}
                        {% endif %}

                    {% else %}

                         {{sample.ff_info.apply_ff|floatformat:"2"}}

                    {% endif %}

                {% else %}

                    {{sample.ff_info.apply_ff|floatformat:"2"}}

                {% endif %}

            </td>
        </tr>
    </table>

    {% endif %}

</div>