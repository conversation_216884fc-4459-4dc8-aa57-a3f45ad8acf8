{% extends "layouts/base.html" %}

{% block title %} Edit Sample {% endblock %}

<!-- Specific Page CSS goes HERE  -->
{% block stylesheets %}

{% endblock stylesheets %}

{% block content %}

{% include 'genomom/common/search_patient_popup.html' %}

<!-- Header -->
<div class="header pb-6">
    <div class="container-fluid">
        <div class="header-body">
            <div class="row align-items-center py-4">
                <div class="col-lg-6 col-7">
                    <h6 class="h2 d-inline-block mb-0">Sample Entry</h6>
                    <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                        <ol class="breadcrumb breadcrumb-links">
                            <li class="breadcrumb-item"><a href="#"><i class="fas fa-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="#">Dashboards</a></li>
                            <li class="breadcrumb-item active" aria-current="page">entry</li>
                        </ol>
                    </nav>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Page content -->
<div class="container-fluid mt--6">

    <div class="card mb-4">
        <!-- Card header -->
        <div class="card-header ">
            <h3 class="mb-0"> 등록된 정보 </h3>

        </div>

        <!-- Card body -->
        <div class="card-body">

            <!-- Form groups used in grid -->
            {% if messages %}
            {% for message in messages %}

            <div class="alert alert-{{message.tags}} alert-dismissible fade show" role="alert">
                <span class="alert-icon"><i class="ni ni-like-2"></i></span>
                <span class="alert-text"><strong>{{ message.tags }}</strong> {{ message }} </span>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            {% endfor %}
            {% endif %}

            {% if patient %}

            <button type="button" class="row btn btn-primary btn-lg btn-block" style="margin-bottom: 10px;">

                Patient Related Informations ( To Change Jedan, Jedan branch & hospital related information contact
                Entry staff )
            </button>

            <br>

            <div class="card">
                <table class="table table-bordered">
                    <thead class="thead-lite">
                        <tr>
                            <th>
                                <h4> {{ patient.jedan.jedan_name}} </h4>
                            </th>

                            <th>
                                <h4> {{ patient.jedan_branch.jedan_branch_name}} </h4>
                            </th>

                            <th class="bg-info">
                                <h4> {{ patient.tgc_p_id }} </h4>
                            </th>

                            <th>
                                <h4> {{ patient.hospital.hospital_name }} </h4>
                            </th>

                            <th>
                                <h4> {{ patient.doctor.full_name }} </h4>
                            </th>

                        </tr>
                    </thead>
                </table>
            </div>

            {% endif %}

            <form method="post" class="form-visibility" enctype="multipart/form-data">
                {% csrf_token %}

                <!-- this for top level design -->
                <div class="row">

                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.jedan_registered_date.id_for_label }}"
                            class="form-label">{{ patient_form.jedan_registered_date.label }}</label>
                        {{ patient_form.jedan_registered_date }}
                        {% if patient_form.jedan_registered_date.errors %}
                        <div class="text-danger">{{ patient_form.jedan_registered_date.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.jedan_registered_number.id_for_label }}"
                            class="form-label">{{patient_form.jedan_registered_number.label }}</label>
                        {{ patient_form.jedan_registered_number }}
                        {% if patient_form.jedan_registered_number.errors %}
                        <div class="text-danger">{{ patient_form.jedan_registered_number.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-2">
                        <label for="{{ sample_form.service_type.id_for_label }}"
                            class="form-label">{{ sample_form.service_type.label }}</label>
                        {{ sample_form.service_type }}
                        {% if sample_form.service_type.errors %}
                        <div class="text-danger">{{ sample_form.service_type.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.doctor_depart.id_for_label }}"
                            class="form-label">{{patient_form.doctor_depart.label }}</label>
                        {{ patient_form.doctor_depart }}
                        {% if patient_form.doctor_depart.errors %}
                        <div class="text-danger">{{ patient_form.doctor_depart.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.hospital_chart.id_for_label }}"
                            class="form-label">{{ patient_form.hospital_chart.label }}</label>
                        {{ patient_form.hospital_chart }}
                        {% if patient_form.hospital_chart.errors %}
                        <div class="text-danger">{{ patient_form.hospital_chart.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.last_name.id_for_label }}"
                            class="form-label">{{ patient_form.last_name.label }}</label>
                        {{ patient_form.last_name }}
                        {% if patient_form.last_name.errors %}
                        <div class="text-danger">{{ patient_form.last_name.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.first_name.id_for_label }}"
                            class="form-label">{{ patient_form.first_name.label }}</label>
                        {{ patient_form.first_name }}
                        {% if patient_form.first_name.errors %}
                        <div class="text-danger">{{ patient_form.first_name.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.patient_age.id_for_label }}"
                            class="form-label">{{ patient_form.patient_age.label }}</label>
                        {{ patient_form.patient_age }}
                        {% if patient_form.patient_age.errors %}
                        <div class="text-danger">{{ patient_form.patient_age.errors|join:", " }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.patient_height.id_for_label }}"
                            class="form-label">{{ patient_form.patient_height.label }}</label>
                        {{ patient_form.patient_height }}
                        {% if patient_form.patient_height.errors %}
                        <div class="text-danger">{{ patient_form.patient_height.errors|join:", " }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.patient_weight.id_for_label }}"
                            class="form-label">{{ patient_form.patient_weight.label }}</label>
                        {{ patient_form.patient_weight }}
                        {% if patient_form.patient_weight.errors %}
                        <div class="text-danger">{{ patient_form.patient_weight.errors|join:", " }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.patient_bmi.id_for_label }}"
                            class="form-label">{{ patient_form.patient_bmi.label }}</label>
                        {{ patient_form.patient_bmi }}
                        {% if patient_form.patient_bmi.errors %}
                        <div class="text-danger">{{ patient_form.patient_bmi.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-2">
                        <label for="{{ sample_form.patient_ga.id_for_label }}"
                            class="form-label">{{ sample_form.patient_ga.label }}</label>
                        {{ sample_form.patient_ga }}
                        {% if sample_form.patient_ga.errors %}
                        <div class="text-danger">{{ sample_form.patient_ga.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.ivf_treatment.id_for_label }}"
                            class="form-label">{{ patient_form.ivf_treatment.label }}</label>
                        {{ patient_form.ivf_treatment }}
                        {% if patient_form.ivf_treatment.errors %}
                        <div class="text-danger">{{ patient_form.ivf_treatment.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.ivf_number.id_for_label }}"
                            class="form-label">{{ patient_form.ivf_number.label }}</label>
                        {{ patient_form.ivf_number }}
                        {% if patient_form.ivf_number.errors %}
                        <div class="text-danger">{{ patient_form.ivf_number.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.fetus_number.id_for_label }}"
                            class="form-label">{{ patient_form.fetus_number.label }}</label>
                        {{ patient_form.fetus_number }}
                        {% if patient_form.fetus_number.errors %}
                        <div class="text-danger">{{ patient_form.fetus_number.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.twin_memo.id_for_label }}"
                            class="form-label">{{ patient_form.twin_memo.label }}</label>
                        {{ patient_form.twin_memo }}
                        {% if patient_form.twin_memo.errors %}
                        <div class="text-danger">{{ patient_form.twin_memo.errors|join:", " }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.vanishing_twin_memo.id_for_label }}"
                            class="form-label">{{ patient_form.vanishing_twin_memo.label }}</label>
                        {{ patient_form.vanishing_twin_memo }}
                        {% if patient_form.vanishing_twin_memo.errors %}
                        <div class="text-danger">{{ patient_form.vanishing_twin_memo.errors|join:", " }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.nt_size.id_for_label }}"
                            class="form-label">{{ patient_form.nt_size.label }}</label>
                        {{ patient_form.nt_size }}
                        {% if patient_form.nt_size.errors %}
                        <div class="text-danger">{{ patient_form.nt_size.errors|join:", " }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.ultrasound_report.id_for_label }}"
                            class="form-label">{{ patient_form.ultrasound_report.label }}</label>
                        {{ patient_form.ultrasound_report }}
                        {% if patient_form.ultrasound_report.errors %}
                        <div class="text-danger">{{ patient_form.ultrasound_report.errors|join:", " }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.seng_hwahag_test.id_for_label }}"
                            class="form-label">{{ patient_form.seng_hwahag_test.label }}</label>
                        {{ patient_form.seng_hwahag_test }}
                        {% if patient_form.seng_hwahag_test.errors %}
                        <div class="text-danger">{{ patient_form.seng_hwahag_test.errors|join:", " }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.seng_hwahag_memo.id_for_label }}"
                            class="form-label">{{ patient_form.seng_hwahag_memo.label }}</label>
                        {{ patient_form.seng_hwahag_memo }}
                        {% if patient_form.seng_hwahag_memo.errors %}
                        <div class="text-danger">{{ patient_form.seng_hwahag_memo.errors|join:", " }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.aneuploidy_history.id_for_label }}"
                            class="form-label">{{ patient_form.aneuploidy_history.label }}</label>
                        {{ patient_form.aneuploidy_history }}
                        {% if patient_form.aneuploidy_history.errors %}
                        <div class="text-danger">{{ patient_form.aneuploidy_history.errors|join:", " }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group col-md-2">
                        <label for="{{ patient_form.aneup_history_memo.id_for_label }}"
                            class="form-label">{{ patient_form.aneup_history_memo.label }}</label>
                        {{ patient_form.aneup_history_memo }}
                        {% if patient_form.aneup_history_memo.errors %}
                        <div class="text-danger">{{ patient_form.aneup_history_memo.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-2">
                        <label for="{{ sample_form.result_pdf.id_for_label }}"
                            class="form-label">{{ sample_form.result_pdf.label }}</label>
                        {{ sample_form.result_pdf }}
                        {% if sample_form.result_pdf.errors %}
                        <div class="text-danger">{{ sample_form.result_pdf.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-2">
                        <label for="{{ sample_form.fastq_upload.id_for_label }}"
                            class="form-label">{{ sample_form.fastq_upload.label }}</label>
                        {{ sample_form.fastq_upload }}
                        {% if sample_form.fastq_upload.errors %}
                        <div class="text-danger">{{ sample_form.fastq_upload.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                </div>

                <button type="button" class="row btn btn-primary btn-lg btn-block" style="margin-bottom: 10px;">

                    Sample Related Info (환자 정보)
                </button>

                <div class="row">

                    <div class="form-group col-md-1" {% if re_draw %} style="display:none" {% endif %}>
                        <label for="{{ sample_form.test_type.id_for_label }}"
                            class="form-label">{{ sample_form.test_type.label }}</label>
                        {{ sample_form.test_type }}
                        {% if sample_form.test_type.errors %}
                        <div class="text-danger">{{ sample_form.test_type.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-1">
                        <label for="{{ sample_form.sample_process.id_for_label }}"
                            class="form-label">{{ sample_form.sample_process.label }}</label>
                        {{ sample_form.sample_process }}
                        {% if sample_form.sample_process.errors %}
                        <div class="text-danger">{{ sample_form.sample_process.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-1">
                        <label for="{{ sample_form.sample_type.id_for_label }}"
                            class="form-label">{{ sample_form.sample_type.label }}</label>
                        {{ sample_form.sample_type }}
                        {% if sample_form.sample_type.errors %}
                        <div class="text-danger">{{ sample_form.sample_type.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-1">
                        <label for="{{ sample_form.tube_og.id_for_label }}"
                            class="form-label">{{ sample_form.tube_og.label }}</label>
                        {{ sample_form.tube_og }}
                        {% if sample_form.tube_og.errors %}
                        <div class="text-danger">{{ sample_form.tube_og.errors|join:", " }}</div>
                        {% endif %}

                    </div>

                    <div class="form-group col-md-1">
                        <label for="{{ sample_form.gender.id_for_label }}"
                            class="form-label">{{ sample_form.gender.label }}</label>
                        {{ sample_form.gender }}
                        {% if sample_form.gender.errors %}
                        <div class="text-danger">{{ sample_form.gender.errors|join:", " }}</div>
                        {% endif %}

                    </div> 

                    <div class="form-group col-md-1  text-center">
                        <label for="{{ patient_form.fetus_sex.id_for_label }}"
                            class="form-label text-red h4">{{ patient_form.fetus_sex.label }}</label>
                        {{ patient_form.fetus_sex }}
                        {% if patient_form.fetus_sex.errors %}
                        <div class="text-danger">{{ patient_form.fetus_sex.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-1">
                        <label for="{{ sample_form.is_active.id_for_label }}"
                            class="form-label">{{ sample_form.is_active.label }}</label>
                        {{ sample_form.is_active }}
                        {% if sample_form.is_active.errors %}
                        <div class="text-danger">{{ sample_form.is_active.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-1">
                        <label for="{{ sample_form.report_publish.id_for_label }}"
                            class="form-label">{{ sample_form.report_publish.label }}</label>
                        {{ sample_form.report_publish }}
                        {% if sample_form.report_publish.errors %}
                        <div class="text-danger">{{ sample_form.report_publish.errors|join:", " }}</div>
                        {% endif %}
                    </div>


                    <div class="form-group col-md-1">
                        <label for="{{ sample_form.is_manual.id_for_label }}"
                            class="form-label">{{ sample_form.is_manual.label }}</label>
                        {{ sample_form.is_manual }}
                        {% if sample_form.is_manual.errors %}
                        <div class="text-danger">{{ sample_form.is_manual.errors|join:", " }}</div>
                        {% endif %}
                    </div>





                    <div class="form-group col-md-1">
                        <label for="{{ sample_form.is_re_checked.id_for_label }}"
                            class="form-label">{{ sample_form.is_re_checked.label }}</label>
                        {{ sample_form.is_re_checked }}
                        {% if sample_form.is_re_checked.errors %}
                        <div class="text-danger">{{ sample_form.is_re_checked.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-1">
                        <label for="{{ sample_form.extraction_date.id_for_label }}"
                            class="form-label">{{ sample_form.extraction_date.label }}</label>
                        {{ sample_form.extraction_date }}
                        {% if sample_form.extraction_date.errors %}
                        <div class="text-danger">{{ sample_form.extraction_date.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-1">
                        <label for="{{ sample_form.entry_date.id_for_label }}"
                            class="form-label">{{ sample_form.entry_date.label }}</label>
                        {{ sample_form.entry_date }}
                        {% if sample_form.entry_date.errors %}
                        <div class="text-danger">{{ sample_form.entry_date.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-1">
                        <label for="{{ sample_form.test_date.id_for_label }}"
                            class="form-label">{{ sample_form.test_date.label }}</label>
                        {{ sample_form.test_date }}
                        {% if sample_form.test_date.errors %}
                        <div class="text-danger">{{ sample_form.test_date.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-1">
                        <label for="{{ sample_form.result_sent_time.id_for_label }}"
                            class="form-label">{{ sample_form.result_sent_time.label }}</label>
                        {{ sample_form.result_sent_time }}

                        {% if sample_form.result_sent_time.errors %}
                        <div class="text-danger">{{ sample_form.result_sent_time.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    

                    <div class="form-group col-md-1">
                        <label for="{{ sample_form.language_type.id_for_label }}"
                            class="form-label">{{ sample_form.language_type.label }}</label>
                        {{ sample_form.language_type }}
                        {% if sample_form.language_type.errors %}
                        <div class="text-danger">{{ sample_form.language_type.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-3">
                        <label for="{{ sample_form.jedan_memo.id_for_label }}"
                            class="form-label">{{ sample_form.jedan_memo.label }}</label> <strong class="text-warning">
                            재단 전달용 </strong>
                        {{ sample_form.jedan_memo }}
                        {% if sample_form.jedan_memo.errors %}
                        <div class="text-danger">{{ sample_form.jedan_memo.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-3 text-center">
                        <label for="{{ sample_form.sample_memo.id_for_label }}"
                            class="form-label">{{ sample_form.sample_memo.label }} </label> <strong
                            class="text-warning"> 내부 전달용 </strong>
                        {{ sample_form.sample_memo }}
                        {% if sample_form.sample_memo.errors %}
                        <div class="text-danger">{{ sample_form.sample_memo.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group col-md-4">
                        <label for="{{ patient_form.clinical_note.id_for_label }}"
                            class="form-label">{{ patient_form.clinical_note.label }}</label> <strong
                            class="text-warning"> 병원에서 환저용 전달 </strong>
                        {{ patient_form.clinical_note }}
                        {% if patient_form.clinical_note.errors %}
                        <div class="text-danger">{{ patient_form.clinical_note.errors|join:", " }}</div>
                        {% endif %}
                    </div>

                </div>

                <div class="row">

                    <div class="col-md-2">
                        <div class="form-group" style="text-align: left;">
                            <button type="submit" name="save_and_previous" value="save_and_previous"
                                class="btn btn-primary">저장 및 이전페이지 </button>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group" style="text-align: left;">
                            <button type="submit" class="btn btn-primary"> 저장 및 홈 </button>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group" style="text-align: left;">
                            <button type="submit" name="save_and_db" value="save_and_db" class="btn btn-primary"> 저장
                                및 데이터베이스</button>
                        </div>
                    </div>
                </div>
            </form>

            <div id="message">
            </div>

        </div>
    </div>
    {% include 'includes/footer.html' %}
</div>

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}

<script>

</script>

{% endblock javascripts %}