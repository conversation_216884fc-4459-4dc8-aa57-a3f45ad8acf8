from django.db import models
from .sample import SampleInfo
#from simple_history.models import HistoricalRecords


class FF_info(models.Model):
    """
    Store Fetal Fraction Information in Data Base
    rename to ff_info.py 
    Remove all Defaults value Later
    """
    sample = models.OneToOneField(SampleInfo,related_name="ff_info",on_delete=models.CASCADE)
    geno_ai = models.FloatField(verbose_name="Geno_AI", null = True ) 
    pca_ai = models.FloatField(verbose_name="PCA_AI", null=True)


    geno_ff = models.FloatField(verbose_name="Geno_FF", null=True )
    seq1 = models.FloatField(verbose_name="Seq1", null=True)
    seq2 = models.FloatField(verbose_name="Seq2", null=True)
    ff_y    = models.FloatField(verbose_name="FF Y", null = True)
    enet = models.FloatField(verbose_name="Enet", null=True)
    wrsc = models.FloatField(verbose_name="WRSC", null=True)
    
    apply_ff =  models.FloatField(verbose_name="Result FF", null=True)
    
    # ! Result send time is created at + 1 Day  # auto_now_add=True
    #!sample_memo = models.CharField(max_length=250, verbose_name= "Sample Memo", default = "-")
    
    created_at = models.DateTimeField(auto_now=True)
    last_edited = models.DateField(verbose_name="최근 수정", auto_now=True)  # ! default time
    
    #history = HistoricalRecords()

    def save(self, *args, **kwargs):
        """
        apply_ff is the ff that will display in pdf file, so in such case use can freely change this value.
        if ff_y exist then ff_y will apply , other wise geno_ai will apply 
        
        while loading random data this function is causing error so.
        i applied try and catch, in real data this try and catch not required
        
        """
        try:
            #! if want to apply biggest in genoffai or ffy 
            apply_ff = round(self.ff_y, 3) if self.ff_y  > self.geno_ai else round( self.geno_ai, 3)

            #! if ffy apply if exist
            #apply_ff = self.ff_y if self.ff_y  >  5 else round( self.geno_ai, 3)

            if not self.apply_ff :
                self.apply_ff = apply_ff     


            
            # if not self.apply_ff :
            #     if self.ff_y > 5 :
            #         self.apply_ff = self.ff_y 
            #     else:
            #         self.apply_ff = self.geno_ai


            super().save(*args, **kwargs)
        except:
            pass

    
    class Meta:
        verbose_name = "5.Fetal Fraction 정보"
        verbose_name_plural = "5.Fetal Fraction 정보"


    def sample_id(self):
        " TGCS ID "
        return self.sample.tgc_s_id
    
    def patient_name(self) :
        "Patient Name "
        return self.sample.patient.first_name
    
    def hospital_name(self) :
        return self.sample.patient.hospital.hospital_name
    
    def smallest_ff(self):
        """
        this will return smallest FF among Seq1 or apply_ff
        """
        return self.seq1 if self.apply_ff > self.seq1 else self.apply_ff
    
    def biggest_ff(self):
        """
        this will return smallest FF among Seq1 or apply_ff
        we will ignore seq 
        """
        return round(self.ff_y, 3) if self.ff_y  > self.geno_ai else round( self.geno_ai, 3)
    
    def ffy_difference(self ) :
        """
        this will check weather there is a big gap between FFY and Sample 
        this will ignore female case and ffy less than 5 
        """
        DIFFERENT = 10
        try :
            if self.ff_y > 3:
                difference = abs( self.ff_y - self.geno_ai )
                
                if difference > DIFFERENT :
                    return  round(difference,1)
                else :
                    return 0 
            else :
                return  0 
        except Exception as err :
            return difference 

    def ffy_risk_zone(self ):
        """
        after pippin data if ffy is around 4,5,6 then this is Considered Risk zone 
        """
        RISK_MIN = 7 #! if FFy below 8 then this will display red warning in confirm page 
        
        try:
            if self.ff_y > 3  and self.ff_y < RISK_MIN :
                return True
                
            else:
                return False
        except Exception as err:
            return False
