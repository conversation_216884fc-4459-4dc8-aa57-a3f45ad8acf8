from django.db import models
from ..models import SampleInfo
class Trisomy(models.Model):

    choice_trisomy = (
        ('0', 'Low Risk'),
        ('1', 'High Risk'),
        ('2', 'Borderline'),
    )
    
    #! Dont Change this order 

    FINAL_CHOICES  =(
                ('0', 'Low Risk'),
                ('1', 'High Risk'),
                ('2', 'Borderline'),
                
                ("7", "검사불능"),
                
                
                ('a', "Retest_V2_ReSeq"),    #! DNA Seq
                ("b", "Retest_V3_ReSeqLib"), #! DNA Lib
                ("c", "Retest_V4_RePrep"),  # ! DNA prep
                ("d", "Retest_V5_gm"),  # ! DNA Seq
                ("e", "Retest_V6_CS"),  # ! DNA prep
                
                ("f", "Retest_V2+V3"),  # ! V2 : DNA Seq, V3 : DNA Lib
                ("g", "Retest_V2+V4"),  # ! V2 : DNA Seq, V4 : DNA prep
                ("h", "Retest_V3+V4"),  # ! V3 : DNA Lib, V4 : DNA prep
                
                
                
                
                ("6", "Re_draw"),
        )
    
    #! in the previously re-test were 4,5,6 not using number is greater than 9 , db is set to char 1 so used char
    
    
    sample = models.OneToOneField( SampleInfo,  on_delete=models.CASCADE, related_name="samples_trisomy")
    
    t1 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t1')
    t2 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t2')
    t3 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t3')
    t4 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t4')
    t5 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t5')
    t6 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t6')
    t7 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t7')
    t8 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t8')
    t9 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t9')
    t10 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t10')
    t11 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t11')
    t12 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t12')
    t13 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t13')
    t14 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t14')
    t15 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t15')
    t16 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t16')
    t17 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t17')
    t18 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t18')
    t19 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t19')
    t20 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t20')
    t21 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t21')
    t22 = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='t22')
    xo = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='xo')
    xxy = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='xxy')
    xxx = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='xxx')
    xyy = models.CharField(max_length=1,choices=choice_trisomy, default='0', verbose_name='xyy')
    
    
    final_result1 = models.CharField(max_length=1 , verbose_name="1-차 판독" , choices= FINAL_CHOICES)
    
    #! Sample Final Result 
    final_result2 = models.CharField(max_length=1 , verbose_name="2-차 판독" , choices= FINAL_CHOICES, blank=True )
    #! IN Visualization or in other case we need Samples Not Low Data base column, after 2-차 판독 auto Create this column
    
    memo_1 = models.CharField(max_length=256,verbose_name="1-차 판독 메모",  default='-')
    memo_2 = models.CharField(verbose_name="2-차 판독 메모", max_length=256,  default='-')
    

    persons_1 = models.CharField(max_length=256,verbose_name="1-차 판독자" , default='', null=True, blank=True )
    persons_2 = models.CharField(max_length=256,verbose_name="2-차 판독자" , default='', null=True, blank=True  )
    


    #! Create 2 seprate columns for done or not
    status1=models.BooleanField( verbose_name="1-차 판독 Status", default=False, help_text= "1-차 판독 완료 되면 True ")
    status2 = models.BooleanField(verbose_name="2-차 판독 Status", default=False, help_text="2-차 판독 완료 되면 True ")
    
    # ! Result send time is created at + 1 Day  # auto_now_add=True
    created_at = models.DateTimeField(auto_now_add=True)
    
    
    def final_result_short(self):
        result_steps  =[
                'Low','High','Border',"a", "Re_P", "Re_L", "Re_S", "Re_draw","검사불능"]
        
        #return result_steps[int(self.final_result1)] if self.final_result1 else ''
        return self.get_final_result1_display()
    
    
    def final_result_short2(self):
    
        result_steps  =[
                'Low','High','Borderline',"Re_P", "Re_L", "Re_S", "Re_draw","검사불능", ""]
        
        try:
            return result_steps[int(self.final_result2)] if self.final_result2 else ''
        except :
            return f"{self.final_result2}**" 
    


    def not_low_aneps(self):
        """
        this function is just copy version of samples_not_low 
        only one difference is here we only receive name 
        this function is using in 판독 page
        """
        low_dict =  {  '0': 'Low','1': 'High','2': 'Border'}
        
        aca = [f"t{t_number}" for t_number in range(1, 23)] 
        sca =  ["xo", "xxy", "xxx", "xyy"  ] 
        
        
        result = ""
        for t_number in aca :
            # Extract the values of all samples
            risk_value = getattr(self, t_number )
            
            if risk_value != "0":
                result += t_number.lstrip('t') + ', '  # .lstrip('t')
            
        for  sca_name in sca:
            risk_value = getattr(self, sca_name)
            
            if risk_value != "0":
                result +=  sca_name.upper() + ', ' 
                
        #! Check weather CNV Exists or Not 
        if self.sample.cnv_info.all():
            for cnv in self.sample.cnv_info.all().order_by('-id'):
                try:
                    result += cnv.ppv_npv.name + ', '
                    
                    #result += cnv.cnv_name + ', ' 
                    
                    
                except Exception as err:
                    continue 

        return result.rstrip(', ') if len(result) > 2 else '-'



    
    def samples_not_low(self):
        """
        we will return samples which are not Low as a String Format
        this function will be displayed in confirm 1, 2 page 
        we will display not Low samples, So user can easily understand
        
        """
        low_dict =  {  '0': 'Low','1': 'High','2': 'Border'}
        
        aca = [f"t{t_number}" for t_number in range(1, 23)] 
        sca =  ["xo", "xxy", "xxx", "xyy"  ] 
        
        
        result = ""
        for t_number in aca :
            # Extract the values of all samples
            risk_value = getattr(self, t_number )
            
            if risk_value != "0":
                result += t_number.lstrip('t') + '번 삼염색체증, '  # .lstrip('t')
            
        for  sca_name in sca:
            risk_value = getattr(self, sca_name)
            
            if risk_value != "0":
                result +=  sca_name.upper() + ', ' 
                
        #! Check weather CNV Exists or Not 
        if self.sample.cnv_info.all():
            for cnv in self.sample.cnv_info.order_by('-id'):
                
                # result += cnv.ppv_npv.name_long + ', '
                result += cnv.cnv_name + ', '

                    

        return result.rstrip(', ') if len(result) > 2 else '-'
    
    def samples_not_low_eng(self):
        """
        we will return samples which are not Low as a String Format
        this function will be displayed in confirm 1, 2 page 
        we will display not Low samples, So user can easily understand
        
        """
        low_dict =  {  '0': 'Low','1': 'High','2': 'Border'}
        
        aca = [f"t{t_number}" for t_number in range(1, 23)] 
        sca =  ["xo", "xxy", "xxx", "xyy"  ] 
        
        
        result = ""
        for t_number in aca :
            # Extract the values of all samples
            risk_value = getattr(self, t_number )
            
            if risk_value != "0":
                result += 'Trisomy ' + t_number.lstrip('t') + ','  # .lstrip('t')
            
        for  sca_name in sca:
            risk_value = getattr(self, sca_name)
            
            if risk_value != "0":
                result +=  sca_name.upper() + ', ' 
                
        #! Check weather CNV Exists or Not 
        if self.sample.cnv_info.all():
            for cnv in self.sample.cnv_info.all().order_by('-id'):
                
                # result += cnv.ppv_npv.name_long + ', '
                result += cnv.cnv_name + ', '

                    

        return result.rstrip(', ') if len(result) > 2 else '-'
    



    def sample_result(self) :
        return "Low"
    
    
    def sample_high_borderlines(self):
        """
        In our Result page we need to print seprate value for High Risk and Border line
        like 21 번 고위면 , 18 번 임계치 의험 ....
        2024.08.06 검사1본부의 요청으로 임계치 => 경계치 
        
        """
        
        low_dict = {'0': 'Low', '1': '고위험', '2': '경계치 위험'}

        aca = [f"t{t_number}" for t_number in range(1, 23)]
        sca = ["xo", "xxy", "xxx", "xyy"]

        result = ""
        for t_number in aca:
            # Extract the values of all samples
            risk_value = getattr(self, t_number)

            
            if risk_value == '1' : #! High Risk
                result += t_number.lstrip('t') + '번 삼염색체증 고위험, '  # .lstrip('t')
            elif risk_value == '2': #! Border Line
                result += t_number.lstrip('t') + '번 삼염색체증 경계치 위험, '

        for sca_name in sca:
            risk_value = getattr(self, sca_name)

            if risk_value == '1':  # ! High Risk
                result += sca_name.upper() + ' 고위험, '  # .lstrip('t')
            elif risk_value == '2': #! Border Line
                result += sca_name.upper() + '경계치 위험, '
        
        
        #! Check weather CNV Exists or Not 
        if self.sample.cnv_info.all():
            for cnv in self.sample.cnv_info.order_by('-id'):
                if cnv.cnv_final_result =='1':
                    # result += cnv.ppv_npv.name_long + ' 고위험, '
                    result += cnv.cnv_name + ' 고위험, '
                    
                    
                elif cnv.cnv_final_result == '2':
                    # result += cnv.ppv_npv.name_long + '경계치 위험, '
                    result += cnv.cnv_name + '경계치 위험, '
        
        
        return  result.rstrip(', ') if len(result) > 2 else ''
    
    
    def sample_high_borderlines_eng(self):
        """
        In our Result page we need to print seprate value for High Risk and Border line
        like 21 번 고위면 , 18 번 경계치 의험 ....
        
        """
        
        low_dict = {'0': 'Low', '1': 'High Risk', '2': 'Borderline'}

        aca = [f"t{t_number}" for t_number in range(1, 23)]
        sca = ["xo", "xxy", "xxx", "xyy"]

        result = ""
        for t_number in aca:
            # Extract the values of all samples
            risk_value = getattr(self, t_number)

            
            if risk_value == '1' : #! High Risk
                result += 'Trisomy '+  t_number.lstrip('t') +  'is High Risk, '  # .lstrip('t')
            elif risk_value == '2': #! Border Line
                result += "Trisomy " + t_number.lstrip('t') + ' is Borderline, '

        for sca_name in sca:
            risk_value = getattr(self, sca_name)

            if risk_value == '1':  # ! High Risk
                result += sca_name.upper() + ' High Risk, '  # .lstrip('t')
            elif risk_value == '2': #! Border Line
                result += sca_name.upper() + 'Borderline, '
        
        
        #! Check weather CNV Exists or Not 
        if self.sample.cnv_info.all():
            for cnv in self.sample.cnv_info.all().order_by('-id'):
                if cnv.cnv_final_result =='1':
                    # result += cnv.ppv_npv.name + ' High Risk, '
                    result += cnv.cnv_name + '  High Risk, '
                    
                elif cnv.cnv_final_result == '2':
                    #result += cnv.ppv_npv.name + 'Borderline, '
                    result += cnv.cnv_name + ' Borderline, '
        
        
        return  result.rstrip(', ') if len(result) > 2 else ''


    
    def aneuploidy(self):
        """
        in Result pdf File Name we need to Write Aneuploidy Info like file_name_T21.pdf
        for cnv we will discuss later
        
        if 2-차 판독 is Fail then return 검사불능 no need to check other
        
        #! Result PDF 
        """      
        if self.final_result2 == "7":
            return "_검사불능"
        
        aca = [f"t{t_number}" for t_number in range(1, 23)]
        sca = ["xo", "xxy", "xxx", "xyy" ]
        
        result = ""
        for t_number in aca:
            # Extract the values of all samples
            risk_value = getattr(self, t_number)

            if risk_value != "0":
                result += t_number.upper() + ','  # .lstrip('t')

        for sca_name in sca:
            risk_value = getattr(self, sca_name)

            if risk_value != "0":
                result += sca_name.upper() + ','
                
        #! Check weather CNV Exists or Not
        if self.sample.cnv_info.all():
            for cnv in self.sample.cnv_info.all():
                if cnv.cnv_final_result in '12' :
                    result += "CNV"

        return  f"_{result.rstrip(',')}" if len(result) > 2 else ''
    
    
    def __str__ (self):
        return f"{self.sample.tgc_s_id } - {self.final_result1 } -  {self.final_result2 }"
    
    class Meta:
        verbose_name = "판독 Trisomy"  # table header in admin sites
        verbose_name_plural = "판독 Trisomies"
