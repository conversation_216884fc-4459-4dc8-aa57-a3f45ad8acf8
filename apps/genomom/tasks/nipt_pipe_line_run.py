import subprocess

import matplotlib


# from geno_ai.fastq_download import fastq_download

import os
import sys
import time
import pytz
import json
from dateutil import parser
import random
from datetime import datetime

import pandas as pd
from celery import shared_task

from django.conf import settings
from django.core.files.uploadedfile import SimpleUploadedFile
from django.forms import model_to_dict
from django.http import HttpResponse
from django.template.loader import get_template

from apps.common.log import path_check

from celery import current_app


from apps.genomom.pipe_line_helper import (fastq_download,
                                           sample_step_add,
                                           run_pipeline_with_error_handling,
                                           data_analysis_ff,
                                           data_analysis_Fastq,
                                           cnv_plot,
                                           genoAI_rc,
                                           info_for_fastq_download,
                                           rc_gc_db_update,
                                           z_score_update,
                                           ff_info_update,
                                           read_ratio_update,
                                           pff_update,
                                           file_size_check,
                                           delete_files_matching_patterns,
                                           zip_files_matching_patterns,
                                           cnv_prediction
                                           )


from apps.genomom.models import (
    SampleInfo, FF_info, Logo_Sign, Ppv_Npv, Trisomy, Read_Counts_GC,
    Read_Ratio, Z_score, Pff_Info, Ppv_Npv
)
from apps.genomom.weasyprint import build_pdf

matplotlib.use('agg')


@shared_task
def nipt_pipe_line_run(*args,  **kwrgs):
    """
    if function called, we will pass sample table primary key.
    inside this function selected time will opened and other information required will also obtained using data base.
    sample pk is sample table primary key

    if pk is not integer then 
    before Running pipe line check weather cancelled or not 
    make a logic on the basis of time 
    celery -A core worker -l INFO --concurrency=1

    # https://medium.com/clean-slate-technologies/deploy-celery-and-celery-beat-in-production-with-django-ubuntu-de71ccb24907


    """
    from geno_ai.functions_list import (

        cnv_info_string, cnv_plot_run,
        genomom_ff_ai, ml_n_pdf, pff_update_unque, rc_rl_normalization,
        ref_og_n_sca, sam_to_rc_rl, store_info_value_unique,
        trained_models_all_joblib, unique_csv_2_unique_csv_t, unique_to_csv,
        vc_jeong_model, tFFcal
    )

    pk = kwrgs.get('pk')
    sample = SampleInfo.objects.get(pk=pk)
    
    #! TO DOWNLOAD FASTQ FROM TORRENT SERVER WE NEED THESE INFO
    ts_url, ts_run, ts_id, ts_pw, barcode, sample_id, result_folder = info_for_fastq_download(sample=sample)


    #! change Flag when Started
    sample.pipeline_status = '2'  #! started
    sample.save()

    #! just give progress 10 so we know weather celery working or not
    sample_step_add(sample=sample, step_size=5)

    Fastaq_ID = f"{sample_id}_{barcode}"

    #!* path where log File exist is usefull for functions which are imported from genomom
    log_path = result_folder + "/log.txt"

    #!* in data base we save like pippin and whole
    ref_type = sample.sample_dna_qc.ref_type
    
    
    age = sample.patient.patient_age
    week = sample.patient_ga
    fetus_type = sample.patient.fetus_number

    args = ()
    
    
    #! Dont Know the reason but some time celery is not stopping even when job is terminated so this will stop further Running
    if sample.pipeline_progress > 10:
        current_app.control.revoke(sample.celery_task, terminate=True)
        current_app.control.revoke(
            sample.celery_task, terminate=True, signal='SIGKILL')

        return
    
    
    
    
    if sample.is_manual :
        print(f"Doing using local FIle put the File with same name of sample ID " , Fastaq_ID )
        
    else:

        #! First Step Download Fastq File     
        fastq_down_kwrgs = {'sample_id': sample_id,'barcode': barcode, "data_dir" : result_folder, 'ts_url': ts_url, 'user_id': ts_id,'user_pw': ts_pw,  }
        _ = run_pipeline_with_error_handling(fastq_download, args, fastq_down_kwrgs, function_name="fastq_download",  result_folder=result_folder, sample=sample ) 
    
    

    os.chdir(result_folder)  # ! Run Program from Sample Folder


    #! Check weather Fastq downloaded or not
    _ = file_size_check(f"{Fastaq_ID}.Fastq", result_folder, sample, size=300)



    #! Converting Fastq File to Sam File for Fetal Fraction Calculation
    data_analysis_kwrgs = {"Fastq_ID" : Fastaq_ID  , "result_folder" : result_folder }
    
    
    
    ##! status = data_analysis_ff(Fastq_ID=Fastaq_ID,log_path=log_path)#! single line also Run but log will not written
    _ = run_pipeline_with_error_handling(data_analysis_ff, args,  data_analysis_kwrgs, function_name="Fastq Analysis Run (Fetal Fraction)",
                                      result_folder=result_folder, sample=sample )

    # #! Converting Fastq File to Unique File for Z-Score and CNV  File
    _ = run_pipeline_with_error_handling(data_analysis_Fastq, args,  data_analysis_kwrgs, function_name="Fastq Analysis Unique Z -Score & CNV", result_folder=result_folder, sample=sample )




    # #! Convert Unique File to CSV File , Counting Reads with in each Chromosomes
    unique_file_path = f"{Fastaq_ID}.unique"
    _ = file_size_check(unique_file_path, result_folder,sample, size=20)

    unique_to_csv_kwrgs = {"ngs_root": result_folder, "Fastaq_ID": Fastaq_ID} # This Will Create a Unique_csv File inside root directory
    _ = run_pipeline_with_error_handling(unique_to_csv, args, unique_to_csv_kwrgs,
                                              function_name="Uniqe to CSV for Z-Score ",
                                              result_folder=result_folder, sample=sample   )

    # #! To Calculate Fetal Fraction information, RC & RL information is required
    _ = file_size_check(f"{Fastaq_ID}.rmdup.sort.sam" , result_folder, sample, size=100)
    

    sam_to_rc_rl_kwrl = {"Fastq_ID": Fastaq_ID, "result_folder": result_folder}
    _ = run_pipeline_with_error_handling(sam_to_rc_rl, args, sam_to_rc_rl_kwrl, function_name="Sam to RC RL for FF  ", result_folder=result_folder, sample=sample  )



    #! Calculating tFF #SeqFF
    tff_kwrgs = {"Fastq_ID": Fastaq_ID, "log_path":log_path, "result_folder": result_folder}
    _ = run_pipeline_with_error_handling(tFFcal, args, tff_kwrgs, function_name="Seq FF Calculating ", result_folder=result_folder, sample=sample  )




    #! Normalizing RC RL for geno_ai FF (Fastq_ID, result_folder, log_path) # same parameter as tff
    _ = run_pipeline_with_error_handling(rc_rl_normalization, args, tff_kwrgs, function_name="RC RL Normalization ", result_folder=result_folder, sample=sample )



    ##! Running genomom AI  genomom_ff_ai(Fastq_ID, log_path, result_folder, ref_type) #! Extract this from sample table
    geno_ff_ai_kwrgs = {"Fastq_ID": Fastaq_ID, "result_folder": result_folder, "ref_type":ref_type} #!
    _ = run_pipeline_with_error_handling(genomom_ff_ai, args, geno_ff_ai_kwrgs, function_name="Geno AI Calculating ",
                                               result_folder=result_folder,
                                               sample=sample  )



    cnv_input_file = f"{result_folder}/{Fastaq_ID}.ext" if os.path.exists(f"{result_folder}/{Fastaq_ID}.ext") else f"{result_folder}/{Fastaq_ID}.unique"

    cnv_plot_kwrgs = {"cnv_input_file":cnv_input_file, "result_folder": result_folder, "ref_type": ref_type }
    _ = run_pipeline_with_error_handling(cnv_plot, args, cnv_plot_kwrgs, function_name="CNV Plot using ",  result_folder=result_folder, sample=sample  )

    cnv_info = cnv_info_string(
        filtered_final_cnv_file=f"{result_folder}/{Fastaq_ID}.final.cnv")
    
    
    #! if run without any Error,Update Fetal Fraction Table
    ff = pd.read_csv(result_folder + '/' + Fastaq_ID + ".FF.csv")

    #! later Change the Column name while Fastaq_ID .FF.csv saving
    ff.rename(columns={"Seq1": "SeqFF1", "SeqFF": "SeqFF2"}, inplace=True)

    unique_csv = pd.read_csv(os.path.join(
        result_folder, Fastaq_ID + "._unique.csv"))
    
    
    # unique_csv # less than 1 # put all Fetal Fraction information in unique_csv File
    # unique_csv.loc[10:9+ff.shape[1], "Info"] = list(ff.columns)
    # unique_csv.loc[10:9+ff.shape[1], "Value"] = list(round(ff.iloc[0, :], 2))
    #! After PCA Changed 
    unique_csv.loc[9:8+ff.shape[1], "Info"] = list(ff.columns)
    unique_csv.loc[9:8+ff.shape[1], "Value"] = list(round(ff.iloc[0, :], 2))
    
    
    # FFY Fetal Fraction Ratio
    Y_Ratio = float(
        unique_csv.loc[unique_csv.Chr == "chrY", "Ratio"].values[0])
    Y_FF = round((Y_Ratio / 0.00181 - 0.022) * 100, 3)
    
    
    
    #! Update in Unique CSV File
    store_info_value_unique(23, "FF_Y", Y_FF, unique_csv)

    #! Fetal Fraction Information Update #! info value dict error
    info_value_kwrgs = {"sample": sample,
                        "ff_y": Y_FF}  # !

    run_pipeline_with_error_handling(ff_info_update,
                                     args,
                                     info_value_kwrgs,
                                     function_name=" FF Value Updating to Data Base ",
                                     result_folder=result_folder,
                                     sample=sample, step_size=1, arg_save=False)

    #! !!!!!!!!!!!!!!!!!!!!!!!! Data Base Update !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

    #! Read Count & GC  Update
    rc_gb_ai_kwrgs = {"sample": sample, "unique_csv": unique_csv}  # !
    run_pipeline_with_error_handling(rc_gc_db_update, args, rc_gb_ai_kwrgs,
                                     function_name=" Updating Read Count & GC  ",
                                     result_folder=result_folder,
                                     sample=sample, step_size=1, arg_save=False)

    #! Read Ratio although not required but using
    read_ratio_update_kwrgs = {"sample": sample, "unique_csv": unique_csv}  # !
    run_pipeline_with_error_handling(read_ratio_update, args,
                                     read_ratio_update_kwrgs,
                                     function_name="Read Ratio Update in Database ",
                                     result_folder=result_folder,
                                     sample=sample, step_size=1, arg_save=False)


 
    

    unique_csv_t = unique_csv_2_unique_csv_t(unique_csv, Fastaq_ID)

    #! We Will Run CNV pipeline later
    cnv_info = cnv_info_string(
        filtered_final_cnv_file=f"{result_folder}/{Fastaq_ID}.final.cnv")
    
    
    #! Loading the model is time consuming so open in the begining, Loading model is time consuming so open after doing all 
    trained_models = trained_models_all_joblib(ref_type=ref_type )
    trained_models_vc = vc_jeong_model(ref_type=ref_type)  # ! only using in Genomom

    #! SCA & ACA Model is calling on the basis of sample pippin type or not
    ref_og, sca_ref = ref_og_n_sca(ref_type=ref_type)

    try:
        fig, unique_csv, unique_csv_t = ml_n_pdf(unique_csv=unique_csv,
                                                 unique_csv_t=unique_csv_t,
                                                 trained_models=trained_models,
                                                 trained_models_vc=trained_models_vc,
                                                 ref_og=ref_og,
                                                 sca_ref=sca_ref,
                                                 fastaq_id=Fastaq_ID,
                                                 fetus_type=fetus_type,
                                                 cnv_info=cnv_info,
                                                 result_folder=result_folder,
                                                 ref_type = ref_type )

        fig.savefig(result_folder + "/" + Fastaq_ID +
                    "_AIM.pdf", dpi=500)  # pdf save
        fig.savefig(result_folder + "/" + Fastaq_ID +
                    "_AIM.png", dpi=500)  # pdf save
    except Exception as err:
        text = "Error raised while running ai ml_n_pdf function \n" + str(err)
        path_check(text=text, root_dir=result_folder)

    
    
#!**####################################################################################
    #! added 2024-12-06 cnv ai automation info_value_dict.get("GenoAI", ""),
    #! 2024-12-17 Debugging Complete By CH Hwang    
    try:
                
        try :
            ff_value = float(ff['GenoAI'].to_list()[0] ) # ff['GenoAI'].to_numpy()[0]
        except Exception as err:
            
            ff_value = 15.0



                
        cnv_prediction_kwrgs = {"unique_input_file":f"{result_folder}/{Fastaq_ID}.unique",
                                "ff_input": str(ff_value),
                                "result_folder":result_folder}
        
        run_pipeline_with_error_handling(cnv_prediction, args,
                                         cnv_prediction_kwrgs,
                                         function_name=" CNV DNN Models Prediction Start ",
                                         result_folder=result_folder,
                                         sample=sample, step_size=1, arg_save=False)

    except Exception as err:
        text = "Error raised while running CNV DNN Model applying process \n" + str(err)
        path_check(text=text, root_dir=result_folder)


 
#!* ##########################################################################################


    #! Calculate PFF value to the unique csv File
    try:
        print("PFF Updating ... ")
        pff_update_unque(unique_csv, age, week, z_col_name='0.0005_Z')
        
        # Save files with the new naming pattern
        unique_csv.to_excel(f"{result_folder}/{Fastaq_ID}_AIM.xlsx", index=None)
        unique_csv_t.to_excel(f"{result_folder}/{Fastaq_ID}_T.xlsx", index=None)
        

        
    except Exception as err:
        text = "Error raised while updating pff_update _unique around line 423 task.py \n" + \
            str(err)
        path_check(text=text, root_dir=result_folder)


    #! Run AI using Read Count Normalized 
            #! Run AI using Read Count Normalized 
    genoAI_kwrgs = {"unique_file": f"{Fastaq_ID}_AIM.xlsx",  "result_folder": result_folder }
    _ = run_pipeline_with_error_handling(genoAI_rc, args, genoAI_kwrgs, function_name="AI Running using RC method ",  result_folder=result_folder, sample=sample  )
    
    
    #! If Everything goes as planned then lets update Data Base using Excel File
    unique_csv = pd.read_excel(result_folder + '/' + Fastaq_ID + "_AIM.xlsx")
    info_value_dict = unique_csv.loc[:, ["Info", "Value"]].set_index("Info").to_dict()[
        "Value"]
    info_value_kwrgs = {"sample": sample,
                        "info_value_dict": info_value_dict}  # !



    #! Z-Score table, there are 4 types but will store only One
    z_score_update_kwrgs = {
        "sample": sample, "unique_csv": unique_csv, "z_score_column": "0.001_Z"}  # !
    run_pipeline_with_error_handling(z_score_update, args, z_score_update_kwrgs,
                                     function_name=" Updating Z-Score Table ",
                                     result_folder=result_folder,
                                     sample=sample, step_size=1, arg_save=False)




    #! PFF using for Result
    run_pipeline_with_error_handling(pff_update,
                                     args,
                                     info_value_kwrgs,
                                     function_name=" PFF Value Updating to Data Base ",
                                     result_folder=result_folder,
                                     sample=sample,
                                     step_size=1, arg_save=False)



    #! Save Whole unique csv in a log File
    path_check(text="\n\n" + unique_csv.to_string(), root_dir=result_folder)

    #! When all process completed then change flag as completed
    sample.pipeline_status = '4'  #! Success Pipe Line Running 
    sample.pipeline_progress = 100
    sample.save()
    
    
    #! later Remove Bigger File here after process completed
    delete_patterns = [f"{Fastaq_ID}*sam" , f"{Fastaq_ID}*sam.csv.zip" f"{Fastaq_ID}*unique", f"{Fastaq_ID}*ratio", ]
    
    delete_files_matching_patterns(result_folder, delete_patterns)
    
    #! After few months delete unique file 
    #! Make a Compress of other big files 
    file_patterns = [
                    #f"{Fastaq_ID}*unique",
                     f"{Fastaq_ID}*ext",
                     #f"{Fastaq_ID}*ratio",
                     #f"{Fastaq_ID}*sam.csv",
                     ]
    
    zip_files_matching_patterns(result_folder, file_patterns=file_patterns)
    

    return f'Pipe Line Run Success! {sample.tgc_s_id}'