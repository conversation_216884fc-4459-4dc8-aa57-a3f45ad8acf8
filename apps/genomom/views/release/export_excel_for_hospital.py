from django.http import HttpResponse, Http404
import pandas as pd
from io import BytesIO
from apps.genomom.models import SampleInfo
from apps.authentication.decorators import staff_required
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
import logging

logger = logging.getLogger(__name__)

def get_fetus_sex(sample):
    # Helper to safely get a value or fallback to empty string if None
    def default_if_none(value):
        return value if value is not None else ""

    if sample.patient.fetus_sex != '-':
        if sample.patient.fetus_number == 'Twin':
            return "검출"
        else:
            return default_if_none(sample.patient.doctor.male_sex_new)
    elif sample.patient.fetus_sex == '-':
        if sample.patient.fetus_number == 'Twin':
            return "미검출"
        else:
            return default_if_none(sample.patient.doctor.female_sex_new)
    else:
        return "-"
    
    

def format_sample_data(sample):

    
    
    """Format a single sample's data for Excel export."""
    
    dash_line = "----------------------------------------------------------------------------------"
    
    fetus_sex = get_fetus_sex(sample)
    
    # Base text with patient name and test list
    result_explanation = (
        f"{sample.patient.patient_name()}님의 혈액 내 존재하는 세포유리 DNA를 분석하여 "
        f"{sample.service_type.test_list}에 대해 확인하였습니다. "
        "검사 결과: "
    )
    
    # Add result classification
    if sample.samples_trisomy.final_result2 == '0':
        result_explanation += "저위험군"
    elif sample.samples_trisomy.final_result2 in '12':
        result_explanation += f"{sample.samples_trisomy.sample_high_borderlines()}군"
    else:
        result_explanation += f"{sample.samples_trisomy.get_final_result2_display()}군"
    
    memo = sample.service_type.service_limit_memo
    try:
        # Add service limit memo
        if sample.result_detail and sample.result_detail.service_limit_memo:
            memo = sample.result_detail.service_limit_memo
    except:
        pass
    
    memo = memo.replace('<br>', ' ')
    memo = memo.replace('<br />', ' ')
    for i in range(1, 10):
        memo = memo.replace('&nbsp;', ' ')
    
    # Clean the memo text (remove HTML tags and extra whitespace)
    from bs4 import BeautifulSoup
    clean_memo = BeautifulSoup(memo, 'html.parser').get_text().strip()
    result_explanation += f" {clean_memo}"
    
    result_explanation = result_explanation.replace('    ', ' ')
    result_explanation = result_explanation.replace('    ', ' ')
    result_explanation = result_explanation.replace('    ', ' ')
    
    result_explanation = result_explanation.replace('\n', ' ')
    result_explanation = result_explanation.replace('\n', ' ')
    
    result_explanation = result_explanation.replace('.', '.\n')
    
    
    #!!!!!!!!!!!!!!!!!!!!!!!!!!!!! COMMON ACA RESULT
    
    aca_result_text = f"""
* 주요 상염색체 이수성 3종
{dash_line}
검사항목                    결과            검사대상자 위험도            일반인 위험도
{dash_line}
21번 삼염색체증(Trisomy 21)    {sample.samples_trisomy.get_t21_display()}      {"< 1/10,000" if sample.samples_trisomy.t21 == '0' else sample.pff_info.t21_sample_risk}               {sample.pff_info.t21_pop_risk}
18번 삼염색체증(Trisomy 18)    {sample.samples_trisomy.get_t18_display()}      {"< 1/10,000" if sample.samples_trisomy.t18 == '0' else sample.pff_info.t18_sample_risk}               {sample.pff_info.t18_pop_risk}
13번 삼염색체증(Trisomy 13)    {sample.samples_trisomy.get_t13_display()}      {"< 1/10,000" if sample.samples_trisomy.t13 == '0' else sample.pff_info.t13_sample_risk}               {sample.pff_info.t13_pop_risk}
{dash_line}
"""

    sca_result_text = f"""
* 성염색체 이수성 (SCA) 4종
{dash_line}
       검사항목                     결과
{dash_line}
       XO                         {sample.samples_trisomy.get_xo_display()}
       XXY                        {sample.samples_trisomy.get_xxy_display()}
       XXX                        {sample.samples_trisomy.get_xxx_display()}
       XYY                        {sample.samples_trisomy.get_xyy_display()}
{dash_line}
"""

    aca_risks_except_common =  f"""
* 상염색체 이수성 (ACA) 19종
{dash_line}
       검사항목                 결과            검사항목                 결과
{dash_line}
1번 삼염색체증 (Trisomy 1)     {sample.samples_trisomy.get_t1_display()}     11번 삼염색체증 (Trisomy 11)    {sample.samples_trisomy.get_t11_display()}
2번 삼염색체증 (Trisomy 2)     {sample.samples_trisomy.get_t2_display()}     12번 삼염색체증 (Trisomy 12)    {sample.samples_trisomy.get_t12_display()}
3번 삼염색체증 (Trisomy 3)     {sample.samples_trisomy.get_t3_display()}     14번 삼염색체증 (Trisomy 14)    {sample.samples_trisomy.get_t14_display()}
4번 삼염색체증 (Trisomy 4)     {sample.samples_trisomy.get_t4_display()}     15번 삼염색체증 (Trisomy 15)    {sample.samples_trisomy.get_t15_display()}
5번 삼염색체증 (Trisomy 5)     {sample.samples_trisomy.get_t5_display()}     16번 삼염색체증 (Trisomy 16)    {sample.samples_trisomy.get_t16_display()}
6번 삼염색체증 (Trisomy 6)     {sample.samples_trisomy.get_t6_display()}     17번 삼염색체증 (Trisomy 17)    {sample.samples_trisomy.get_t17_display()}
7번 삼염색체증 (Trisomy 7)     {sample.samples_trisomy.get_t7_display()}     19번 삼염색체증 (Trisomy 19)    {sample.samples_trisomy.get_t19_display()}
8번 삼염색체증 (Trisomy 8)     {sample.samples_trisomy.get_t8_display()}     20번 삼염색체증 (Trisomy 20)    {sample.samples_trisomy.get_t20_display()}
9번 삼염색체증 (Trisomy 9)     {sample.samples_trisomy.get_t9_display()}     22번 삼염색체증 (Trisomy 22)    {sample.samples_trisomy.get_t22_display()}
10번 삼염색체증 (Trisomy 10)   {sample.samples_trisomy.get_t10_display()}    
{dash_line}
"""
    cnv_del = sample.cnv_del_list()
    cnv_dup = sample.cnv_dup_list()
    # Suppose you want width 60 for centering
    cnv_result_detail = f"""
* 복제수변이 (CNV) 결실/미세결실 검사항목
{dash_line}
{cnv_del:^60}
{dash_line}

* 복제수변이 (CNV) 중복/미세중복 검사항목
{dash_line}
{cnv_dup:^60}
{dash_line}
"""


    not_using = """
    임신 주수  :  { sample.sample_ga_week_day }
    태아 수    :  { sample.patient.get_fetus_number_display }
    
    검사항목수 : {sample.service_type} 
    검사 방법       :  차세대 염기서열 분석법(NGS)
    """
    fix_width = "          "
        
    result_header = f"염색체 이수성{fix_width}염색체이상항목{fix_width}Fetal Fraction(%){fix_width}Fetal Sex "
    result_value = f"{sample.samples_trisomy.get_final_result2_display()}{fix_width}{sample.samples_trisomy.samples_not_low()} {fix_width} {sample.ff_info.apply_ff} {fix_width*2}  {fetus_sex}"



    # 전체 텍스트
    text = f"""
* 검사방법 : 차세대 염기서열 분석법(NGS)

* 검사항목 :   [ { sample.service_type.service_name } ] { sample.service_type.service_limit }

* 검사결과 요약
{dash_line}
{result_header}
{result_value}
{dash_line}

* 검사소견
{ result_explanation  }

{ aca_result_text }
{sca_result_text }
{ aca_risks_except_common }
{ cnv_result_detail }
    """
    
    if sample.patient.get_fetus_number_display() == '쌍태아':
        service_name = '쌍태아'
    elif sample.patient.get_fetus_number_display() == 'Vanishing twin':
        service_name = 'Vanishing twin'
    else:
        service_name = sample.service_type.service_name.split(' ')[0]
       
    
    
    return {
        "검사일자": sample.entry_date.strftime('%Y-%m-%d'), # sample.entry_date|date:'Y-m-d' # 입고 일 
        "등록번호": sample.patient.jedan_full_number() ,  # 재단 관리 번호 
        '검체번호': sample.patient.hospital_chart_pdf(),   #! 환자 차트 번호 
        "환자이름": sample.patient.patient_name(),         # We dont keep Full Name ( 인멱 처리 )
        "검사코드": "L906029",                             #! 고정 , 재단 서비스 코드 
        '검사명': f"프리시젼 제노맘 [ {service_name} ] " ,  # 기존 결과지에 서비스 이름 라이트 , ..  프리시젼 제노맘 [
        '검사 결과': text.strip(),                   
        '검사분류': "L28",                                  # 고정 
    }

def create_excel_file(data):
    """Create an Excel file with the provided data."""
    df = pd.DataFrame(data)
    
    # Create Excel file in memory
    output = BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='Selected Samples', index=False)
        
        workbook = writer.book
        worksheet = writer.sheets['Selected Samples']
        
        # Formats
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'fg_color': '#D7E4BC',
            'border': 1,
            'font_size': 11
        })
        
        center_format = workbook.add_format({
            'align': 'center',
            'valign': 'vcenter',
            'font_size': 10
        })
        
        result_format = workbook.add_format({
            'align': 'left',
            'valign': 'top',
            'text_wrap': True,
            'font_size': 10,
            'font_name': 'Courier New'  # Monospace font for better alignment of formatted text
        })
        
        # Apply header format
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
        
        # Set column widths based on content
        for col in range(8):
            if df.columns[col] == '검사 결과':
                worksheet.set_column(col, col, 150, result_format)
            else:
                worksheet.set_column(col, col, 20, center_format)
        
        # Set header row height to 30 (smaller than data rows)
        worksheet.set_row(0, 30)
        
        # Set data row heights to 200
        for row in range(1, len(df) + 1):
            worksheet.set_row(row, 400)
            
        # Freeze the header row
        worksheet.freeze_panes(1, 0)
        
        # Add autofilter to the header row
        worksheet.autofilter(0, 0, len(df), len(df.columns) - 1)
    
    return output

@login_required(login_url="/")
@staff_required(allowed_user_groups=['6', '7'])
def export_excel_for_hospital(request):
    """Export selected samples to Excel for hospital."""
    if request.method != 'POST':
        return HttpResponse('Invalid request method', status=400)
    
    try:
        selected_ids = request.POST.getlist('selected_ids[]')
        
        if not selected_ids:
            return HttpResponse('No samples selected', status=400)
        
        # Get the selected samples with optimized query
        samples = SampleInfo.objects.filter(id__in=selected_ids).select_related(
            'patient', 
            'patient__doctor',
            'service_type',
            'samples_trisomy',
            'ff_info',
            'pff_info'
        )
        
        # Verify that all requested samples exist
        if len(samples) != len(selected_ids):
            logger.warning(f"Some requested samples were not found. Requested: {len(selected_ids)}, Found: {len(samples)}")
        
        # Format the data
        data = [format_sample_data(sample) for sample in samples]
        
        # Create the Excel file
        output = create_excel_file(data)
        
        # Return the Excel file with a timestamp in the filename
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"precision_genomom_text_result_{timestamp}.xlsx"
        
        output.seek(0)
        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename={filename}'
        return response
        
    except Exception as e:
        logger.error(f"Error exporting Excel: {str(e)}")
        return HttpResponse(f"Error creating Excel file: {str(e)}", status=500)
