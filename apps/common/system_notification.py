#!/home/<USER>/anaconda3/envs/genomom_lims/bin/python

import datetime
import json
import subprocess

def dev_system_info(server_name, content):
    """
    Extract system information into a dictionary
    Split content into 5 parts with 14 characters each
    datetime_obj should be datetime.now() object
    """
    
    # 서버 이름 14글자로 제한
    if len(server_name) > 14:
        server_name = server_name[:14]
    
    # Extract date and time components
    datetime_obj=datetime.datetime.now()
    
    # Extract date and time components
    date_Year = datetime_obj.strftime("%Y")
    date_Month = datetime_obj.strftime("%m")
    date_Day = datetime_obj.strftime("%d")
    date_Time = datetime_obj.strftime("%H:%M")
    
    # Initialize all content parts with default empty string
    content_parts = ["" for _ in range(5)]
    
    # Split content into chunks of 14 characters
    for i in range(min(5, len(content) // 14 + (1 if len(content) % 14 else 0))):
        start = i * 14
        end = start + 14
        content_parts[i] = content[start:end]
    
    # print(f'{date_Year}/{date_Month}/{date_Day} {date_Time}')
    
    system_infos = {
        "server_name": server_name,
        "date_Year": f'{date_Year}/',
        "date_Month": f'{date_Month}/',
        "date_Day": f'{date_Day} ',
        "date_Time": f'{date_Time}',
        "content_1": content_parts[0],
        "content_2": content_parts[1],
        "content_3": content_parts[2],
        "content_4": content_parts[3],
        "content_5": content_parts[4],
    }
    
    return system_infos

def send_system_notification(server_name, content):
    
    template_parameters = dev_system_info(server_name, content)
    
    developer_dict = {
        '01022734018': '크리스나',
        '01049067627': '황창회'
    }
    
    developer_phones = list(developer_dict.keys())
    developer_names = list(developer_dict.values())
    
    print('**** template parameters:')
    print(template_parameters)
    
    python_path = '/home/<USER>/anaconda3/envs/genomom_lims/bin/python'

    # Send Kakao notification
    kakao_cmd = [
        python_path,
        "apps/common/kakao_notification_cli.py",
        "--phones", ",".join(developer_phones),
        "--names", ",".join(developer_names),
        "--template_code", 'system_notif',
        "--template_params", json.dumps(template_parameters, ensure_ascii=False)
    ]
    
    # 카카오 알림 코드 실행 및 결과 반환
    result = subprocess.run(kakao_cmd, capture_output=True, text=True)
    # if 'False' in result.stderr:
    #     print('False')
    # else:
    #     print('True')


# send_system_notification(server_name='genomom_lims',
#                          content=f'시스템 알림 테스트')