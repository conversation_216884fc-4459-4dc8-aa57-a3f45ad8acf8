#!/home/<USER>/anaconda3/envs/genomom_lims/bin/python

import time
import pandas as pd
import argparse
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from webdriver_manager.chrome import ChromeDriverManager
import sys
import logging
from datetime import datetime
import json
import os
from pathlib import Path
from dotenv import load_dotenv

# .env_alim 파일 경로 명시해서 로드
load_dotenv(dotenv_path='/BiO/github/genomom_lims/.env')

# 환경변수 가져오기
WEHAGO_APP_ID = os.getenv('WEHAGO_APP_ID')
WEHAGO_APP_PASSWORD = os.getenv('WEHAGO_APP_PASSWORD')

# 로깅 설정
def setup_logging():
    # 절대 경로로 로그 디렉토리 설정 - 통합된 로그 디렉토리 사용
    log_dir = "/BiO/github/genomom_lims/logs/notifications"
    history_dir = "/BiO/github/genomom_lims/logs/notifications/history"
    
    # 디렉토리 생성
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    Path(history_dir).mkdir(parents=True, exist_ok=True)
    
    # 날짜별로 로그 파일 생성 (일별 로그)
    current_date = datetime.now().strftime("%Y%m%d")
    log_file = f"{log_dir}/notifications_{current_date}.log"
    
    # 로거 설정
    logger = logging.getLogger('wehago_notification')
    logger.setLevel(logging.INFO)
    
    # 기존 핸들러 제거
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 파일 핸들러
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(logging.Formatter('%(asctime)s - [%(name)s] - %(levelname)s - %(message)s'))
    logger.addHandler(file_handler)
    
    # 콘솔 핸들러 (Django runserver에서도 보이도록)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter('%(asctime)s - [%(name)s] - %(levelname)s - %(message)s'))
    logger.addHandler(console_handler)
    
    return logger, log_file

# 메시지 기록 저장
def save_message_history(recipients, message, status="success"):
    history_dir = "/BiO/github/genomom_lims/logs/notifications/history"
    Path(history_dir).mkdir(parents=True, exist_ok=True)
    
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    history_file = f"{history_dir}/wehago_history_{current_time}.json"
    
    # 메시지를 줄바꿈으로 구분된 형식으로 변환
    formatted_message = message.replace('\n', '\n    ')
    
    history = {
        "timestamp": current_time,
        "recipients": recipients,
        "message": formatted_message,
        "status": status,
        "message_details": {
            "title": message.split('\n')[0],  # 첫 줄을 제목으로 사용
            "content": message.split('\n')[1:]  # 나머지 줄들을 내용으로 사용
        }
    }
    
    with open(history_file, 'w', encoding='utf-8') as f:
        json.dump(history, f, ensure_ascii=False, indent=2)
    
    return history_file

def Wehago_CS_Notification(name_lst, msg, max_retries=3, retry_delay=10):
    """
    위하고 CS 알림을 전송하는 함수
    
    Args:
        name_lst: 수신자 이름 리스트
        msg: 전송할 메시지
        max_retries: 최대 재시도 횟수 (기본값: 3)
        retry_delay: 재시도 전 대기 시간(초) (기본값: 60)
    """
    attempt = 0
    while attempt < max_retries:
        attempt += 1
        logger, log_file = setup_logging()
        logger.info(f"=== 위하고 CS 알림 시작 (시도 {attempt}/{max_retries}) ===")
        
        try:
            # 메시지 포맷팅
            formatted_msg = msg.replace('\n', '\n    ')
            logger.info(f"전송할 메시지:\n{formatted_msg}")
            
            logger.info("1. Chrome 드라이버 설정 중...")
            options = Options()
            options.add_argument("disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            options.add_argument('headless')
            options.add_argument('window-size=1920x1080')
            options.add_argument("disable-gpu")
            options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.150 Safari/537.36")
            # options.page_load_strategy = 'eager'
            driver = webdriver.Chrome(service=webdriver.chrome.service.Service(ChromeDriverManager().install()), options=options)

            try:
                logger.info("2. 위하고 로그인 페이지 접속 중...")
                driver.get('https://www.wehago.com/#/login')
                time.sleep(3)
                logger.info("3. 로그인 정보 입력 중...")
                driver.find_element(By.CSS_SELECTOR, '#inputId').send_keys(WEHAGO_APP_ID)
                driver.find_element(By.CSS_SELECTOR, '#inputPw').send_keys(WEHAGO_APP_PASSWORD)
                time.sleep(3)
                driver.find_element(By.CSS_SELECTOR, '#contnt > div.content_box.login_process > div > button').send_keys(Keys.ENTER)
                time.sleep(1)
                
                logger.info("4. 로그인 상태 확인 중...")
                if driver.current_url == 'https://www.wehago.com/#/login':
                    try:
                        logger.info("   - 다른 기기에서 로그인된 상태 감지, 로그아웃 처리 중...")
                        driver.find_element(By.CSS_SELECTOR, '#contnt > div:nth-child(9) > div:nth-child(2) > div > div > div.dialog_content > div > div > div.dialog_data_area > div > div > button:nth-child(2)').click()
                        time.sleep(3)
                    except Exception as e:
                        logger.warning(f"   - 로그아웃 처리 중 오류 발생: {str(e)}")

                logger.info("5. 커뮤니케이션 페이지로 이동 중...")
                driver.get('https://www.wehago.com/#/communication2')
                time.sleep(10)

                logger.info(f"6. 메시지 전송 시작 (대상: {len(name_lst)}명)")
                failed_recipients = []
                successful_recipients = []
                
                # 메시지 세팅 단계
                logger.info("   - 메시지 세팅 시작")
                for i, name in enumerate(name_lst, 1):
                    try:
                        logger.info(f"   - {i}/{len(name_lst)}: {name}에게 메시지 세팅 중...")
                        time.sleep(3)
                        driver.find_element(By.XPATH, '//*[@id="BODY_CLASS"]/div[3]/div/div/div/div/div/div[1]/div[3]/div[2]/div[1]/div[1]/div/div[1]/button').click()
                        time.sleep(1.5)
                        driver.find_element(By.XPATH, '/html/body/div[3]/div/div/div[2]/div/div/div/ul/li[2]/a/div').click()
                        time.sleep(1.5)
                        driver.find_element(By.XPATH, '//*[@id="inputSearch-TK"]').send_keys(name)
                        time.sleep(1.5)
                        driver.find_element(By.XPATH, '//*[@id="BODY_CLASS"]/div[3]/div/div/div/div/div/div[1]/div[3]/div[4]/div[2]/div/div/div[1]/div/div[3]/div[2]/div/div/div/div[3]/div/div/button/span').click()
                        time.sleep(1.5)
                        driver.find_element(By.XPATH, f'//div[@class="name"][contains(normalize-space(.), "{name}")]').click()
                        time.sleep(1.5)
                        
                        button = driver.find_element(By.XPATH, '//*[@id="BODY_CLASS"]/div[3]/div/div/div/div/div/div[1]/div[3]/div[4]/div[2]/div/div/div[1]/div/div[4]/div/button[2]/span')
                        current_url = driver.current_url
                        button.click()
                        time.sleep(1)
                        
                        change_url = driver.current_url
                        driver.execute_script("window.open('');")
                        time.sleep(1)
                        driver.switch_to.window(driver.window_handles[-1])
                        time.sleep(1)
                        driver.get(change_url)
                        time.sleep(1)
                        
                        element = driver.find_element(By.XPATH, '//*[starts-with(@id, "mentiony-content-")]')
                        lines = msg.splitlines()
                        for j, line in enumerate(lines):
                            element.send_keys(line)
                            if j != len(lines) - 1:
                                element.send_keys(Keys.SHIFT, Keys.ENTER)
                        
                        driver.switch_to.window(driver.window_handles[0])
                        time.sleep(1)
                        
                    except Exception as e:
                        logger.error(f"   - {name}에게 메시지 세팅 실패: {str(e)}")
                        failed_recipients.append(name)
                        continue

                # 메시지 전송 단계
                logger.info("   - 메시지 전송 시작")
                reversed_names = list(reversed(name_lst))
                for tab_index in range(1, len(driver.window_handles)):
                    try:
                        recipient_name = reversed_names[tab_index-1]
                        driver.switch_to.window(driver.window_handles[tab_index])
                        time.sleep(0.25)
                        element = driver.find_element(By.XPATH, '//*[starts-with(@id, "mentiony-content-")]')
                        element.send_keys(Keys.ENTER)
                        time.sleep(0.25)
                        successful_recipients.append(reversed_names[tab_index-1])
                        logger.info(f"   - {recipient_name}에게 메시지 전송 성공")
                    except Exception as e:
                        logger.warning(f"   - {recipient_name}에게 메시지 전송 실패: {str(e)}")
                        failed_recipients.append(recipient_name)
                
                driver.switch_to.window(driver.window_handles[0])
                time.sleep(1)

                logger.info("7. 브라우저 종료 중...")
                driver.quit()
                
                # 실패한 수신자가 없으면 성공으로 처리하고 종료
                if not failed_recipients:
                    history_file = save_message_history(
                        recipients={
                            "successful": successful_recipients,
                            "failed": []
                        },
                        message=msg,
                        status="success"
                    )
                    logger.info(f"=== 위하고 CS 알림 완료 (시도 {attempt}/{max_retries}) ===")
                    logger.info(f"로그 파일: {log_file}")
                    logger.info(f"히스토리 파일: {history_file}")
                    return True
                
                # 실패한 수신자가 있으면 name_lst를 업데이트하고 재시도
                name_lst = failed_recipients
                logger.warning(f"실패한 수신자 {len(failed_recipients)}명에 대해 재시도 예정")
                
            except Exception as e:
                logger.error(f"전송 중 오류 발생: {str(e)}")
                if 'driver' in locals():
                    driver.quit()
                raise
                
        except Exception as e:
            if attempt < max_retries:
                logger.error(f"오류 발생, {retry_delay}초 후 재시도 예정: {str(e)}")
                time.sleep(retry_delay)
            else:
                logger.error(f"최대 시도 횟수 도달. 최종 오류: {str(e)}")
                # 마지막 시도에서 실패한 경우 히스토리 저장
                history_file = save_message_history(
                    recipients={
                        "successful": successful_recipients if 'successful_recipients' in locals() else [],
                        "failed": failed_recipients if 'failed_recipients' in locals() else name_lst
                    },
                    message=msg,
                    status="failed"
                )
                logger.info(f"히스토리 파일: {history_file}")
                return False
    
    return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='위하고 CS 알림 전송 프로그램')
    parser.add_argument('--names', nargs='+', required=True, help='메시지를 전송할 대상 이름 리스트 (예: --names 황창회 크리스나)')
    parser.add_argument('--msg', type=str, required=True, help='메시지 내용')
    args = parser.parse_args()
    
    name_lst = args.names
    msg = args.msg
    logger, _ = setup_logging()
    logger.info(f"전송 대상: {name_lst}")
    try:
        Wehago_CS_Notification(name_lst, msg)
    except Exception as e:
        logger.error(f"오류 발생: {str(e)}")
        sys.exit(1) 