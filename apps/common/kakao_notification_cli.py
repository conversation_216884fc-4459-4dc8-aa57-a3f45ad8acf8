#!/home/<USER>/anaconda3/envs/genomom_lims/bin/python

import os
import requests
import json
from dotenv import load_dotenv
import argparse
import logging
import sys
from datetime import datetime
from pathlib import Path

# .env 파일 로드
load_dotenv(dotenv_path='/BiO/github/genomom_lims/.env')

ALIMTALK_APP_KEY = os.getenv('ALIMTALK_APP_KEY')
ALIMTALK_SECRET_KEY = os.getenv('ALIMTALK_SECRET_KEY')
ALIMTALK_SENDER_KEY = os.getenv('ALIMTALK_SENDER_KEY')

API_URL = f'https://api-alimtalk.cloud.toast.com/alimtalk/v2.3/appkeys/{ALIMTALK_APP_KEY}/messages'

# 통합 로깅 설정
def setup_logging():
    # 절대 경로로 로그 디렉토리 설정 - 통합된 로그 디렉토리 사용
    log_dir = "/BiO/github/genomom_lims/logs/notifications"
    history_dir = "/BiO/github/genomom_lims/logs/notifications/history"
    
    # 디렉토리 생성
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    Path(history_dir).mkdir(parents=True, exist_ok=True)
    
    # 날짜별로 로그 파일 생성 (일별 로그)
    current_date = datetime.now().strftime("%Y%m%d")
    log_file = f"{log_dir}/notifications_{current_date}.log"
    
    # 로거 설정
    logger = logging.getLogger('notification_system')
    logger.setLevel(logging.INFO)
    
    # 기존 핸들러 제거
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 파일 핸들러
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(logging.Formatter('%(asctime)s - [%(name)s] - %(levelname)s - %(message)s'))
    logger.addHandler(file_handler)
    
    # 콘솔 핸들러
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter('%(asctime)s - [%(name)s] - %(levelname)s - %(message)s'))
    logger.addHandler(console_handler)
    
    return logger, log_file

# 메시지 기록 저장
def save_message_history(recipient_phones, recipient_names, template_code, template_parameters, result, status="success"):
    history_dir = "/BiO/github/genomom_lims/logs/notifications/history"
    Path(history_dir).mkdir(parents=True, exist_ok=True)
    
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    history_file = f"{history_dir}/kakao_history_{current_time}.json"
    
    history = {
        "timestamp": current_time,
        "recipients": {
            "phones": recipient_phones,
            "names": recipient_names
        },
        "template_code": template_code,
        "template_parameters": template_parameters,
        "result": result,
        "status": status
    }
    
    with open(history_file, 'w', encoding='utf-8') as f:
        json.dump(history, f, ensure_ascii=False, indent=2)
    
    return history_file

def send_alimtalk(recipient_list, template_code):
    logger, _ = setup_logging()
    logger.info(f"=== 알림톡 전송 시작 (템플릿: {template_code}) ===")
    
    headers = {
        'Content-Type': 'application/json;charset=UTF-8',
        'X-Secret-Key': ALIMTALK_SECRET_KEY
    }
    payload = {
        'senderKey': ALIMTALK_SENDER_KEY,
        'templateCode': template_code,
        'recipientList': recipient_list,
    }
    
    try:
        logger.info(f"API 요청 전송 중... (수신자 수: {len(recipient_list)})")
        response = requests.post(API_URL, headers=headers, json=payload)
        response.raise_for_status()
        result = response.json()
        logger.info(f"API 요청 성공: {json.dumps(result['header'], ensure_ascii=False)}")
        return result
    except requests.exceptions.RequestException as e:
        logger.error(f"API 요청 실패: {str(e)}")
        raise

def kakao_message(recipient_phones, recipient_names, template_code, template_parameters):
    logger, log_file = setup_logging()
    logger.info(f"알림톡 메시지 준비 - 템플릿: {template_code}, 수신자: {list(zip(recipient_names, recipient_phones))}")
    
    try:
        recipient_list = [
            {
                'recipientNo': phone,
                'templateParameter': template_parameters
            }
            for phone in recipient_phones
        ]
        
        result = send_alimtalk(recipient_list, template_code)
        history_file = save_message_history(recipient_phones, recipient_names, template_code, template_parameters, result)
        
        # 알림톡 전송 결과 확인
        transfer_result = result['header']['isSuccessful']
        if transfer_result:
            logger.info(f"알림톡 전송 완료 - 결과: {json.dumps(transfer_result, ensure_ascii=False)}")
        else:
            logger.error(f"알림톡 전송 실패 - 결과: {json.dumps(transfer_result, ensure_ascii=False)}")
        
        logger.info(f"알림톡 전송 완료 - 결과: {json.dumps(result['header']['isSuccessful'], ensure_ascii=False)}")
        logger.info(f"로그 파일: {log_file}")
        logger.info(f"히스토리 파일: {history_file}")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        return transfer_result
    except Exception as e:
        logger.error(f"알림톡 전송 중 오류 발생: {str(e)}")
        history_file = save_message_history(recipient_phones, recipient_names, template_code, template_parameters, {"error": str(e)}, "failed")
        logger.info(f"히스토리 파일: {history_file}")
        raise

# ✅ CLI Entry Point
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="알림톡 메시지 전송")
    parser.add_argument("--phones", required=True, help="쉼표로 구분된 수신자 번호 목록")
    parser.add_argument("--names", required=True, help="쉼표로 구분된 수신자 이름 목록")
    parser.add_argument("--template_code", required=True, help="템플릿 코드")
    parser.add_argument("--template_params", required=True, help="템플릿 파라미터 JSON 문자열")

    args = parser.parse_args()

    recipient_phones = args.phones.split(",")
    recipient_names = args.names.split(",")
    template_code = args.template_code
    template_parameters = json.loads(args.template_params)

    try:
        transfer_result = kakao_message(recipient_phones, recipient_names, template_code, template_parameters)
        print(f"전송 결과: {transfer_result}", file=sys.stderr)  # 디버깅을 위해 결과 출력
    except Exception as e:
        print(f"Error: False", file=sys.stderr)