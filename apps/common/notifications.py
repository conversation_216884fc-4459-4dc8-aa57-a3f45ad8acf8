#!/home/<USER>/anaconda3/envs/genomom_lims/bin/python

import subprocess
import json
import threading
from typing import Dict
from django.db.models import QuerySet
from .notification_templates import get_notification_template
from apps.crm.views.constants import extract_cs_info, extract_cs_comment, extract_marketing_info, extract_marketing_comment

# 시스템 알림용 함수 추가
from .system_notification import send_system_notification

def shorten(text, max_length=14):
    if isinstance(text, str) and len(text) > max_length:
        return text[:max_length - 1] + '…'
    return text

def send_notifications_async(template_code: str, data: Dict, target_employees: Dict):
    """
    비동기로 알림을 전송하는 함수
    
    Args:
        template_code: 사용할 템플릿 코드
        data: 템플릿에 따른 데이터 (CS_Inquiry 객체 또는 기타 데이터)
        target_employees: 알림을 받을 직원 QuerySet
    """
    thread = threading.Thread(
        target=send_notifications,
        args=(template_code, data, target_employees),
        daemon=True
    )
    thread.start()
    return thread

def send_notifications(template_code: str, data: Dict, target_employees: Dict):
    """
    Send notifications to both Kakao and Wehago platforms
    
    Args:
        template_code: 사용할 템플릿 코드
        data: 템플릿에 따른 데이터 (CS_Inquiry 객체 또는 기타 데이터)
        target_employees: 알림을 받을 직원 QuerySet
    """
    # Extract phone numbers and names from queryset
    recipient_names = list(target_employees.keys())
    recipient_phones = list(target_employees.values())

    
    # 템플릿 코드에 따라 데이터 준비
    if template_code == 'gc_marketing_info':
        infos = extract_marketing_info(data)  # MarketingVisit 객체에서 정보 추출
    
    elif template_code == 'gc_cs_info':
        infos = extract_cs_info(data)  # CS_Inquiry 객체에서 정보 추출
        
    elif template_code == 'gc_marketing_comment':
        infos = extract_marketing_comment(data)  # MarketingVisitComment 객체에서 정보 추출
        
    elif template_code == 'gc_cs_comment':
        infos = extract_cs_comment(data)  # CS_Inquiry 객체에서 정보 추출

    else:
        return print('템플릿 코드가 잘못되었습니다.')
    
    # Get template parameters and messages
    template_parameters, wehago_msg = get_notification_template(template_code, infos)
    # print("**** infos:")
    # print(infos)
    print('**** template parameters:')
    print(template_parameters)
    python_path = '/home/<USER>/anaconda3/envs/genomom_lims/bin/python'

    # Send Kakao notification
    kakao_cmd = [
        python_path,
        "apps/common/kakao_notification_cli.py",
        "--phones", ",".join(recipient_phones),
        "--names", ",".join(recipient_names),
        "--template_code", template_code,
        "--template_params", json.dumps(template_parameters, ensure_ascii=False)
    ]
    
    # 카카오 알림 코드 실행 및 결과 반환
    result = subprocess.run(kakao_cmd, capture_output=True, text=True)
    if 'False' in result.stderr:
        send_system_notification(server_name='genomom_lims',
                            content=f'{template_code} - 알림톡 전송 실패. 확인 요망.')









    # 위하고 기능 불안정으로 인해 주석처리
    # # Send Wehago notification
    # wehago_cmd = [python_path, "apps/common/wehago_cs_notification.py"]
    # wehago_cmd.extend(["--names"] + recipient_names)
    # wehago_cmd.extend(["--msg", wehago_msg])

    # process = subprocess.Popen(wehago_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1, universal_newlines=True)

    # # Handle output in real-time
    # def print_output(pipe):
    #     for line in iter(pipe.readline, ''):
    #         print(line.strip())

    # stdout_thread = threading.Thread(target=print_output, args=(process.stdout,))
    # stderr_thread = threading.Thread(target=print_output, args=(process.stderr,))
    # stdout_thread.daemon = True
    # stderr_thread.daemon = True
    # stdout_thread.start()
    # stderr_thread.start()