#!/home/<USER>/anaconda3/envs/genomom_lims/bin/python

from typing import Dict, Tuple, List, Any, Optional, Union
from bs4 import BeautifulSoup as bs
import re

def shorten(text, max_length=14):
    """
    텍스트를 지정된 최대 길이로 줄이고 필요한 경우 '…'를 추가하는 함수
    Args:
        text: 원본 텍스트
        max_length: 최대 길이 (기본값: 14)
    """
    if isinstance(text, str) and len(text) > max_length:
        return text[:max_length - 1] + '…'
    return text

def strip_html_tags(text: str) -> str:
    """
    HTML 태그를 제거하되 단락 구분을 보존하여 순수 텍스트로 변환합니다.
    
    Args:
        text: HTML 태그가 포함된 문자열
        
    Returns:
        str: HTML 태그가 제거되고 단락 구분이 보존된 텍스트
    """
    if not isinstance(text, str):
        text = str(text) if text is not None else ''
    
    try:
        # 빈 텍스트 처리
        if not text.strip():
            return ''
        
        # BeautifulSoup으로 HTML 파싱
        soup = bs(text, 'html.parser')
        
        # 단락 태그와 줄바꿈 태그 처리
        for p_tag in soup.find_all('p'):
            if p_tag.text.strip():
                p_tag.append('\n')
                
        for br_tag in soup.find_all('br'):
            br_tag.replace_with('\n')
        
        # 텍스트 추출
        clean_text = soup.get_text()
        
        # 연속된 줄바꿈 정리
        clean_text = re.sub(r'\n{2,}', '\n', clean_text)
        
        # 앞뒤 공백 제거
        clean_text = clean_text.strip()
        
        return clean_text
    except Exception as e:
        print(f"Error in strip_html_tags: {e}")
        return text

def clean_text(text: Any, strip_html: bool = False, max_length: Optional[int] = None) -> str:
    """
    텍스트를 정리하는 통합 함수
    - HTML 태그 제거 (필요시)
    - 길이 제한 (필요시)
    """
    # None 또는 비문자열 타입 처리
    if not isinstance(text, str):
        text = str(text) if text is not None else ''
    
    # HTML 태그 제거
    if strip_html:
        text = strip_html_tags(text)
    
    # 길이 제한
    if max_length is not None and len(text) > max_length:
        text = text[:max_length-1] + '…'
    
    return text

def split_text_into_parts(text: Any, max_length: int = 140, part_size: int = 14, 
                          num_parts: int = 10, strip_html: bool = False) -> List[str]:
    """
    텍스트를 지정된 크기로 나누고 최대 길이를 제한하는 함수
    Args:
        text: 분할할 텍스트
        max_length: 전체 텍스트 최대 길이 (기본값: 140)
        part_size: 각 부분의 크기 (기본값: 14)
        num_parts: 생성할 파트 개수 (기본값: 10)
        strip_html: 텍스트에서 HTML 태그를 제거할지 여부
    Returns:
        지정된 크기로 나눈 문자열 리스트 (빈 문자열 포함)
    """
    # 텍스트 정리 (HTML 태그 제거 포함)
    text = clean_text(text, strip_html=strip_html, max_length=max_length)
    
    # 지정된 크기로 분할
    parts = []
    for i in range(0, len(text), part_size):
        part = text[i:i+part_size]
        parts.append(part)
    
    # 지정된 파트 개수로 맞추기
    while len(parts) < num_parts:
        parts.append('')
    
    return parts[:num_parts]  # 혹시 더 많이 분할된 경우를 대비해 개수 제한

def get_input_text(html_obj, get_method: str = 'first') -> str:
    """
    응답 객체에서 답변 텍스트를 안전하게 추출합니다.
    
    Args:
        html_obj: HTML 태그가 포함된 문자열
        get_method: 응답을 가져오는 방식 ('first', 'last' 등 QuerySet 메서드)
        
    Returns:
        str: HTML 태그가 제거된 답변 텍스트 또는 빈 문자열
    """
    try:
        if html_obj is None:
            return ""
        
        # QuerySet인 경우 exists() 확인 후 지정된 메서드로 응답 가져오기
        if hasattr(html_obj, 'exists'):
            if html_obj.exists():
                if hasattr(html_obj, get_method):
                    response = getattr(html_obj, get_method)()
                    return strip_html_tags(response) if response else ""
                return ""  # 지정된 메서드가 없는 경우
            return ""
            
        # 단일 CS_Response 객체인 경우
        if hasattr(html_obj, 'answer'):
            return strip_html_tags(html_obj)
            
        return strip_html_tags(str(html_obj)) if html_obj else ""
        
    except Exception as e:
        print(f"Error in get_input_text: {e}")
        return ""

def create_template_parameters(field_map: Dict, data: Dict, text_fields: Dict = None) -> Dict:
    """
    템플릿 파라미터를 생성하는 일반 함수
    
    Args:
        field_map: 템플릿 필드명과 데이터 필드명 매핑
        data: 데이터 딕셔너리
        text_fields: 특별 처리가 필요한 긴 텍스트 필드 (HTML 제거 및 분할)
        
    Returns:
        템플릿 파라미터 딕셔너리
    """
    params = {}
    
    # 기본 필드 처리
    for template_key, data_key in field_map.items():
        if data_key in data:
            params[template_key] = shorten(data[data_key])
    
    # 긴 텍스트 필드 처리
    if text_fields:
        for field_name, config in text_fields.items():
            if field_name in data:
                field_value = data[field_name]
                if isinstance(field_value, str) or field_value is None:
                    # 직접 분할이 필요한 경우
                    parts = split_text_into_parts(
                        field_value,
                        strip_html=config.get('strip_html', False),
                        max_length=config.get('max_length', 140),
                        part_size=config.get('part_size', 14),
                        num_parts=config.get('num_parts', 10)
                    )
                else:
                    # 함수를 통해 텍스트를 얻어야 하는 경우 (예: inquiry_answer)
                    text = get_input_text(field_value, get_method=config.get('get_method', 'first'))
                    parts = split_text_into_parts(
                        text,
                        max_length=config.get('max_length', 140),
                        part_size=config.get('part_size', 14),
                        num_parts=config.get('num_parts', 10)
                    )
                
                # 템플릿 파라미터에 추가
                for i, part in enumerate(parts, 1):
                    params[f"{config.get('prefix', field_name)}_{i}"] = part
    
    return params

def prepare_cs_info_template(cs_infos: Dict) -> Tuple[Dict, str]:
    """
    CS 문의 알림을 위한 템플릿 파라미터와 메시지를 준비하는 함수
    """
    # 문의 내용 처리 (10개 파트로 분할)
    question_parts = split_text_into_parts(cs_infos['inquiry_question'], strip_html=True, num_parts=10)
    
    # 답변 내용 처리 - HTML 단락 구분 보존
    answer_html = cs_infos.get('inquiry_answer', '')
    if isinstance(answer_html, str):
        # HTML이 문자열로 직접 들어온 경우 (constants.py에서 process_html_paragraph 적용됨)
        answer_text = strip_html_tags(answer_html)
    else:
        # CS_Response 객체나 QuerySet인 경우
        answer_text = get_input_text(answer_html, get_method='first')
    
    # 답변 내용 분할
    answer_parts = split_text_into_parts(answer_text, num_parts=10)
    
    # 템플릿 만들기
    template_parameters = {
        'inquiry_id': shorten(cs_infos['inquiry_id']),
        'sample_id': shorten(cs_infos['sample_id']),
        'category1': shorten(cs_infos['category1']),
        'inquiry_date': shorten(cs_infos['inquiry_date']),
        'inquiry_question_1': question_parts[0],
        'inquiry_question_2': question_parts[1],
        'inquiry_question_3': question_parts[2],
        'inquiry_question_4': question_parts[3],
        'inquiry_question_5': question_parts[4],
        'inquiry_question_6': question_parts[5],
        'inquiry_question_7': question_parts[6],
        'inquiry_question_8': question_parts[7],
        'inquiry_question_9': question_parts[8],
        'inquiry_question_10': question_parts[9],
        'inquiry_answer_1': answer_parts[0],
        'inquiry_answer_2': answer_parts[1],
        'inquiry_answer_3': answer_parts[2],
        'inquiry_answer_4': answer_parts[3],
        'inquiry_answer_5': answer_parts[4],
        'inquiry_answer_6': answer_parts[5],
        'inquiry_answer_7': answer_parts[6],
        'inquiry_answer_8': answer_parts[7],
        'inquiry_answer_9': answer_parts[8],
        'inquiry_answer_10': answer_parts[9],
        'service_type': shorten(cs_infos['service_type']),
        'branch': shorten(cs_infos['branch']),
        'inquiry_org': shorten(cs_infos['inquiry_org']),
        'inquiry_person': shorten(cs_infos['inquiry_person']),
        'inquiry_phone': shorten(cs_infos['inquiry_phone']),
        'hospital_name': shorten(cs_infos['hospital_name']),
        'hospital_chart': shorten(cs_infos['hospital_chart']),
    }
    
    # 문의 내용과 답변 내용 가져오기 (위하고 메시지용)
    question_text = clean_text(cs_infos['inquiry_question'], strip_html=True)
    
    # 위하고 메시지 준비
    wehago_msg = f"[지놈케어 CS 등록 알림]\n"
    wehago_msg += f"- 문의 내용: {question_text}\n"
    wehago_msg += f"- 답변 내용: {answer_text}\n"
    wehago_msg += f"- 문의 ID: {cs_infos['inquiry_id']}\n"
    wehago_msg += f"- 검체번호: {cs_infos['sample_id']}\n"
    wehago_msg += f"- 문의 구분: {cs_infos['category1']}\n"
    wehago_msg += f"- 문의 날짜: {cs_infos['inquiry_date']}\n"
    wehago_msg += f"- 상태: {cs_infos['status']}\n"
    wehago_msg += f"- 내부 관련 부서: {cs_infos['related_department']}\n"
    wehago_msg += f"- 서비스 유형: {cs_infos['service_type']}\n"
    wehago_msg += f"- 발신 기관: {cs_infos['inquiry_org']}/{cs_infos['branch']}\n"
    wehago_msg += f"- 발신자: {cs_infos['inquiry_person']}/{cs_infos['inquiry_phone']}\n"
    wehago_msg += f"- 병원명: {cs_infos['hospital_name']}"

    return template_parameters, wehago_msg

def prepare_cs_comment_template(cs_comment_infos: Dict) -> Tuple[Dict, str]:
    """
    CS 댓글 알림을 위한 템플릿 파라미터와 메시지를 준비하는 함수
    """
    #comment_content 파트는 html 제거만 수행
    comment_content_html = cs_comment_infos.get('comment_content', '')
    comment_content_text = strip_html_tags(comment_content_html)

    comment_content_parts = split_text_into_parts(comment_content_text, num_parts=10)
    
    # 템플릿 파라미터 준비 - 명시적으로 표시
    template_parameters = {
        'inquiry_id': shorten(cs_comment_infos['inquiry_id']),
        'inquiry_author': shorten(cs_comment_infos['inquiry_author']),
        'comment_author': shorten(cs_comment_infos['comment_author']),
        'comment_content_1': comment_content_parts[0],
        'comment_content_2': comment_content_parts[1],
        'comment_content_3': comment_content_parts[2],
        'comment_content_4': comment_content_parts[3],
        'comment_content_5': comment_content_parts[4],
        'comment_content_6': comment_content_parts[5],
        'comment_content_7': comment_content_parts[6],
        'comment_content_8': comment_content_parts[7],
        'comment_content_9': comment_content_parts[8],
        'comment_content_10': comment_content_parts[9],
    }

    # 댓글 내용 가져오기 (위하고 메시지용)
    comment_text = clean_text(cs_comment_infos['comment_content'], strip_html=True)
    
    # 위하고 메시지 준비
    wehago_msg = f"[지놈케어 CS 댓글 알림]\n"
    wehago_msg += f"- 문의 ID: {cs_comment_infos['inquiry_id']}\n"
    wehago_msg += f"- 문의 작성자: {cs_comment_infos['inquiry_author']}\n"
    wehago_msg += f"- 댓글 작성자: {cs_comment_infos['comment_author']}\n"
    wehago_msg += f"- 댓글 내용: {comment_text}\n"

    return template_parameters, wehago_msg



def prepare_marketing_info_template(marketing_infos: Dict) -> Tuple[Dict, str]:
    """
    마케팅 방문 알림을 위한 템플릿 파라미터와 메시지를 준비하는 함수
    """
    # 상담내용을 15개 파트로 분할
    discussion_parts = split_text_into_parts(marketing_infos['discussion_details'], strip_html=True, num_parts=15)
    
    # 템플릿 파라미터 준비 - 명시적으로 표시
    template_parameters = {
        'marketing_id': shorten(marketing_infos['marketing_id']),
        'location_name': shorten(marketing_infos['location_name']),
        'full_name': shorten(marketing_infos['full_name']),
        'visit_date': shorten(marketing_infos['visit_date']),
        'person_met': shorten(marketing_infos['person_met']),
        'main_topic': shorten(marketing_infos['main_topic']),
        'discussion_details_1': discussion_parts[0],
        'discussion_details_2': discussion_parts[1],
        'discussion_details_3': discussion_parts[2],
        'discussion_details_4': discussion_parts[3],
        'discussion_details_5': discussion_parts[4],
        'discussion_details_6': discussion_parts[5],
        'discussion_details_7': discussion_parts[6],
        'discussion_details_8': discussion_parts[7],
        'discussion_details_9': discussion_parts[8],
        'discussion_details_10': discussion_parts[9],
        'discussion_details_11': discussion_parts[10],
        'discussion_details_12': discussion_parts[11],
        'discussion_details_13': discussion_parts[12],
        'discussion_details_14': discussion_parts[13],
        'discussion_details_15': discussion_parts[14],
    }
    
    # 상담내용 가져오기 (위하고 메시지용)
    discussion_text = clean_text(marketing_infos['discussion_details'], strip_html=True)
    
    # 위하고 메시지 준비
    wehago_msg = f"[지놈케어 마케팅 등록 알림]\n"
    wehago_msg += f"- 장소: {marketing_infos['location_name']}\n"
    wehago_msg += f"- 담당자: {marketing_infos['full_name']}\n"
    wehago_msg += f"- 방문일자: {marketing_infos['visit_date']}\n"
    wehago_msg += f"- 면담자: {marketing_infos['person_met']}\n"
    wehago_msg += f"- 주요 논의 사항: {marketing_infos['main_topic']}\n"
    wehago_msg += f"- 상담내용: {discussion_text}\n"
    wehago_msg += f"- URL: https://gm.genomecare.net/crm/marketing-visit/?selected_visit={marketing_infos['marketing_id']}/"
    
    return template_parameters, wehago_msg

def prepare_marketing_comment_template(marketing_comment_infos: Dict) -> Tuple[Dict, str]:
    """
    마케팅 방문 댓글 알림을 위한 템플릿 파라미터와 메시지를 준비하는 함수
    """
    
    # 템플릿 파라미터 준비 - 명시적으로 표시
    template_parameters = {
        'marketing_id': shorten(marketing_comment_infos['marketing_id']),
        'full_name': shorten(marketing_comment_infos['full_name']),
        'main_topic': shorten(marketing_comment_infos['main_topic']),
        'comment_author': shorten(marketing_comment_infos['comment_author']),
        'comment_content': shorten(marketing_comment_infos['comment_content']),
    }
    
    # 위하고 메시지 준비
    wehago_msg = f"[지놈케어 마케팅 댓글 알림]\n"
    wehago_msg += f"- 방문기록 작성자: {marketing_comment_infos['full_name']}\n"
    wehago_msg += f"- 주요 논의 사항: {marketing_comment_infos['main_topic']}\n\n"
    wehago_msg += f"- 댓글 작성자: {marketing_comment_infos['comment_author']}\n"
    wehago_msg += f"- 댓글 내용: {marketing_comment_infos['comment_content']}\n"
    wehago_msg += f"- URL: https://gm.genomecare.net/crm/marketing-visit/?selected_visit={marketing_comment_infos['marketing_id']}"
    
    return template_parameters, wehago_msg


def process_html_paragraph(text: str) -> str:
    """
    HTML 텍스트에서 단락 태그(<p>)를 적절히 처리하여 가독성을 높입니다.
    
    Args:
        text: HTML 텍스트
        
    Returns:
        str: 단락 구분이 개선된 텍스트
    """
    if text is None or text == '':
        return ''
    
    try:
        # BeautifulSoup을 사용하여 안전하게 HTML 처리
        soup = bs(text, 'html.parser')
        
        # 단락 태그마다 개행 문자 추가 (다양한 스타일 속성이 있을 수 있음)
        for p_tag in soup.find_all('p'):
            # 내용이 있는 경우에만 처리
            if p_tag.text.strip():
                # 기존 태그를 유지하면서 단락 구분을 위한 개행 추가
                p_tag.append("\n")
        
        # <br> 태그 처리
        for br_tag in soup.find_all('br'):
            br_tag.replace_with("\n")
        
        # HTML을 문자열로 변환
        result = str(soup)
        
        return result
    except Exception as e:
        print(f"Error in process_html_paragraph: {e}")
        return text

def get_notification_template(template_code: str, data: Dict) -> Tuple[Dict, str]:
    """
    템플릿 코드에 따라 알림 템플릿 파라미터와 메시지를 가져오는 함수
    Args:
        template_code: 사용할 템플릿을 식별하는 코드
        data: 알림 정보를 담고 있는 딕셔너리
    Returns:
        템플릿 파라미터와 위하고 메시지를 포함하는 튜플
    """
    
    if template_code == 'gc_cs_info':
        # HTML 단락 처리
        if 'inquiry_answer' in data and isinstance(data['inquiry_answer'], str):
            data['inquiry_answer'] = process_html_paragraph(data['inquiry_answer'])
        return prepare_cs_info_template(data)
    
    elif template_code == 'gc_cs_comment':
        if 'comment_content' in data and isinstance(data['comment_content'], str):
            data['comment_content'] = process_html_paragraph(data['comment_content'])
        return prepare_cs_comment_template(data)
    
    elif template_code == 'gc_marketing_info':
        # HTML 단락 처리
        if 'discussion_details' in data and isinstance(data['discussion_details'], str):
            data['discussion_details'] = process_html_paragraph(data['discussion_details'])
        return prepare_marketing_info_template(data)
    
    elif template_code == 'gc_marketing_comment':
        return prepare_marketing_comment_template(data)
    
    
    # 추가 템플릿 핸들러는 여기에 추가
    raise ValueError(f"알 수 없는 템플릿 코드입니다: {template_code}") 