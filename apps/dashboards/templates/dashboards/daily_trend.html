{% extends "layouts/base.html" %}

{% block title %} 일간 트렌드 분석 {% endblock %}

{% block stylesheets %}
<style>
    .dashboard-card {
        margin-bottom: 1.5rem;
        box-shadow: 0 0 2rem 0 rgba(136, 152, 170, .15);
        border: none;
    }
    .dashboard-card .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.5rem;
        background: #f8f9fe;
        border-bottom: 1px solid #e9ecef;
    }
    .dashboard-card .card-header h5 {
        margin: 0;
        font-weight: 600;
    }
    .dashboard-export-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        transition: all 0.2s;
    }
    .dashboard-stat-card {
        transition: transform 0.2s;
        border: none;
        box-shadow: 0 0 2rem 0 rgba(136, 152, 170, .15);
    }
    .dashboard-stat-card:hover {
        transform: translateY(-5px);
    }
    .dashboard-chart-container {
        position: relative;
        padding: 1rem;
        height: 450px;
    }
</style>
{% endblock stylesheets %}

{% block content %}
<!-- Header -->
<div class="header pb-6">
    <div class="container-fluid">
        <div class="header-body">
            <div class="row align-items-center py-4">
                <div class="col-lg-6 col-7">
                    <h6 class="h2 d-inline-block mb-0">일간 트렌드 분석</h6>
                    <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                        <ol class="breadcrumb breadcrumb-links">
                            <li class="breadcrumb-item"><a href="#"><i class="fas fa-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="#">대시보드</a></li>
                            <li class="breadcrumb-item active" aria-current="page">일간 트렌드</li>
                        </ol>
                    </nav>
                </div>
            </div>

            <!-- Include Date Range Form -->
            {% include 'dashboards/components/date_range_form.html' %}

            <!-- Statistics Cards -->
            <div class="row">
                <div class="col-xl-3 col-md-6">
                    <div class="card dashboard-stat-card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <h5 class="card-title text-uppercase text-muted mb-0">출고 완료</h5>
                                    <span class="h2 font-weight-bold mb-0">{{ completed }}/{{ total_data }}</span>
                                    <div class="progress progress-xs mt-3 mb-0">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             aria-valuenow="{{ progress }}" aria-valuemin="0" aria-valuemax="100" 
                                             style="width: {{ progress }}%;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card dashboard-stat-card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <h5 class="card-title text-uppercase text-muted mb-0">고위험 사례</h5>
                                    <span class="h2 font-weight-bold mb-0">{{ not_low_risk }}/{{ total_data }}</span>
                                    <div class="progress progress-xs mt-3 mb-0">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             aria-valuenow="{{ not_low_risk_progress }}" aria-valuemin="0" aria-valuemax="100" 
                                             style="width: {{ not_low_risk_progress }}%;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card dashboard-stat-card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <h5 class="card-title text-uppercase text-muted mb-0">재검사 + 재채혈</h5>
                                    <span class="h2 font-weight-bold mb-0">{{ not_first_test }}/{{ total_data }}</span>
                                    <div class="progress progress-xs mt-3 mb-0">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             aria-valuenow="{{ not_first_test_progress }}" aria-valuemin="0" aria-valuemax="100" 
                                             style="width: {{ not_first_test_progress }}%;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card dashboard-stat-card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <h5 class="card-title text-uppercase text-muted mb-0">4일 초과 TAT</h5>
                                    <span class="h2 font-weight-bold mb-0">50/62</span>
                                    <div class="progress progress-xs mt-3 mb-0">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             aria-valuenow="90" aria-valuemin="0" aria-valuemax="100" 
                                             style="width: 90%;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Page content -->
<div class="container-fluid mt--6">
    <!-- Jedan Trend -->
    <div class="card dashboard-card">
        <div class="card-header">
            <h5 class="h3 mb-0"><i class="fa-sharp fa-solid fa-hospital"></i> 제단별 일간 샘플 입고 트렌드</h5>
            <a href="?export=jedan_trend{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" 
               class="btn btn-sm dashboard-export-btn">
                <i class="fas fa-download"></i> 내보내기
            </a>
        </div>
        <div class="card-body">
            {{ jedan_div|safe }}
        </div>
    </div>

    <!-- Service Type Trend -->
    <div class="card dashboard-card">
        <div class="card-header">
            <h5 class="h3 mb-0"><i class="fa-sharp fa-solid fa-hospital"></i> 서비스 유형별 일간 샘플 입고 트렌드</h5>
            <a href="?export=service_trend{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" 
               class="btn btn-sm dashboard-export-btn">
                <i class="fas fa-download"></i> 내보내기
            </a>
        </div>
        <div class="card-body">
            {{ service_div|safe }}
        </div>
    </div>

    <!-- Result Trend -->
    <div class="card dashboard-card">
        <div class="card-header">
            <h5 class="h3 mb-0"><i class="fa-sharp fa-solid fa-hospital"></i> 결과 트렌드</h5>
            <a href="?export=result_trend{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" 
               class="btn btn-sm dashboard-export-btn">
                <i class="fas fa-download"></i> 내보내기
            </a>
        </div>
        <div class="card-body">
            {{ final_result_div|safe }}
        </div>
    </div>

    <!-- Jedan Cumulative Sum -->
    <div class="card dashboard-card">
        <div class="card-header">
            <h5 class="h3 mb-0"><i class="fa-sharp fa-solid fa-hospital"></i> 제단별 누적 합계</h5>
            <a href="?export=jedan_cumsum{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" 
               class="btn btn-sm dashboard-export-btn">
                <i class="fas fa-download"></i> 내보내기
            </a>
        </div>
        <div class="card-body">
            {{ jedan_cum_sum|safe }}
        </div>
    </div>

    <div class="page-header-title">
        <div class='p-3 mb-2 bg-success text-white text-center h5 font-weight-bold'>
            <h3>개발: Krishna (R&D Genomecare)</h3>
            <p class="mb-0">문의사항은 AIM 팀에 연락해 주세요</p>
        </div>
    </div>

    {% include 'includes/footer.html' %}
</div>
{% endblock content %}

{% block javascripts %}
<script>
    // Add any custom JavaScript here if needed
</script>
{% endblock javascripts %}