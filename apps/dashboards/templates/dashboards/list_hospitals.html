{% extends "layouts/base.html" %}

{% block title %} Hospital Visualizations {% endblock %}

<!-- Specific Page CSS goes HERE  -->
{% block stylesheets %}
<style>
    .stat-box {
        padding: 5px;
        border-radius: 4px;
        margin-bottom: 5px;
        font-weight: bold;
    }
    .stat-label {
        font-size: 0.8rem;
        color: #555;
    }
    .stat-value {
        font-size: 1rem;
        font-weight: bold;
    }
    .high-risk {
        background-color: rgba(255, 99, 132, 0.2);
        border-left: 3px solid #ff6384;
    }
    .border-risk {
        background-color: rgba(255, 205, 86, 0.2);
        border-left: 3px solid #ffcd56;
    }
    .retest {
        background-color: rgba(54, 162, 235, 0.2);
        border-left: 3px solid #36a2eb;
    }
</style>
{% endblock stylesheets %}

{% block content %}

<!-- Header -->
<div class="header pb-3">
    <div class="container-fluid">
        <div class="header-body">
            <div class="row align-items-center py-1">
                <h6 class="h2 d-inline-block mb-0"> 병원 분석 페이지 입니다. <strong style="color:red;"> ****빨강색으로 표시된 병원은 아직 환자 입고 현황 없습니다.</strong>  </h6>

            </div>
        </div>
    </div>
</div>

<!-- Page content -->

<div class="container-fluid">

    <div class="card">

        {% for jedan in all_hospitals %}

        <div class='p-3 mb-2 bg-success text-dark text-center h1 font-weight-bold'>
             &nbsp;&nbsp;  <strong style="color:rgb(8, 41, 228);"> {{jedan.jedan_name }}   </strong>  재단으로 등록된 병원 
        </div>

        <div class="row">

            {% for hospital in jedan.hospital_info.all %}

            <div class="col-xl-4">
                <div class="card mb-3">
                    <div class="card-header p-2 {% if hospital.number_of_patients == 0 %} bg-danger {% else %} bg-info {% endif %}">
                        <div class="d-flex justify-content-between align-items-center">
                            <!-- Hospital name as link to detail page -->
                            <a href="{% url 'hospital_report_detail' hospital.id %}" class="text-white font-weight-bold">
                                <h4 class="mb-0">{{hospital.hospital_name}}</h4>
                            </a>
                            <!-- Edit icon -->
                            <a href="{% url 'edit_hospital' hospital.id %}" class="text-white" title="Edit Hospital">
                                <i class="fas fa-pencil-alt"></i>
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-3">
                        <div class="row">
                            <!-- Left column - Basic info -->
                            <div class="col-md-6">
                                <p class="mb-1"><strong>지점:</strong> {{hospital.jedan_branch.jedan_branch_name}}</p>
                                <p class="mb-1"><strong>주소:</strong> {{hospital.si_address}}</p>
                                <p class="mb-1"><strong>전화번호:</strong> {{hospital.phone_number}}</p>
                                <p class="mb-1"><strong>의사:</strong> {{hospital.number_of_doctors}}</p>
                                <p class="mb-1"><strong>환자:</strong> {{hospital.number_of_patients}}</p>
                                <p class="mb-1"><strong>영업 담당자:</strong> {{hospital.marketing_manager|default:"미지정"}}</p>
                            </div>
                            
                            <!-- Right column - Stats -->
                            <div class="col-md-6">
                                <div class="stat-box high-risk">
                                    <span class="stat-label">High Risk %</span>
                                    <div class="stat-value">{{hospital.high_risk_percentage|default:"0"}}%</div>
                                </div>
                                
                                <div class="stat-box border-risk">
                                    <span class="stat-label">Border %</span>
                                    <div class="stat-value">{{hospital.border_percentage|default:"0"}}%</div>
                                </div>
                                
                                <div class="stat-box retest">
                                    <span class="stat-label">Re-test %</span>
                                    <div class="stat-value">{{hospital.retest_percentage|default:"0"}}%</div>
                                </div>
                                
                                <!-- Action buttons -->
                                <div class="mt-3">
                                    <a href="{% url 'hospital_report_detail' hospital.id %}" class="btn btn-sm btn-primary btn-block">
                                        <i class="fas fa-chart-bar"></i> 상세 보고서
                                    </a>
                                    <a href="{% url 'hospital_report_detail' hospital.id %}" class="btn btn-sm btn-info btn-block mt-2">
                                        <i class="fas fa-info-circle"></i> 상세 정보
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {% endfor %}

        </div>
        <br>

        {% endfor %}

    </div>

</div>

<div class="page-header-title">
    <div class='p-3 mb-2 bg-success text-white text-center h5 font-weight-bold'>
        <h3> Developed By R&D Genomecare,</h3> for any query contact AIM Team
    </div>
</div>

{% include 'includes/footer.html' %}

</div>

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}

{% endblock javascripts %}
