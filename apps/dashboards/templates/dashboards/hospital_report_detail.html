{% extends "layouts/base.html" %}

{% block title %} 병원 상세 보고서 {% endblock %}

<!-- Specific Page CSS goes HERE  -->
{% block stylesheets %}
<style>
    .dashboard-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }
    
    .dashboard-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }
    
    .dashboard-card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 1rem;
        border-radius: 8px 8px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .dashboard-card-header h5 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0;
        color: #2d3748;
    }
    
    .dashboard-card-body {
        padding: 1.5rem;
    }
    
    .dashboard-stat-card {
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
    }
    
    .dashboard-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
    }
    
    .dashboard-stat-card:nth-child(1) {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid #e9ecef;
    }
    .dashboard-stat-card:nth-child(1)::before {
        background: #4e73df;
    }
    
    .dashboard-stat-card:nth-child(2) {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid #e9ecef;
    }
    .dashboard-stat-card:nth-child(2)::before {
        background: #1cc88a;
    }
    
    .dashboard-stat-card:nth-child(3) {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid #e9ecef;
    }
    .dashboard-stat-card:nth-child(3)::before {
        background: #36b9cc;
    }
    
    .dashboard-stat-card:nth-child(4) {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid #e9ecef;
    }
    .dashboard-stat-card:nth-child(4)::before {
        background: #f6c23e;
    }
    
    .dashboard-stat-card:nth-child(5) {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid #e9ecef;
    }
    .dashboard-stat-card:nth-child(5)::before {
        background: #e74a3b;
    }
    
    .dashboard-stat-card:nth-child(6) {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid #e9ecef;
    }
    .dashboard-stat-card:nth-child(6)::before {
        background: #858796;
    }
    
    .dashboard-stat-card h3 {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
        color: #5a5c69;
        font-weight: 600;
    }
    
    .dashboard-stat-card .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.25rem;
    }
    
    .dashboard-stat-card .stat-label {
        font-size: 0.8rem;
        color: #858796;
    }
    
    .dashboard-export-btn {
        background: #4e73df;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .dashboard-export-btn:hover {
        background: #224abe;
        transform: translateY(-1px);
        color: white;
        text-decoration: none;
    }
    
    .page-title {
        font-size: 2rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 1rem;
    }
    
    .chart-container {
        min-height: 600px;
    }

    .date-filter-form {
        background: #fff;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .date-filter-form .form-group {
        margin-bottom: 0;
    }

    .date-filter-form label {
        font-weight: 600;
        margin-right: 0.5rem;
    }

    .date-filter-form .btn-primary {
        background: #4e73df;
        border: none;
        padding: 0.5rem 1.5rem;
    }

    .date-filter-form .btn-primary:hover {
        background: #224abe;
    }

    .section-title {
        font-size: 1.75rem;
        font-weight: 700;
        color: #2d3748;
        text-align: center;
        margin: 2rem 0 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 3px solid #4e73df;
        width: fit-content;
        margin-left: auto;
        margin-right: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="page-title">{{ hospital.hospital_name }} 상세 보고서</h1>
    </div>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'hospital_report' %}">병원 보고서</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ hospital.hospital_name }}</li>
        </ol>
    </nav>

    <!-- Date Filter -->
    <div class="date-filter-form">
        <form method="get" class="row align-items-end">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="start_date">시작일:</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" 
                           value="{{ start_date|date:'Y-m-d' }}">
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="end_date">종료일:</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" 
                           value="{{ end_date|date:'Y-m-d' }}">
                </div>
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary">필터 적용</button>
            </div>
        </form>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-xl-2 col-md-4 mb-4">
            <div class="dashboard-stat-card">
                <h3>총 검체 수</h3>
                <div class="stat-value">{{ total_samples|default:"0" }}</div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 mb-4">
            <div class="dashboard-stat-card">
                <h3>평균 일일 검체</h3>
                <div class="stat-value">{{ avg_samples_per_day|default:"0" }}</div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 mb-4">
            <div class="dashboard-stat-card">
                <h3>평균 월간 검체</h3>
                <div class="stat-value">{{ avg_samples_per_month|default:"0" }}</div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 mb-4">
            <div class="dashboard-stat-card">
                <h3>평균 처리 시간</h3>
                <div class="stat-value">{{ avg_processing_time|default:"0" }}</div>
                <div class="stat-label">일</div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 mb-4">
            <div class="dashboard-stat-card">
                <h3>총 의사 수</h3>
                <div class="stat-value">{{ total_doctors|default:"0" }}</div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 mb-4">
            <div class="dashboard-stat-card">
                <h3>고위험 검체 비율</h3>
                <div class="stat-value">{{ high_risk_rate|default:"0" }}%</div>
            </div>
        </div>
    </div>

    <!-- Pie Charts -->
    <div class="row">
        <div class="col-xl-6 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h5>태아수 비율</h5>
                    <a href="?export=fetus" class="dashboard-export-btn">
                        <i class="fas fa-download"></i> 엑셀 다운로드
                    </a>
                </div>
                <div class="dashboard-card-body">
                    {% autoescape off %}
                    {{ fetus_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>

        <div class="col-xl-6 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h5>검사 방법 비율</h5>
                    <a href="?export=test" class="dashboard-export-btn">
                        <i class="fas fa-download"></i> 엑셀 다운로드
                    </a>
                </div>
                <div class="dashboard-card-body">
                    {% autoescape off %}
                    {{ test_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>

        <div class="col-xl-6 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h5>검사 항목</h5>
                    <a href="?export=service" class="dashboard-export-btn">
                        <i class="fas fa-download"></i> 엑셀 다운로드
                    </a>
                </div>
                <div class="dashboard-card-body">
                    {% autoescape off %}
                    {{ service_type_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>

        <div class="col-xl-6 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h5>분석 결과 비율</h5>
                    <a href="?export=result" class="dashboard-export-btn">
                        <i class="fas fa-download"></i> 엑셀 다운로드
                    </a>
                </div>
                <div class="dashboard-card-body">
                    {% autoescape off %}
                    {{ final_result_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>

        <div class="col-xl-6 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h5>의사 비율</h5>
                    <a href="?export=doctor" class="dashboard-export-btn">
                        <i class="fas fa-download"></i> 엑셀 다운로드
                    </a>
                </div>
                <div class="dashboard-card-body">
                    {% autoescape off %}
                    {{ doctor_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>

        <div class="col-xl-6 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h5>IVF 검체 비율</h5>
                    <a href="?export=ivf" class="dashboard-export-btn">
                        <i class="fas fa-download"></i> 엑셀 다운로드
                    </a>
                </div>
                <div class="dashboard-card-body">
                    {% autoescape off %}
                    {{ ivf_treatment_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Analysis Section -->
    <div class="row mt-4">
        <div class="col-12">
            <h2 class="section-title">월간 분석</h2>
        </div>
    </div>

    <!-- Monthly Charts -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h5>월간 검사 항목 현황</h5>
                    <a href="?export=service_monthly" class="dashboard-export-btn">
                        <i class="fas fa-download"></i> 엑셀 다운로드
                    </a>
                </div>
                <div class="dashboard-card-body">
                    <div class="chart-container">
                        {{ service_monthly_div|safe }}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h5>의사별 월간 입고 현황</h5>
                    <a href="?export=doctor_monthly" class="dashboard-export-btn">
                        <i class="fas fa-download"></i> 엑셀 다운로드
                    </a>
                </div>
                <div class="dashboard-card-body">
                    <div class="chart-container">
                        {{ doctor_monthly_div|safe }}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h5>월간 IVF 검체 현황</h5>
                    <a href="?export=ivf_monthly" class="dashboard-export-btn">
                        <i class="fas fa-download"></i> 엑셀 다운로드
                    </a>
                </div>
                <div class="dashboard-card-body">
                    <div class="chart-container">
                        {{ ivf_monthly_div|safe }}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h5>월간 결과 현황</h5>
                    <a href="?export=result_monthly" class="dashboard-export-btn">
                        <i class="fas fa-download"></i> 엑셀 다운로드
                    </a>
                </div>
                <div class="dashboard-card-body">
                    <div class="chart-container">
                        {{ final_result_div_month|safe }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize any additional functionality here
    });
</script>
{% endblock %}