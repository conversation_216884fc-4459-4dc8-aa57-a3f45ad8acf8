{% extends "layouts/base.html" %}
{% extends "layouts/base.html" %}

{% block title %} Alternative Dashboard {% endblock %}

<!-- Specific Page CSS goes HERE  -->
{% block stylesheets %}{% endblock stylesheets %}

{% block content %}

<!-- Header -->
<div class="header pb-6">
    <div class="container-fluid">
        <div class="header-body">
            <div class="row align-items-center py-4">
                <div class="col-lg-6 col-7 align-items-center ">
                    <h6 class="h2 d-inline-block mb-0 "> &nbsp; &nbsp; &nbsp; &nbsp; 금일 데이터 현황  </h6>
                    
                </div>

                <div class="col-lg-6 col-7">
                    <h6 class="h2 d-inline-block mb-0"> 금일 수정 데이터 현황  </h6>
                    
                </div>


                
            </div>
        </div>
    </div>
</div>

<!-- Page content -->

<div class="container-fluid mt--6">

    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-gradient-primary border-0">

                <div class="card-body">
                    

                            <table div class="table" style="margin:0px">
                                <table class='table table-bordered '>
                                    <thead class="thead-dark">

                                    <tr>
                                        <th> <h3 class="text-white"> 입고 </h3> </th>
                                        <th> <h3 class="text-white"> 판독 </h3> </th>
                                        <th> <h3 class="text-white"> 출고 </h3> </th>
                                        <th> <h3 class="text-white"> 출고 </h3> </th>
                                    </tr>

                                </thead>

                                    <tr>
                                        <td class="bg-success text-white"> <h3> {{ today_entry }}</h3> </td>
                                        <td class="bg-success text-white"> <h3> {{ today_confirm }}</h3> </td>
                                        <td class="bg-success text-white"> <h3> {{ today_release }}</h3> </td>
                                        <td class="bg-success text-white"> <h3> {{ release_finished }}</h3> </td>

                                        
                                    </tr>
                                </table>
                            </table>

                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6">
            <div class="card bg-gradient-secondary border-0" style="margin:0px;padding:0px" >

                <div class="card-body table-responsive " style="margin:0px;padding:0px" >
                    
                        <div class="col">
                            {{ result_confirm_table | safe }}
                            
                        </div>
                    

                </div>
            </div>
        </div>
        
        <div class="col-xl-4 col-md-6">
            <div class="card bg-primary-default border-0">

                <div class="card-body" style="margin:0px;padding:0px" >
                        
                            <table div class="table" style="margin:0px">
                                <table class='table table-bordered '>
                                    <thead class="thead-dark">

                                    <tr>
                                        {% for key, _ in today_edited_samples.items %}
                                        <th> <h3 class="text-white"> {{ key }} </h3> </th>
                                        {% endfor %}
                                    </tr>
                                </thead>

                                    <tr>
                                        {% for _, value in today_edited_samples.items %}

                                        <td>{{ value }}</td>

                                        {% endfor %}
                                    </tr>
                                </table>
                            </table>

                        
                </div>

            </div>

            
        </div>

        



        {% comment %} <div class="col-xl-1 col-md-6">
            <div class="card bg-gradient-primary border-0">

                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0 text-white"> 뭐 필요해요 ? </h5>
                            <span class="h2 font-weight-bold mb-0 text-white">{{ completed }}/ {{ total_data }} </span>
                            <div class="progress progress-xs mt-3 mb-0">
                                <div class="progress-bar bg-success" role="progressbar" aria-valuenow="30"
                                    aria-valuemin="0" aria-valuemax="100" style="width: {{ progress }}%;">
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div> {% endcomment %}














        <div>
        </div>

    </div>

    <!-- Header -->
    <div class="header pb-6">
        <div class="container-fluid">
            <div class="header-body">
                <div class="row align-items-center py-4">

                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid mt--8">

        <div class="row">
            <div class="col-xl-6">

                <div class="card">

                    <div class="card-header">
                        <!-- Surtitle -->
                        <h6 class="surtitle"> </h6>
                        <!-- Title -->
                        <h5 class="h3 mb-0">

                            <i class="fa-solid fa-building"></i> 최근 {{ recent_days }}일간의 재단 분포 </h5>

                    </div>

                    <div class="card-body">

                        {{ jedan_div | safe }}

                    </div>

                </div>
            </div>

            <div class="col-xl-6">

                <div class="card">

                    <div class="card-header">

                        <h5 class="h3 mb-0"><i class="fa-solid fa-baby"></i> 최근 {{ recent_days }}일간의 서비스 종류 </h5>
                    </div>

                    <div class="card-body">

                        {{ service_div | safe }}

                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xl-6">

                <div class="card">

                    <div class="card-header">

                        <h5 class="h3 mb-0"><i class="fa-solid fa-vial-circle-check"></i> 최근{{ recent_days }}일간의 입고 샘플
                            재실험 여부
                        </h5>
                    </div>

                    <div class="card-body">

                        <!-- Chart wrapper -->
                        {{ test_type_div |safe }}

                    </div>
                </div>
            </div>
            <div class="col-xl-6">

                <div class="card">

                    <div class="card-header">

                        <h5 class="h3 mb-0"><i class="fa-solid fa-calendar-days"></i> 최근{{ recent_days }} 보고 결과 현황 
                        </h5>
                    </div>

                    <div class="card">

                        <div class="card-body">
                            {{ sample_result_div | safe }}

                        </div>
                    </div>

                </div>
            </div>
        </div>



        <div class="row">
            <div class="col-xl-6">

                <div class="card">

                    <div class="card-header">
                        <!-- Title -->
                        <h5 class="h3 mb-0"><i class="fa-solid fa-square-poll-vertical"></i> 최근 {{recent_days}}일간의 재단별 출고 현황  </h5>
                    </div>

                    <div class="card-body">
                        {{ final_result_div | safe  }}
                    </div>
                </div>
            </div>

            <div class="col-xl-6">

                <div class="card">

                    <div class="card-header">
                        <!-- Title -->
                        <h5 class="h3 mb-0"><i class="fa-solid fa-square-poll-vertical"></i> 최근 {{recent_days}}일간의 재단별 입고 현황  </h5>
                    </div>



                <div class="card-body">

                    {{ entry_date_div |safe }}
                </div>

            </div>
            </div>
        </div>

        <div class="card">

            <div class="card-header">

                <!-- Title -->
                <h5 class="h3 mb-0"> <i class="fa-sharp fa-solid fa-hospital"></i> 최근 {{recent_days}}일간의 재단-지점 분포 </h5>
            </div>

            <div class="card-body">

                {{ jedan_branch_div | safe }}

            </div>
        </div>
        <div class="card">

            <div class="card-header">
                <!-- Title -->
                <h5 class="h3 mb-0"> <i class="fa-sharp fa-solid fa-hospital"></i> 최근 {{recent_days}}일간의 의료기관 분포 </h5>
            </div>

            <div class="card-body">

                {{ hospital_div | safe }}

            </div>
        </div>

        <div class="page-header-title">
            <div class='p-3 mb-2 bg-success text-white text-center  h5 font-weight-bold'>
                <h3> Developed By R&D Genomecare,</h3> for any query contact AIM Team
            </div>

        </div>

        {% include 'includes/footer.html' %}

    </div>

    {% endblock content %}

    <!-- Specific Page JS goes HERE  -->
    {% block javascripts %}

    {% endblock javascripts %}