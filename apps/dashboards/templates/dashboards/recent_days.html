{% extends "layouts/base.html" %}

{% block title %} NIPT Daily Dashboard {% endblock %}

{% block stylesheets %}
<style>
    .dashboard-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }
    
    .dashboard-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .stats-card {
        background: linear-gradient(45deg, #4e73df, #224abe);
        color: white;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        height: 100%;
    }
    
    .stats-card h3 {
        font-size: 1rem;
        margin-bottom: 0.5rem;
        opacity: 0.9;
    }
    
    .stats-card .value {
        font-size: 1.5rem;
        font-weight: bold;
    }
    
    .date-filter {
        background: #fff;
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        margin-bottom: 1.5rem;
    }

    .date-filter .form-group {
        margin-bottom: 0;
    }

    .date-filter label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #344767;
    }

    .date-filter .form-control {
        border: 1px solid #d1d7e0;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }

    .date-filter .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        border-radius: 0.375rem;
    }

    .date-filter .btn-primary {
        background-color: #5e72e4;
        border-color: #5e72e4;
    }

    .date-filter .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .date-filter .btn:hover {
        opacity: 0.9;
    }

    .date-filter .form-text {
        font-size: 0.75rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    
    .export-btn {
        background: #4e73df;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        margin-bottom: 1rem;
    }
    
    .export-btn:hover {
        background: #224abe;
        color: white;
        text-decoration: none;
    }
    
    @media (max-width: 768px) {
        .stats-card {
            margin-bottom: 1rem;
        }
        
        .stats-card .value {
            font-size: 1.25rem;
        }
        
        .card-body {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="header pb-6">
        <div class="container-fluid">
            <div class="header-body">
                <div class="row align-items-center py-4">
                    <div class="col-lg-6">
                        <h6 class="h2 d-inline-block mb-0">NIPT Daily Dashboard</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="date-filter">
        <form method="get" class="row align-items-end">
            <div class="col-md-4">
                <label for="end_date">종료일</label>
                <input type="date" class="form-control" id="end_date" name="end_date" 
                       value="{{ end_date|date:'Y-m-d' }}" max="{{ today|date:'Y-m-d' }}">
                <small class="form-text">데이터를 보여줄 마지막 날짜를 선택하세요</small>
            </div>
            <div class="col-md-4">
                <label for="days">기간</label>
                <select class="form-control" id="days" name="days">
                    <option value="1" {% if days == 1 %}selected{% endif %}>1일</option>
                    <option value="7" {% if days == 7 %}selected{% endif %}>7일</option>
                    <option value="14" {% if days == 14 %}selected{% endif %}>14일</option>
                    <option value="30" {% if days == 30 %}selected{% endif %}>30일</option>
                </select>
                <small class="form-text">선택한 종료일로부터 보여줄 기간을 선택하세요</small>
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary me-2">적용</button>
                <button type="button" id="resetBtn" class="btn btn-secondary">초기화</button>
            </div>
        </form>
    </div>

    <!-- Today's Stats -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <h3>입고</h3>
                <div class="value">{{ today_entry }}</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <h3>판독</h3>
                <div class="value">{{ today_confirm }}</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <h3>출고</h3>
                <div class="value">{{ today_release }}</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <h3>출고 완료</h3>
                <div class="value">{{ release_finished }}</div>
            </div>
        </div>
    </div>

    <!-- Main Charts -->
    <div class="row">
        <div class="col-xl-6">
            <div class="dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">재단별 분포</h5>
                    <a href="?export=jedan&end_date={{ end_date|date:'Y-m-d' }}&days={{ days }}" 
                       class="export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body">
                    {{ jedan_div|safe }}
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">서비스 종류</h5>
                    <a href="?export=service&end_date={{ end_date|date:'Y-m-d' }}&days={{ days }}" 
                       class="export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body">
                    {{ service_div|safe }}
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Charts -->
    <div class="row">
        <div class="col-xl-6">
            <div class="dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">재실험 여부</h5>
                    <a href="?export=test_type&end_date={{ end_date|date:'Y-m-d' }}&days={{ days }}" 
                       class="export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body">
                    {{ test_type_div|safe }}
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">결과 현황</h5>
                    <a href="?export=result&end_date={{ end_date|date:'Y-m-d' }}&days={{ days }}" 
                       class="export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body">
                    {{ sample_result_div|safe }}
                </div>
            </div>
        </div>
    </div>

    <!-- Wide Charts -->
    <div class="dashboard-card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">재단-지점 분포</h5>
            <a href="?export=jedan_branch&end_date={{ end_date|date:'Y-m-d' }}&days={{ days }}" 
               class="export-btn">
                <i class="fas fa-download"></i> 내보내기
            </a>
        </div>
        <div class="card-body">
            {{ jedan_branch_div|safe }}
        </div>
    </div>

    <div class="dashboard-card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">의료기관 분포</h5>
            <a href="?export=hospital&end_date={{ end_date|date:'Y-m-d' }}&days={{ days }}" 
               class="export-btn">
                <i class="fas fa-download"></i> 내보내기
            </a>
        </div>
        <div class="card-body">
            {{ hospital_div|safe }}
        </div>
    </div>

    <!-- Footer -->
    <div class="page-header-title">
        <div class='p-3 mb-2 bg-success text-white text-center h5 font-weight-bold'>
            <h3>Developed By R&D Genomecare</h3>
            <p class="mb-0">For any query contact AIM Team</p>
        </div>
    </div>

    {% include 'includes/footer.html' %}
</div>
{% endblock content %}

{% block javascripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle reset button
        document.getElementById('resetBtn').addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = window.location.pathname;
        });

        // Handle export buttons to prevent page refresh
        document.querySelectorAll('.export-btn').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const url = this.getAttribute('href');
                window.location.href = url;
            });
        });
    });
</script>
{% endblock javascripts %}