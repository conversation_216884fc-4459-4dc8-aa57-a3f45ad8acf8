{% load static %}

<div class="card dashboard-card mb-4">
    <div class="card-header">
        <h5 class="h3 mb-0"><i class="fas fa-calendar-alt"></i> 기간 필터</h5>
    </div>
    <div class="card-body dashboard-card-body">
        <form method="get" class="row align-items-end">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="start_date" class="form-control-label">시작일</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" 
                           value="{{ request.GET.start_date|default:'' }}"
                           max="{{ request.GET.end_date|default:'' }}">
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="end_date" class="form-control-label">종료일</label>
                    <input type="date" class="form-control" id="end_date" name="end_date"
                           value="{{ request.GET.end_date|default:'' }}"
                           min="{{ request.GET.start_date|default:'' }}">
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <button type="submit" class="btn btn-sm dashboard-export-btn">
                        <i class="fas fa-filter"></i> 필터 적용
                    </button>
                    {% if request.GET.start_date or request.GET.end_date %}
                    <a href="{{ request.path }}" class="btn btn-sm btn-secondary">
                        <i class="fas fa-times"></i> 필터 초기화
                    </a>
                    {% endif %}
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');

    // Update max date of start date when end date changes
    endDate.addEventListener('change', function() {
        startDate.max = this.value;
    });

    // Update min date of end date when start date changes
    startDate.addEventListener('change', function() {
        endDate.min = this.value;
    });
});
</script> 