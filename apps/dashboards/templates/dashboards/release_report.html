{% extends "layouts/base.html" %}

{% block title %} 출고 관리 보고서 {% endblock %}

<!-- Specific Page CSS goes HERE  -->
{% block stylesheets %}
<style>
    .dashboard-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }
    
    .dashboard-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }
    
    .dashboard-card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 1rem;
        border-radius: 8px 8px 0 0;
    }
    
    .dashboard-card-body {
        padding: 1.5rem;
    }
    
    .dashboard-stat-card {
        background: linear-gradient(45deg, #4e73df, #224abe);
        color: white;
        border-radius: 8px;
        padding: 1.25rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    
    .dashboard-stat-card h3 {
        font-size: 1rem;
        margin-bottom: 0.5rem;
        opacity: 0.9;
        font-weight: 500;
    }
    
    .dashboard-stat-card .stat-value {
        font-size: 1.75rem;
        font-weight: bold;
        margin-bottom: 0.25rem;
    }
    
    .dashboard-stat-card .stat-label {
        font-size: 0.85rem;
        opacity: 0.9;
    }

    .stats-row {
        margin-bottom: 2rem;
    }

    .stats-row .col {
        padding: 0 0.5rem;
    }
    
    .dashboard-export-btn {
        background: #4e73df;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .dashboard-export-btn:hover {
        background: #224abe;
        transform: translateY(-1px);
    }
    
    .dashboard-export-btn i {
        margin-right: 0.5rem;
    }
    
    .dataframe {
        border-collapse: collapse;
        width: 100%;
    }

    .dataframe th {
        border: 2px solid #dee2e6;
        background-color: #4e73df;
        color: white;
        padding: 8px;
        text-align: center;
        font-size: 14px;
    }

    .dataframe td {
        border: 1px solid #dee2e6;
        padding: 8px;
        text-align: center;
        font-size: 14px;
        color: #011f4b;
    }
    
    .date-filter-form {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
    }
    
    .date-filter-form .form-group {
        margin-bottom: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">출고 관리 보고서</h1>
    </div>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'release_report' %}">분석</a></li>
            <li class="breadcrumb-item active" aria-current="page">출고 관리</li>
        </ol>
    </nav>

    <!-- Date Filter Form -->
    <div class="date-filter-form">
        <form method="get" class="form-inline">
            <div class="form-group mr-3">
                <label for="start_date" class="mr-2">시작일:</label>
                <input type="date" class="form-control" id="start_date" name="start_date" 
                       value="{{ start_date|default:'' }}">
            </div>
            <div class="form-group mr-3">
                <label for="end_date" class="mr-2">종료일:</label>
                <input type="date" class="form-control" id="end_date" name="end_date" 
                       value="{{ end_date|default:'' }}">
            </div>
            <button type="submit" class="btn btn-primary">필터 적용</button>
        </form>
    </div>

    <!-- Combined Statistics Row -->
    <div class="row stats-row">
        <div class="col">
            <div class="dashboard-stat-card">
                <h3>총 입고 건수</h3>
                <div class="stat-value">{{ total_entry|default:"0" }}</div>
            </div>
        </div>
        <div class="col">
            <div class="dashboard-stat-card">
                <h3>평균 일일 입고</h3>
                <div class="stat-value">{{ avg_entry|default:"0" }}</div>
            </div>
        </div>
        <div class="col">
            <div class="dashboard-stat-card">
                <h3>최대 일일 입고</h3>
                <div class="stat-value">{{ max_entry|default:"0" }}</div>
                <div class="stat-label">{{ max_entry_date|default:"" }}</div>
            </div>
        </div>
        <div class="col">
            <div class="dashboard-stat-card">
                <h3>평균 처리 시간</h3>
                <div class="stat-value">{{ avg_processing_time|default:"0" }}</div>
                <div class="stat-label">일</div>
            </div>
        </div>
        <div class="col">
            <div class="dashboard-stat-card">
                <h3>총 출고 건수</h3>
                <div class="stat-value">{{ total_release|default:"0" }}</div>
            </div>
        </div>
        <div class="col">
            <div class="dashboard-stat-card">
                <h3>평균 일일 출고</h3>
                <div class="stat-value">{{ avg_release|default:"0" }}</div>
            </div>
        </div>
        <div class="col">
            <div class="dashboard-stat-card">
                <h3>최대 일일 출고</h3>
                <div class="stat-value">{{ max_release|default:"0" }}</div>
                <div class="stat-label">{{ max_release_date|default:"" }}</div>
            </div>
        </div>
        <div class="col">
            <div class="dashboard-stat-card">
                <h3>출고 완료율</h3>
                <div class="stat-value">{{ completion_rate|default:"0" }}%</div>
            </div>
        </div>
    </div>

    <!-- Entry Count Table -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">입고 재단 현황</h5>
                        <a href="?export=entry_count{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" 
                           class="dashboard-export-btn">
                            <i class="fas fa-download"></i> 내보내기
                        </a>
                    </div>
                </div>
                <div class="dashboard-card-body">
                    <div class="table-responsive">
                        {{ entry_count_html|safe }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Release Count Table -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">출고 재단 현황</h5>
                        <a href="?export=release_count{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" 
                           class="dashboard-export-btn">
                            <i class="fas fa-download"></i> 내보내기
                        </a>
                    </div>
                </div>
                <div class="dashboard-card-body">
                    <div class="table-responsive">
                        {{ release_count_html|safe }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize any additional functionality here
    });
</script>
{% endblock %}