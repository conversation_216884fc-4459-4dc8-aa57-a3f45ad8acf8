{% extends "layouts/base.html" %}

{% block title %} 데이터베이스 분석 대시보드 {% endblock %}

{% block stylesheets %}
<style>
    /* Dashboard specific styles */
    .dashboard-card {
        margin-bottom: 1.5rem;
        box-shadow: 0 0 2rem 0 rgba(136, 152, 170, .15);
        border: none;
    }
    .dashboard-card .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.5rem;
        background: #f8f9fe;
        border-bottom: 1px solid #e9ecef;
    }
    .dashboard-card .card-header h5 {
        margin: 0;
        font-weight: 600;
    }
    .dashboard-export-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        transition: all 0.2s;
    }
    .dashboard-stat-card {
        transition: transform 0.2s;
        border: none;
        box-shadow: 0 0 2rem 0 rgba(136, 152, 170, .15);
    }
    .dashboard-stat-card:hover {
        transform: translateY(-5px);
    }
    .dashboard-chart-container {
        position: relative;
        padding: 1rem;
        height: 450px;
    }
    .dashboard-stat-icon {
        width: 3rem;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }
    .dashboard-stat-value {
        font-size: 1.5rem;
        font-weight: 600;
    }
    .dashboard-stat-label {
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }
    .dashboard-plot-container {
        height: 100% !important;
    }
    .dashboard-legend {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 4px;
        padding: 5px;
    }
    .dashboard-hovertext {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .dashboard-bar-chart-container {
        min-height: 500px;
        width: 100%;
        position: relative;
    }
    .dashboard-card-body .plotly-graph-div {
        min-height: 500px;
    }
</style>
{% endblock stylesheets %}

{% block content %}
<div class="header pb-6">
    {% include 'genomom/messages_display.html'%}

    <div class="container-fluid">
        <div class="header-body">
            <div class="row align-items-center py-4">
                <div class="col-lg-6 col-7">
                    <h6 class="h2 d-inline-block mb-0">데이터베이스 분석</h6>
                    <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                        <ol class="breadcrumb breadcrumb-links">
                            <li class="breadcrumb-item"><a href="#"><i class="fas fa-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="#">분석</a></li>
                            <li class="breadcrumb-item active" aria-current="page">데이터베이스 개요</li>
                        </ol>
                    </nav>
                </div>
            </div>

            <!-- Include Date Range Form -->
            {% include 'dashboards/components/date_range_form.html' %}

            <!-- Statistics Cards -->
            <div class="row">
                <div class="col-xl-3 col-md-6">
                    <div class="card dashboard-stat-card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <h5 class="dashboard-stat-label">전체 샘플 수</h5>
                                    <span class="dashboard-stat-value">{{ total_samples }}</span>
                                </div>
                                <div class="col-auto">
                                    <div class="dashboard-stat-icon">
                                        <i class="ni ni-chart-bar-32 text-white"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card dashboard-stat-card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <h5 class="dashboard-stat-label">활성 샘플 수</h5>
                                    <span class="dashboard-stat-value">{{ active_samples }}</span>
                                </div>
                                <div class="col-auto">
                                    <div class="dashboard-stat-icon">
                                        <i class="ni ni-check-bold text-white"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card dashboard-stat-card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <h5 class="dashboard-stat-label">고위험 사례</h5>
                                    <span class="dashboard-stat-value">{{ high_risk_samples }}</span>
                                </div>
                                <div class="col-auto">
                                    <div class="dashboard-stat-icon">
                                        <i class="ni ni-notification-70 text-white"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card dashboard-stat-card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <h5 class="dashboard-stat-label">IVF 치료</h5>
                                    <span class="dashboard-stat-value">{{ ivf_samples }}</span>
                                </div>
                                <div class="col-auto">
                                    <div class="dashboard-stat-icon">
                                        <i class="ni ni-single-02 text-white"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Page content -->
<div class="container-fluid mt--6">
    <!-- Service Type and Fetus Distribution -->
    <div class="row">
        <div class="col-xl-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-vial-circle-check"></i> 서비스 유형 분포</h5>
                    <a href="?export=service_type" class="btn btn-sm dashboard-export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body dashboard-card-body">
                    {% autoescape off %}
                    {{ service_type_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-baby"></i> 태아 수 분포</h5>
                    <a href="?export=fetus" class="btn btn-sm dashboard-export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body dashboard-card-body">
                    {% autoescape off %}
                    {{ fetus_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
    </div>

    <!-- Test Type and Jedan Distribution -->
    <div class="row">
        <div class="col-xl-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-vial-circle-check"></i> 검사 유형 분포</h5>
                    <a href="?export=test_type" class="btn btn-sm dashboard-export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body dashboard-card-body">
                    {% autoescape off %}
                    {{ test_type_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-building"></i> 제단 분포</h5>
                    <a href="?export=jedan" class="btn btn-sm dashboard-export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body dashboard-card-body">
                    {% autoescape off %}
                    {{ jedan_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
    </div>

    <!-- Result Distribution and Regional Distribution -->
    <div class="row">
        <div class="col-xl-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-square-poll-vertical"></i> 결과 분포</h5>
                    <a href="?export=final_result" class="btn btn-sm dashboard-export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body dashboard-card-body">
                    {% autoescape off %}
                    {{ final_result_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-map-marker-alt"></i> 상위 10개 지역 분포 (도)</h5>
                    <a href="?export=do_address" class="btn btn-sm dashboard-export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body dashboard-card-body">
                    {% autoescape off %}
                    {{ do_address_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
    </div>

    <!-- IVF Treatment and Active Status -->
    <div class="row">
        <div class="col-xl-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-square-poll-vertical"></i> IVF 치료 분포</h5>
                    <a href="?export=ivf_treatment" class="btn btn-sm dashboard-export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body dashboard-card-body">
                    {% autoescape off %}
                    {{ ivf_treatment_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-solid fa-square-poll-vertical"></i> 활성 상태 분포</h5>
                    <a href="?export=is_active" class="btn btn-sm dashboard-export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body dashboard-card-body">
                    {% autoescape off %}
                    {{ is_active_div }}
                    {% endautoescape %}
                </div>
            </div>
        </div>
    </div>

    <!-- Bar Charts -->
    <div class="row">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-sharp fa-solid fa-hospital"></i> 상위 12개 제단 지점 분포</h5>
                    <a href="?export=jedan_branch" class="btn btn-sm dashboard-export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body dashboard-card-body">
                    {{ jedan_branch_div | safe }}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-sharp fa-solid fa-hospital"></i> 상위 12개 병원 분포</h5>
                    <a href="?export=hospital" class="btn btn-sm dashboard-export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body dashboard-card-body">
                    {{ hospital_div | safe }}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-sharp fa-solid fa-map"></i> 상위 10개 도시 분포</h5>
                    <a href="?export=si_address" class="btn btn-sm dashboard-export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body dashboard-card-body">
                    {{ si_address_div | safe }}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="h3 mb-0"><i class="fa-sharp fa-solid fa-user-doctor"></i> 상위 12개 의사 분포</h5>
                    <a href="?export=doctor" class="btn btn-sm dashboard-export-btn">
                        <i class="fas fa-download"></i> 내보내기
                    </a>
                </div>
                <div class="card-body dashboard-card-body">
                    {{ doctor_div | safe }}
                </div>
            </div>
        </div>
    </div>

    <div class="page-header-title">
        <div class='p-3 mb-2 bg-success text-white text-center h5 font-weight-bold'>
            <h3>개발: Krishna (R&D Genomecare)</h3>
            <p class="mb-0">문의사항은 AIM 팀에 연락해 주세요</p>
        </div>
    </div>

    {% include 'includes/footer.html' %}
</div>
{% endblock content %}

{% block javascripts %}
<script>
    // Add any custom JavaScript here if needed
</script>
{% endblock javascripts %}