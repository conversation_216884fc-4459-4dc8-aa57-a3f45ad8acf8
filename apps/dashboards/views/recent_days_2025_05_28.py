from django.shortcuts import render

import pandas as pd

from datetime import datetime, date , timedelta


from plotly.offline import plot
import plotly.express as px

from apps.authentication.decorators import admin_required, staff_required
from django.contrib.auth.decorators import login_required

from apps.genomom.models import PatientInfo, SampleInfo




def generate_bar_plot(df, target_col, color_column,  width=800, height = 500, total= True ,legend = True ):
    """
    Generate a bar plot based on the specified target and color columns.

    Args:
        df (pd.DataFrame): The input DataFrame containing the data.
        target_col (str): The target column for grouping and plotting.
        color_column (str): The color column for distinguishing data in the plot.
        config (dict): Configuration settings for the plotly plot.

    Returns:
        str: The HTML representation of the generated plot.
    """
    config = dict({'scrollZoom': False, 'displayModeBar': False})
   

    grouped_data = df.groupby([target_col, color_column]).agg({
        'Counts': 'sum',
    }).reset_index()

    grouped_data.sort_values([ target_col,], inplace=True)
    

    fig = px.bar(grouped_data, x=target_col, y='Counts', color=color_column, text='Counts',
                 hover_data=[target_col])

    all_samples_counts = grouped_data.groupby(target_col).sum("Counts").reset_index()

    all_samples_counts.sort_values([ target_col ], inplace=True)


    fig.update_traces(texttemplate='%{text}', textposition="outside", textfont_size=8, textangle=0,
                      marker_line_color='rgb(8,48,107)', marker_line_width=1)

    if total:
        fig.add_scatter(x=all_samples_counts[target_col],
                        y=all_samples_counts["Counts"],
                        name="Total",
                        text=all_samples_counts["Counts"],
                        mode="markers",
                        marker=dict(size=15, color="red"),)
    


    fig.update_layout(
        width=width, height=height,

        legend=dict(orientation="h", yanchor="bottom",
                    y=1.02, xanchor="right", x=1),
        showlegend=legend,
        xaxis_title=target_col ,
        yaxis_title='NIPT 건수'
    )    
    
        
    

    return plot(fig, output_type="div", config=config, include_plotlyjs=True)












# Create your views here.
@login_required(login_url="/")
@staff_required(allowed_user_groups=["1", "7","6" ])
def recent_days(request):
    """
    Recent days progress will display here.
    In case of Genomom 7 days will be suitalble.
    we will discuss later for this 
    
        ('1','입고_페이지'), #! Sample Entry 
        ('2', '실험_페이지'),  # ! Lab Testing members
        ('3', '분석_페이지'),  # ! Pipe Line Run
        ("4", "판독-1_페이지"),  # ! AIM Team 1-차 판독
        ("5", "판독-2_페이지"),  # ! Final Confirm 2
        ("6", "출고_페이지"),  # ! 출고 팀
        ("7", "영업_페이지"),  # ! Marketing
        ("8", "IT_페이지"),  # ! IT if required
        ("9", "CEO_페이지"),  # ! CEO
        ("0", "운영_페이지"),  # ! Option 2
        ("X", "DontAllow"),
        ("H", "Jedan_페이지"),

    
    """
    #! Later change extraction_date to patient_created_at 
    recent_days = 10  
    
    all_samples = SampleInfo.objects.filter(extraction_date__range=[date.today(
    )-timedelta(days= recent_days), date.today() + timedelta(days=1)], is_active=True)

    context = {}
    
    if all_samples:

        samples = all_samples.values("patient__jedan__jedan_name",
                                     "patient__jedan_branch__jedan_branch_name",
                                     "patient__hospital__hospital_name",
                                     "patient__doctor__full_name",
                                     "patient__patient_age",
                                     "patient__fetus_number",
                                     "service_type__service_name",
                                     "extraction_date", 
                                     "entry_date",
                                     "result_sent_time",
                                     "report_publish",
                                     "entry_date", 
                                     "received_by",
                                     "test_type",
                                     "samples_trisomy__final_result2" )  # "final_result", ,  "sample_result"
        
        df = pd.DataFrame(samples)
        
        df.rename(columns={'patient__jedan__jedan_name': "jedan",
                        'patient__jedan_branch__jedan_branch_name':"jedan_branch",
                        'patient__hospital__hospital_name':"hospital",
                        "patient__doctor__full_name":"doctor",
                        'patient__created_at':"created_at",
                        'patient__patient_age':'age',
                        'patient__test_type':"test_type",
                        "patient__fetus_number":"fetus",
                        "patient__patient_ga":"ga" ,
                        "service_type__service_name":"service_name" ,
                        "samples_trisomy__final_result2": "final_result",
                        #"samples_trisomy__sample_result":"sample_result"
                        
                    
                    }, inplace=True)
        
        df["Counts"] = 1
        
        df["final_result"] = df.final_result.replace(
            {"0": 'Low', '1': 'High', '2': 'Border', '6': 'Re-Draw', "7": "검사불능"})

        df["report_publish"] = df.report_publish.replace(
                {True: "완료", False: "미출고"})
    
        df["hospital"] = df["jedan"] + '-' + df["hospital"]
        
        df["jedan_branch"] = df["jedan"] + '-' + df["jedan_branch"]
        
        #! *********** Currently entry_date is current date single date so we will make same like extraction date
        df["extraction_date"] = pd.to_datetime(df.extraction_date)
        df['entry_date'] = df.extraction_date.copy()   #! Do 
        df['entry_date'] = pd.to_datetime(df.entry_date.dt.date)
        
        
        #!  Visualization Start from here 
        target_col = "jedan"
        color_column = "service_name"
        jedan_div = generate_bar_plot(df, target_col, color_column)
        
        
        #!  Jedan Branch , Hospital & Doctors may have long rows which need in a single plot

        target_col = "service_name"
        color_column = "jedan"
        service_div = generate_bar_plot(df, target_col, color_column)
        
        
        
        
        
        #!  Test Type which is First Time Test , or Second Time Test                  
        target_col = "test_type"
        color_column = "jedan"

        test_type_div = generate_bar_plot(df, target_col, color_column)
        
        
        #!  Test Type which is First Time Test , or Second Time Test
        target_col = "final_result"  # sample_result" # "final_result" #"sample_result"
        color_column = 'jedan'

        sample_result_div = generate_bar_plot(df, target_col, color_column)

        #!  Test Type which is First Time Test , or Second Time Test                  
        # Assuming df is your DataFrame and entry_date is in date format
        target_col = "entry_date"
        color_column = "jedan"
                
        entry_date_div = generate_bar_plot(df, target_col, color_column)
        
            
        #!  Test Type which is First Time Test , or Second Time Test                  
        target_col = "result_sent_time"
        color_column = 'jedan'

        final_result_div = generate_bar_plot(
            df, target_col, color_column, )


        #! One Row Plot Jedan Branch & Hospital
        target_col = "jedan_branch"
        color_column = 'hospital'
        jedan_branch_div = generate_bar_plot(
            df, target_col, color_column, width=1900, height = 800, legend=False )
        #fig.show()
        
        
        
        
        #! HOSPITAL 
        target_col = "hospital"
        color_column ='doctor'
        
        hospital_div = generate_bar_plot(
            df, target_col, color_column, width=1900, height=800, legend=False )



        #! Non graph related functions
        total_data = df.shape[0]
        completed = df[df.report_publish == True ].shape[0]
        not_first_test = df[df.test_type != "First" ].shape[0]
        not_low_risk = df[df.final_result != "Low"].shape[0]
        
        # Assuming 'today' is a date object representing today's date
        today_entry = SampleInfo.objects.filter(created_at__date=date.today())
        today_entry = len(today_entry)
        
        today_confirm = SampleInfo.objects.filter(samples_trisomy__created_at__date=date.today())
        
        #! 오늘 판독중에서 어떻게 판독 되는지
        if today_confirm:
            today_confirm_samples = today_confirm.values("samples_trisomy__final_result2", "samples_trisomy__final_result1")

            df = pd.DataFrame(today_confirm_samples)

            df.rename(columns={
                "samples_trisomy__final_result1": "판독-1",
                "samples_trisomy__final_result2": "판독-2",
            }, inplace=True)
            
            value_mapping = {
                            "0": 'Low', '1': 'High', '2': 'Border', '3': "Retest_P",
                            "4": "Retest", "5": "Retest", '6': 'Re-Draw', "7": "검사불능"
                        }
            

            df.replace(value_mapping, inplace=True)

            # Melt the DataFrame to combine confirm1 and confirm2 into a single column
            melted_df = pd.melt(df, value_vars=["판독-1", "판독-2"], value_name='Value', var_name='판독')
            
            # Create a new DataFrame with 'confirm1' and 'confirm2' as rows and unique 'Value' as columns
            result = melted_df.groupby(['판독', 'Value']).size().unstack(fill_value=0).reset_index()

            result.columns.name = None
            
            result_confirm_table = result.to_html(
                index=False, classes='table table-bordered', justify='center')
            
            result_confirm_table = result_confirm_table.replace(
                "<th", "<th class='text-white thead-dark bg-primary h2 ' style='padding:1px;font-size:20px;' ")
            
            result_confirm_table = result_confirm_table.replace(
                "<td>", "<td class='text-dark text-center bg-success '  style='padding:10px;margin:12px;font-size:16px;'  > ")
            
            
        else:
            result_confirm_table = "</h3> No datas </h3> "


        today_edited_samples = SampleInfo.objects.filter(last_edited=date.today())
        if today_edited_samples:
            today_edited_samples = today_edited_samples.values("sample_process"   ) # 
            df = pd.DataFrame(today_edited_samples)
            df["sample_process"] = df["sample_process"].replace({  
                    '0': '검체 입고',  # search and add hospital chart
                    '1': '입고 확인',  # sample info updated from entry page
                    '2': 'DNA0',  # Qc analysis, Retest_p
                    '3': 'DNA1',  # Qc update , Retest_L
                    '4': 'DNA2',  # Qc       , Retest_S
                    '5': 'Pipeline',  # Qc sample send to confirmation
                    '6': '판독1',  # Qc sample send to design result 파이프라인 실행
                    '7': '판독2',  # Qc change after pdf result file
                    '8': '미출고',
                    '9':'출고' })
            
            
        
            today_edited_samples = df.value_counts().to_dict()
            
            # Convert the multi-index to a single index by extracting the first element of each tuple
            today_edited_samples = {str(key[0]): value for key, value in today_edited_samples.items()}
        else:
            today_edited_samples = {"No Edited":0}
            
        
        

        
        today_release = SampleInfo.objects.filter(result_sent_time__date=date.today())
        today_release = len(today_release)
        
        release_finished = SampleInfo.objects.filter(
            result_sent_time__date=date.today() , report_publish = True )
        
        release_finished = len(release_finished)
        

        
        
        context = {

            "jedan_div": jedan_div,
            "service_div": service_div, 
            "test_type_div": test_type_div,
            
            #"jedan_service_tunnel_div":jedan_service_tunnel_div ,
            
            "entry_date_div": entry_date_div,
            "final_result_div": final_result_div,
            "sample_result_div": sample_result_div,
            "jedan_branch_div": jedan_branch_div,
            "hospital_div": hospital_div,
            
            #! Non graph variables 
            "today_entry": today_entry,
            "today_confirm":len(today_confirm),
            "today_release": today_release,
            "release_finished": release_finished,
            "today_edited_samples": today_edited_samples,
            "result_confirm_table": result_confirm_table,
            
            
            "recent_days":recent_days,
            "total_data":total_data,
            "completed":completed,
            "progress":round((completed / total_data) * 100 ,4),
            "not_low_risk":not_low_risk,
            "not_low_risk_progress":round((not_low_risk/total_data) * 100 ,4) ,
            "not_first_test":not_first_test,
            "not_first_test_progress": round(( not_first_test / total_data ) * 100 ,4),
            "active_menu": "dashboard",
        }
        
        template = "dashboards/recent_days.html"
        return render(request, template_name=template, context=context)
    else:
        template = "dashboards/recent_days.html"
        return render(request, template_name=template)
