from django.shortcuts import render
import pandas as pd
from datetime import datetime, date, timedelta
from plotly.offline import plot
import plotly.express as px
from apps.authentication.decorators import admin_required, staff_required
from django.contrib.auth.decorators import login_required
from apps.genomom.models import PatientInfo, SampleInfo
from django.http import HttpResponse
import io

def export_to_excel(df, filename):
    """Helper function to export DataFrame to Excel"""
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='Sheet1', index=False)
    output.seek(0)
    response = HttpResponse(
        output.read(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename={filename}.xlsx'
    return response

def generate_bar_plot(df, target_col, color_column, width=800, height=500, total=True, legend=True, title=None):
    """Generate a bar plot with improved styling and mobile responsiveness"""
    config = dict({
        'scrollZoom': False,
        'displayModeBar': True,
        'displaylogo': False,
        'modeBarButtonsToAdd': ['downloadImage'],
        'responsive': True,
        'displayModeBar': 'hover',
        'modeBarButtonsToRemove': [],
        'toImageButtonOptions': {
            'format': 'png',
            'filename': 'custom_image',
            'height': 500,
            'width': 700,
            'scale': 2
        }
    })

    # Check if DataFrame is empty
    if df.empty:
        fig = px.bar(x=[0], y=[0])
        fig.update_layout(
            width=width,
            height=height,
            title=dict(
                text="데이터가 없습니다",
                x=0.5,
                y=0.5,
                font=dict(size=20)
            ),
            showlegend=False,
            xaxis=dict(showticklabels=False),
            yaxis=dict(showticklabels=False),
            paper_bgcolor='rgba(0,0,0,0)',
            plot_bgcolor='rgba(0,0,0,0)'
        )
        return plot(fig, output_type="div", config=config, include_plotlyjs=True)

    # Sort the data by Counts in descending order
    grouped_data = df.groupby([target_col, color_column]).agg({
        'Counts': 'sum',
    }).reset_index()
    
    # Sort by total counts for each target_col
    total_counts = grouped_data.groupby(target_col)['Counts'].sum().sort_values(ascending=False)
    grouped_data[target_col] = pd.Categorical(
        grouped_data[target_col], 
        categories=total_counts.index, 
        ordered=True
    )
    grouped_data = grouped_data.sort_values([target_col, 'Counts'], ascending=[True, False])

    fig = px.bar(grouped_data, x=target_col, y='Counts', color=color_column, text='Counts',
                 hover_data=[target_col])

    # Update bar traces
    fig.update_traces(
        texttemplate='%{text}',
        textposition="outside",
        textfont_size=10,
        marker_line_color='rgb(8,48,107)',
        marker_line_width=1
    )

    if total:
        all_samples_counts = grouped_data.groupby(target_col).sum("Counts").reset_index()
        all_samples_counts = all_samples_counts.sort_values('Counts', ascending=False)
        
        # Add scatter trace with its own textposition
        fig.add_scatter(
            x=all_samples_counts[target_col],
            y=all_samples_counts["Counts"],
            name="Total",
            text=all_samples_counts["Counts"],
            mode="markers+text",
            textposition="top center",
            marker=dict(size=15, color="red")
        )

    fig.update_layout(
        width=width,
        height=height,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        showlegend=legend,
        xaxis_title=target_col,
        yaxis_title='NIPT 건수',
        margin=dict(l=20, r=20, t=40, b=20),
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        xaxis=dict(
            tickangle=-45,
            tickfont=dict(size=10)
        )
    )

    return plot(fig, output_type="div", config=config, include_plotlyjs=True)

@login_required(login_url="/")
@staff_required(allowed_user_groups=["1", "7", "6"])
def recent_days(request):
    """View for displaying recent days statistics with date filtering"""
    # Default to today and 7 days
    today = date.today()
    end_date = request.GET.get('end_date')
    days = int(request.GET.get('days', 7))  # Default to 7 days
    export_type = request.GET.get('export')

    # Handle date parsing first
    try:
        if end_date:
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            else:
                end_date = today
        else:
            end_date = today
    except ValueError:
        end_date = today

    start_date = end_date - timedelta(days=days-1)

    # Handle export
    if export_type:
        all_samples = SampleInfo.objects.filter(
            entry_date__range=[start_date, end_date + timedelta(days=1)],
            is_active=True
        )
        if all_samples:
            samples = all_samples.values(
                "patient__jedan__jedan_name",
                "patient__jedan_branch__jedan_branch_name",
                "patient__hospital__hospital_name",
                "patient__doctor__full_name",
                "patient__patient_age",
                "patient__fetus_number",
                "service_type__service_name",
                "entry_date",
                "result_sent_time",
                "report_publish",
                "received_by",
                "test_type",
                "samples_trisomy__final_result2"
            )
            df = pd.DataFrame(samples)
            if not df.empty:
                df.rename(columns={
                    'patient__jedan__jedan_name': "jedan",
                    'patient__jedan_branch__jedan_branch_name': "jedan_branch",
                    'patient__hospital__hospital_name': "hospital",
                    "patient__doctor__full_name": "doctor",
                    'patient__patient_age': 'age',
                    'patient__test_type': "test_type",
                    "patient__fetus_number": "fetus",
                    "service_type__service_name": "service_name",
                    "samples_trisomy__final_result2": "final_result"
                }, inplace=True)
                
                if export_type == 'all':
                    return export_to_excel(df, f'nipt_data_{start_date}_{end_date}')
                elif export_type in ['jedan', 'service', 'test_type', 'result', 'entry', 'release']:
                    filtered_df = df[df[export_type].notna()]
                    return export_to_excel(filtered_df, f'nipt_{export_type}_{start_date}_{end_date}')

    # Get samples for the date range using entry_date
    all_samples = SampleInfo.objects.filter(
        entry_date__range=[start_date, end_date + timedelta(days=1)],
        is_active=True
    )

    # Get today's data for stats using entry_date
    today_samples = SampleInfo.objects.filter(
        entry_date=today,
        is_active=True
    )

    # Update 출고 and 출고 완료 queries
    today_release = SampleInfo.objects.filter(
        is_active=True,
        report_publish=True,
        result_sent_time__date=today
    ).count()

    today_release_finished = SampleInfo.objects.filter(
        is_active=True,
        report_publish=True,
        result_sent_time__date=today,
        sample_process=9
    ).count()

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'days': days,
        'active_menu': "dashboard",
        'today_entry': today_samples.count(),
        'today_confirm': today_samples.filter(samples_trisomy__created_at__date=today).count(),
        'today_release': today_release,
        'release_finished': today_release_finished,
    }

    if all_samples:
        samples = all_samples.values(
            "patient__jedan__jedan_name",
            "patient__jedan_branch__jedan_branch_name",
            "patient__hospital__hospital_name",
            "patient__doctor__full_name",
            "patient__patient_age",
            "patient__fetus_number",
            "service_type__service_name",
            "entry_date",
            "result_sent_time",
            "report_publish",
            "received_by",
            "test_type",
            "samples_trisomy__final_result2"
        )

        df = pd.DataFrame(samples)
        
        if not df.empty:
            df.rename(columns={
                'patient__jedan__jedan_name': "jedan",
                'patient__jedan_branch__jedan_branch_name': "jedan_branch",
                'patient__hospital__hospital_name': "hospital",
                "patient__doctor__full_name": "doctor",
                'patient__patient_age': 'age',
                'patient__test_type': "test_type",
                "patient__fetus_number": "fetus",
                "service_type__service_name": "service_name",
                "samples_trisomy__final_result2": "final_result"
            }, inplace=True)
            
            df["Counts"] = 1
            
            df["final_result"] = df.final_result.replace({
                "0": 'Low', '1': 'High', '2': 'Border', '6': 'Re-Draw', "7": "검사불능"
            })

            df["report_publish"] = df.report_publish.replace({
                True: "완료", False: "미출고"
            })
        
            df["hospital"] = df["jedan"] + '-' + df["hospital"]
            df["jedan_branch"] = df["jedan"] + '-' + df["jedan_branch"]
            
            df["entry_date"] = pd.to_datetime(df.entry_date)

            # Generate plots
            plots = {
                'jedan_div': generate_bar_plot(df, "jedan", "service_name", title="재단별 분포"),
                'service_div': generate_bar_plot(df, "service_name", "jedan", title="서비스 종류"),
                'test_type_div': generate_bar_plot(df, "test_type", "jedan", title="재실험 여부"),
                'sample_result_div': generate_bar_plot(df, "final_result", "jedan", title="결과 현황"),
                'entry_date_div': generate_bar_plot(df, "entry_date", "jedan", title="입고 현황"),
                'final_result_div': generate_bar_plot(df, "result_sent_time", "jedan", title="출고 현황"),
                'jedan_branch_div': generate_bar_plot(df, "jedan_branch", "hospital", width=1900, height=800, legend=False),
                'hospital_div': generate_bar_plot(df, "hospital", "doctor", width=1900, height=800, legend=False)
            }
            context.update(plots)

    return render(request, "dashboards/recent_days.html", context)
