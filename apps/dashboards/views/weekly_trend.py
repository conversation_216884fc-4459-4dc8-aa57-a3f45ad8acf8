import os
import sys
import django
import random
from datetime import date, timedelta
from django.shortcuts import render
import pandas as pd

import plotly.express as px
from plotly.offline import plot

# from apps.authentication.models import User, Country, DoAddress, SiAddress
# PatientInfo, #, FF_info, Read_Counts_GC, Read_Ratio, Z_score, ServiceName
from apps.genomom.models import SampleInfo

from apps.authentication.decorators import admin_required, staff_required
from django.contrib.auth.decorators import login_required
from django.shortcuts import render
from django.http import HttpResponse
import io
import numpy as np

width, height = 1900, 800

def export_to_excel(df, filename):
    """Helper function to export DataFrame to Excel"""
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='Sheet1', index=False)
    output.seek(0)
    response = HttpResponse(
        output.read(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    return response

# Create your views here.
@login_required(login_url="/")
@staff_required(allowed_user_groups=["7"])  # ! Show to All Staff / Employee
def weekly_trend(request):
    """
    Weekly trend analysis for NIPT samples
    """
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    # Handle export requests
    if request.GET.get('export'):
        export_type = request.GET.get('export')
        if export_type == 'jedan_trend':
            return export_to_excel(jedan_trend_df, 'weekly_jedan_trend.xlsx')
        elif export_type == 'service_trend':
            return export_to_excel(service_trend_df, 'weekly_service_trend.xlsx')
        elif export_type == 'result_trend':
            return export_to_excel(result_trend_df, 'weekly_result_trend.xlsx')

    config = dict({'scrollZoom': False, 'displayModeBar': False})
    
    all_samples = SampleInfo.objects.filter(is_active=True, report_publish=True)
    
    # Apply date filter if dates are provided
    if start_date and end_date:
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            all_samples = all_samples.filter(entry_date__range=[start_date, end_date])
        except ValueError:
            pass

    context = {}
    if all_samples:
        samples = all_samples.values(
            "patient__jedan__jedan_name", 
            "patient__jedan_branch__jedan_branch_name", 
            "patient__hospital__hospital_name", 
            "patient__doctor__full_name",
            "patient__patient_age", 
            "patient__fetus_number", 
            "service_type__service_name", 
            "extraction_date", 
            "entry_date", 
            "result_sent_time",  
            "report_publish",
            "entry_date", 
            "received_by", 
            "test_type", 
            "samples_trisomy__final_result2"
        )
        
        df = pd.DataFrame(samples)
        
        df.rename(columns={
            'patient__jedan__jedan_name': "jedan",
            'patient__jedan_branch__jedan_branch_name': "jedan_branch",
            'patient__hospital__hospital_name': "hospital",
            "patient__doctor__full_name": "doctor",
            'patient__created_at': "created_at",
            'patient__patient_age': 'age',
            'patient__test_type': "test_type",
            "patient__fetus_number": "fetus",
            "patient__patient_ga": "ga",
            "service_type__service_name": "service_type",
            "samples_trisomy__final_result2": "final_result",
        }, inplace=True)
        
        df["Counts"] = 1
        df["final_result"] = df.final_result.replace({
            "0": 'Low', 
            '1': 'High', 
            '2': 'Border', 
            '6': 'Re-Draw', 
            "7": "검사불능"
        })
        
        df.final_result.fillna('미판독', inplace=True)
        df["entry_date"] = pd.to_datetime(df.entry_date, format='%Y%m%d', errors='ignore')
        df['result_sent_time'] = pd.to_datetime(df.result_sent_time.dt.date)
        
        # Add week number and year
        df['week'] = df['entry_date'].dt.isocalendar().week
        df['year'] = df['entry_date'].dt.isocalendar().year
        df['week_year'] = df['year'].astype(str) + '-W' + df['week'].astype(str).str.zfill(2)
        
        columns_to_load = ['jedan', 'jedan_branch', 'hospital', 'fetus', 'service_type', 
                         'entry_date', 'week_year', 'report_publish', 'result_sent_time', 
                         'final_result', 'Counts']
        columns_for_group = ['jedan', 'jedan_branch', 'hospital', 'fetus', 'service_type', 
                           'entry_date', 'week_year', 'report_publish', 'result_sent_time', 
                           'final_result']
        
        first_group = df.loc[:, columns_to_load].groupby(columns_for_group).sum().reset_index()
        all_samples_counts = first_group.loc[:, ["week_year", 'Counts']].groupby(["week_year"]).sum().reset_index()
        all_samples_counts.sort_values(by="week_year", inplace=True)
        
        # Jedan Trend with Trend Line
        sample_type_group = first_group.loc[:, ["week_year", 'jedan', "Counts"]].groupby(
            ["week_year", 'jedan']).sum().reset_index()
        
        color_scale = px.colors.qualitative.Plotly
        
        fig = px.line(sample_type_group, x="week_year", y="Counts",
                     color="jedan",
                     labels={'sample_status': '검체 주기',
                            "Counts": "NIPT 건수", 
                            "week_year": '주차'},
                     color_discrete_sequence=color_scale)
        
        # Improved trend line calculation and visualization
        x = range(len(all_samples_counts["week_year"]))
        y = all_samples_counts["Counts"]
        z = np.polyfit(x, y, 1)
        p = np.poly1d(z)
        
        # Calculate R-squared value
        y_pred = p(x)
        r_squared = 1 - (np.sum((y - y_pred) ** 2) / np.sum((y - np.mean(y)) ** 2))
        
        # Add trend line with improved styling
        fig.add_scatter(
            x=all_samples_counts["week_year"],
            y=p(x),
            mode='lines',
            name=f'Trend Line (R² = {r_squared:.3f})',
            line=dict(
                color='red',
                width=3,
                dash='dash'
            ),
            hoverinfo='name'
        )
        
        # Add trend line equation annotation
        slope = z[0]
        intercept = z[1]
        equation = f'y = {slope:.2f}x + {intercept:.2f}'
        
        fig.add_annotation(
            x=all_samples_counts["week_year"].iloc[-1],
            y=p(len(all_samples_counts["week_year"])-1),
            text=equation,
            showarrow=True,
            arrowhead=1,
            ax=50,
            ay=-50,
            font=dict(size=12, color='red')
        )
        
        # Total Counts line
        fig.add_scatter(x=all_samples_counts["week_year"], 
                       y=all_samples_counts["Counts"],
                       mode='lines+markers', 
                       name="Total", 
                       line=dict(color='green', width=4))
        
        fig.update_layout(
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
            font=dict(family="Courier", size=14, color="black"),
            width=width, height=height,
            xaxis_title='주차',
            yaxis_title='NIPT 건수',
            hovermode='x unified'
        )
        
        jedan_div = plot(fig, config=config, output_type="div")
        jedan_trend_df = sample_type_group  # For export
        
        # Service Type Trend
        sample_type_group = first_group.loc[:, ["week_year", 'service_type', "Counts"]].groupby(
            ["week_year", 'service_type']).sum().reset_index()
        
        fig = px.line(sample_type_group, x="week_year", y="Counts",
                     color="service_type", 
                     symbol="service_type",
                     labels={'sample_status': '검체 주기',
                            "Counts": "NIPT 건수",
                            "week_year": '주차'})
        
        fig.update_layout(
            width=width, height=height,
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
            xaxis_title='주차',
            yaxis_title='NIPT 건수',
            hovermode='x unified'
        )
        
        service_div = plot(fig, config=config, output_type="div")
        service_trend_df = sample_type_group  # For export
        
        # Result Trend
        sample_type_group = first_group.loc[:, ["week_year", 'final_result', "Counts"]].groupby(
            ["week_year", 'final_result']).sum().reset_index()
        
        fig = px.line(sample_type_group, x="week_year", y="Counts",
                     color="final_result", 
                     symbol="final_result",
                     labels={'sample_status': '검체 주기',
                            "Counts": "NIPT 건수",
                            "week_year": '주차'})
        
        fig.update_layout(
            width=width, height=height,
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
            xaxis_title='주차',
            yaxis_title='NIPT 건수',
            hovermode='x unified'
        )
        
        final_result_div = plot(fig, config=config, output_type="div")
        result_trend_df = sample_type_group  # For export
        
        context = {
            "jedan_div": jedan_div,
            'service_div': service_div,
            "final_result_div": final_result_div,
            "active_menu": "dashboard",
        }
        
        template = "dashboards/weekly_trend.html"
        return render(request, template_name=template, context=context)
    else:
        template = "dashboards/weekly_trend.html"
        return render(request, template_name=template)
