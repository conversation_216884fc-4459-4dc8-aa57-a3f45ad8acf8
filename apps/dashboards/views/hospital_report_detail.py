from django.shortcuts import render
import pandas as pd
import numpy as np
from datetime import date, timedelta, datetime
from apps.authentication.decorators import admin_required, staff_required
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
from plotly.offline import plot
import plotly.express as px
import plotly.graph_objects as go
from apps.genomom.models import SampleInfo
from apps.hospital.models import HospitalInfo
import io

from .monthly_trend import generate_bar_plot
from .all_db_info import generate_pie_chart

width, height = 1900, 800

def export_to_excel(df, filename):
    """Helper function to export DataFrame to Excel"""
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='Sheet1', index=True)
    output.seek(0)
    response = HttpResponse(
        output.read(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    return response

@login_required(login_url="/")
@staff_required(allowed_user_groups=["7",])
def hospital_report_detail(request, pk):
    """
    Hospital detailed report with statistics and visualizations
    """
    config = dict({'scrollZoom': False, 'displayModeBar': False})
    
    # Get hospital info
    hospital = HospitalInfo.objects.get(id=pk)
    if not hospital:
        return HttpResponse("Hospital not found")
    
    # Get date range from request
    end_date = date.today()
    start_date = end_date - timedelta(days=365)  # Default to last year
    
    if request.GET.get('start_date'):
        try:
            start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
        except:
            pass
    
    if request.GET.get('end_date'):
        try:
            end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()
        except:
            pass
    
    # Handle export requests
    if request.GET.get('export'):
        export_type = request.GET.get('export')
        if export_type == 'daily_stats':
            return export_to_excel(daily_stats_df, f'{hospital.hospital_name}_daily_stats.xlsx')
        elif export_type == 'monthly_stats':
            return export_to_excel(monthly_stats_df, f'{hospital.hospital_name}_monthly_stats.xlsx')
        elif export_type == 'doctor_stats':
            return export_to_excel(doctor_stats_df, f'{hospital.hospital_name}_doctor_stats.xlsx')
    
    # Get all samples for this hospital
    all_samples = SampleInfo.objects.filter(
        patient__hospital=hospital, 
        is_active=True,
        entry_date__gte=start_date,
        entry_date__lte=end_date
    )
    
    if not all_samples.exists():
        return HttpResponse("This hospital does not have any data yet")
    
    # Get sample data
    values = [
        "patient__jedan__jedan_name",
        "patient__jedan_branch__jedan_branch_name",
        "entry_date",
        "patient__doctor__full_name",
        "patient__patient_age",
        "patient__fetus_number",
        "service_type__service_name",
        "patient_ga",
        "patient__ivf_treatment",
        "test_type",
        "samples_trisomy__final_result2",
        "result_sent_time"
    ]
    
    samples = all_samples.values(*values)
    df = pd.DataFrame(samples)
    
    # Rename columns
    df.rename(columns={
        'patient__jedan__jedan_name': "jedan",
        'patient__jedan_branch__jedan_branch_name': "jedan_branch",
        "patient__doctor__full_name": "doctor",
        'patient__patient_age': 'age',
        'patient__test_type': "test_type",
        "patient__fetus_number": "fetus",
        "patient_ga": "ga",
        "patient__ivf_treatment": "ivf",
        "service_type__service_name": "service_type",
        "samples_trisomy__final_result2": "final_result"
    }, inplace=True)
    
    df["Counts"] = 1
    
    # Process dates
    df["entry_date"] = pd.to_datetime(df.entry_date, format='%Y%m%d', errors='ignore')
    df['result_sent_time'] = pd.to_datetime(df.result_sent_time.dt.date)
    
    # Add time-based columns
    df['year'] = df['entry_date'].dt.year
    df['month'] = df['entry_date'].dt.month
    df["yy_mm"] = df['year'].astype(str) + '년_' + df['month'].astype(str).str.zfill(2) + '_월'
    
    # Process categorical data
    df.final_result.fillna('미판독', inplace=True)
    df["final_result"] = df.final_result.replace({
        "0": 'Low',
        '1': 'High',
        '2': 'Border',
        '6': 'Re-Draw',
        "7": "검사불능"
    })
    
    df["ivf"] = df.ivf.replace({
        "1": "유",
        "0": "무",
        '-': '-'
    })
    
    # Calculate statistics
    total_samples = len(df)
    total_doctors = df['doctor'].nunique()
    avg_samples_per_day = total_samples / df['entry_date'].nunique()
    avg_samples_per_month = total_samples / df['yy_mm'].nunique()
    
    # Calculate processing time statistics
    df['processing_time'] = (df['result_sent_time'] - df['entry_date']).dt.days
    avg_processing_time = df['processing_time'].mean()
    
    # Calculate result statistics
    result_stats = df['final_result'].value_counts()
    high_risk_rate = (result_stats.get('High', 0) / total_samples * 100) if total_samples > 0 else 0
    
    # Create daily statistics DataFrame
    daily_stats_df = df.groupby('entry_date').agg({
        'Counts': 'sum',
        'doctor': 'nunique',
        'service_type': 'nunique',
        'final_result': lambda x: (x == 'High').sum()
    }).reset_index()
    daily_stats_df.columns = ['날짜', '총 검체 수', '의사 수', '서비스 유형 수', '고위험 검체 수']
    
    # Create monthly statistics DataFrame
    monthly_stats_df = df.groupby('yy_mm').agg({
        'Counts': 'sum',
        'doctor': 'nunique',
        'service_type': 'nunique',
        'final_result': lambda x: (x == 'High').sum()
    }).reset_index()
    monthly_stats_df.columns = ['년월', '총 검체 수', '의사 수', '서비스 유형 수', '고위험 검체 수']
    
    # Create doctor statistics DataFrame
    doctor_stats_df = df.groupby('doctor').agg({
        'Counts': 'sum',
        'service_type': 'nunique',
        'final_result': lambda x: (x == 'High').sum(),
        'ivf': lambda x: (x == '유').sum()
    }).reset_index()
    doctor_stats_df.columns = ['의사', '총 검체 수', '서비스 유형 수', '고위험 검체 수', 'IVF 검체 수']
    
    # Generate visualizations
    custom_colors = ["#2ab7ca", "#2a4d69", "#851e3e", "#0392cf", "#fed766", "#011f4b", "#03396c", "#005b96"]
    
    # Pie charts with error handling
    try:
        # Fetus pie chart
        fetus_counts = df['fetus'].value_counts()
        if not fetus_counts.empty:
            fig = px.pie(values=fetus_counts.values, names=fetus_counts.index, 
                        hole=0.4, color_discrete_sequence=custom_colors)
            fig.update_traces(textposition='outside', textinfo='percent+label+value')
            fig.update_layout(
                height=600,
                showlegend=True,
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                margin=dict(t=0, b=0, l=0, r=0)
            )
            fetus_div = plot(fig, output_type="div")
            fetus_df = fetus_counts.reset_index()
            fetus_df.columns = ['태아수', '검체 수']
        else:
            fetus_div = ""
            fetus_df = pd.DataFrame()

        # Test type pie chart
        test_counts = df['test_type'].value_counts()
        if not test_counts.empty:
            fig = px.pie(values=test_counts.values, names=test_counts.index, 
                        hole=0.4, color_discrete_sequence=custom_colors)
            fig.update_traces(textposition='outside', textinfo='percent+label+value')
            fig.update_layout(
                height=600,
                showlegend=True,
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                margin=dict(t=0, b=0, l=0, r=0)
            )
            test_div = plot(fig, output_type="div")
            test_df = test_counts.reset_index()
            test_df.columns = ['검사 방법', '검체 수']
        else:
            test_div = ""
            test_df = pd.DataFrame()

        # Service type pie chart
        service_counts = df['service_type'].value_counts()
        if not service_counts.empty:
            fig = px.pie(values=service_counts.values, names=service_counts.index, 
                        hole=0.4, color_discrete_sequence=custom_colors)
            fig.update_traces(textposition='outside', textinfo='percent+label+value')
            fig.update_layout(
                height=600,
                showlegend=True,
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                margin=dict(t=0, b=0, l=0, r=0)
            )
            service_type_div = plot(fig, output_type="div")
            service_df = service_counts.reset_index()
            service_df.columns = ['검사 항목', '검체 수']
        else:
            service_type_div = ""
            service_df = pd.DataFrame()

        # Final result pie chart
        result_counts = df['final_result'].value_counts()
        if not result_counts.empty:
            fig = px.pie(values=result_counts.values, names=result_counts.index, 
                        hole=0.4, color_discrete_sequence=custom_colors)
            fig.update_traces(textposition='outside', textinfo='percent+label+value')
            fig.update_layout(
                height=600,
                showlegend=True,
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                margin=dict(t=0, b=0, l=0, r=0)
            )
            final_result_div = plot(fig, output_type="div")
            result_df = result_counts.reset_index()
            result_df.columns = ['결과', '검체 수']
        else:
            final_result_div = ""
            result_df = pd.DataFrame()

        # Doctor pie chart
        doctor_counts = df['doctor'].value_counts()
        if not doctor_counts.empty:
            fig = px.pie(values=doctor_counts.values, names=doctor_counts.index, 
                        hole=0.4, color_discrete_sequence=custom_colors)
            fig.update_traces(textposition='outside', textinfo='percent+label+value')
            fig.update_layout(
                height=600,
                showlegend=True,
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                margin=dict(t=0, b=0, l=0, r=0)
            )
            doctor_div = plot(fig, output_type="div")
            doctor_df = doctor_counts.reset_index()
            doctor_df.columns = ['의사', '검체 수']
        else:
            doctor_div = ""
            doctor_df = pd.DataFrame()

        # IVF treatment pie chart
        ivf_counts = df['ivf'].value_counts()
        if not ivf_counts.empty:
            fig = px.pie(values=ivf_counts.values, names=ivf_counts.index, 
                        hole=0.4, color_discrete_sequence=custom_colors)
            fig.update_traces(textposition='outside', textinfo='percent+label+value')
            fig.update_layout(
                height=600,
                showlegend=True,
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                margin=dict(t=0, b=0, l=0, r=0)
            )
            ivf_treatment_div = plot(fig, output_type="div")
            ivf_df = ivf_counts.reset_index()
            ivf_df.columns = ['IVF 여부', '검체 수']
        else:
            ivf_treatment_div = ""
            ivf_df = pd.DataFrame()

    except Exception as e:
        print(f"Error generating pie charts: {str(e)}")
        fetus_div = test_div = service_type_div = final_result_div = doctor_div = ivf_treatment_div = ""
        fetus_df = test_df = service_df = result_df = doctor_df = ivf_df = pd.DataFrame()
    
    # Time series plots with error handling
    try:
        columns_to_load = ['jedan', 'jedan_branch', 'fetus', "doctor",
                          'service_type', 'entry_date', "ivf", 'final_result', 'Counts']
        columns_for_group = ['jedan', 'jedan_branch', "ivf", 'fetus', "doctor",
                            'service_type', 'entry_date', 'final_result']
        
        first_group = df.loc[:, columns_to_load].groupby(columns_for_group).sum().reset_index()
        all_samples_counts = first_group.loc[:, ["entry_date", 'Counts']].groupby(["entry_date"]).sum().reset_index()
        all_samples_counts.sort_values(by="entry_date", inplace=True)
        
        # Result type daily trend
        sample_type_group = first_group.loc[:, ["entry_date", 'final_result', "Counts"]].groupby(
            ["entry_date", 'final_result']).sum().reset_index()
        
        if not sample_type_group.empty:
            fig = px.line(sample_type_group, x="entry_date", y="Counts",
                         color="final_result", symbol="final_result",
                         labels={'final_result': '결과', "entry_date": '검체 입고일'}, height=700)
            
            fig.add_scatter(x=all_samples_counts["entry_date"], y=all_samples_counts["Counts"],
                           mode='lines+markers', name="Total", line=dict(color='green', width=4))
            
            fig.update_layout(
                legend=dict(orientation="h", font=dict(family="Courier", size=14, color="black")),
                width=width, height=height
            )
            
            result_type_div_daily = plot(fig, output_type="div")
        else:
            result_type_div_daily = ""
            
        # Service type daily trend
        sample_type_group = first_group.loc[:, ["entry_date", 'service_type', "Counts"]].groupby(
            ["entry_date", 'service_type']).sum().reset_index()
        
        if not sample_type_group.empty:
            fig = px.line(sample_type_group, x="entry_date", y="Counts",
                         color="service_type", symbol="service_type",
                         labels={'service_type': '서비스 유형', "entry_date": '검체 입고일'}, height=700)
            
            fig.add_scatter(x=all_samples_counts["entry_date"], y=all_samples_counts["Counts"],
                           mode='lines+markers', name="Total", line=dict(color='green', width=4))
            
            fig.update_layout(
                legend=dict(orientation="h", font=dict(family="Courier", size=14, color="black")),
                width=width, height=height
            )
            
            service_type_div_daily = plot(fig, output_type="div")
        else:
            service_type_div_daily = ""
            
        # Doctor daily trend
        sample_type_group = first_group.loc[:, ["entry_date", 'doctor', "Counts"]].groupby(
            ["entry_date", 'doctor']).sum().reset_index()
        
        if not sample_type_group.empty:
            fig = px.line(sample_type_group, x="entry_date", y="Counts",
                         color="doctor", symbol="doctor",
                         labels={'doctor': '의사', "entry_date": '검체 입고일'}, height=700)
            
            fig.update_layout(
                legend=dict(orientation="h", font=dict(family="Courier", size=14, color="black")),
                width=width, height=height
            )
            
            doctor_div_daily = plot(fig, output_type="div")
        else:
            doctor_div_daily = ""
            
    except Exception as e:
        print(f"Error generating time series plots: {str(e)}")
        result_type_div_daily = service_type_div_daily = doctor_div_daily = ""
    
    # Monthly bar plots with error handling
    try:
        # Service type monthly
        service_monthly = df.groupby(['yy_mm', 'service_type'])['Counts'].sum().reset_index()
        if not service_monthly.empty:
            # Sort by year and month
            service_monthly['year'] = service_monthly['yy_mm'].str.extract(r'(\d+)년').astype(int)
            service_monthly['month'] = service_monthly['yy_mm'].str.extract(r'_(\d+)_월').astype(int)
            service_monthly = service_monthly.sort_values(['year', 'month'])
            service_monthly = service_monthly.drop(['year', 'month'], axis=1)
            
            fig = px.bar(service_monthly, x='yy_mm', y='Counts', color='service_type',
                        labels={'yy_mm': '년월', 'Counts': '검체 수', 'service_type': '검사 항목'})
            fig.update_layout(
                height=800,
                xaxis=dict(
                    tickangle=-45,
                    title=dict(
                        text='년월',
                        font=dict(size=16, color='black', family='Arial Black')
                    ),
                    tickfont=dict(size=14, color='black')
                ),
                yaxis=dict(
                    title=dict(
                        text='검체 수',
                        font=dict(size=16, color='black', family='Arial Black')
                    ),
                    tickfont=dict(size=14, color='black')
                ),
                showlegend=True,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="left",
                    x=0,
                    font=dict(size=10)
                ),
                margin=dict(t=50, b=0, l=0, r=0)
            )
            service_monthly_div = plot(fig, output_type="div")
            service_monthly_df = service_monthly.pivot(index='yy_mm', columns='service_type', values='Counts').reset_index()
        else:
            service_monthly_div = ""
            service_monthly_df = pd.DataFrame()

        # Doctor monthly
        doctor_monthly = df.groupby(['yy_mm', 'doctor'])['Counts'].sum().reset_index()
        if not doctor_monthly.empty:
            # Sort by year and month
            doctor_monthly['year'] = doctor_monthly['yy_mm'].str.extract(r'(\d+)년').astype(int)
            doctor_monthly['month'] = doctor_monthly['yy_mm'].str.extract(r'_(\d+)_월').astype(int)
            doctor_monthly = doctor_monthly.sort_values(['year', 'month'])
            doctor_monthly = doctor_monthly.drop(['year', 'month'], axis=1)
            
            fig = px.bar(doctor_monthly, x='yy_mm', y='Counts', color='doctor',
                        labels={'yy_mm': '년월', 'Counts': '검체 수', 'doctor': '의사'})
            fig.update_layout(
                height=800,
                xaxis=dict(
                    tickangle=-45,
                    title=dict(
                        text='년월',
                        font=dict(size=16, color='black', family='Arial Black')
                    ),
                    tickfont=dict(size=14, color='black')
                ),
                yaxis=dict(
                    title=dict(
                        text='검체 수',
                        font=dict(size=16, color='black', family='Arial Black')
                    ),
                    tickfont=dict(size=14, color='black')
                ),
                showlegend=True,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1,
                    font=dict(size=10)
                ),
                margin=dict(t=50, b=0, l=0, r=0)
            )
            doctor_monthly_div = plot(fig, output_type="div")
            doctor_monthly_df = doctor_monthly.pivot(index='yy_mm', columns='doctor', values='Counts').reset_index()
        else:
            doctor_monthly_div = ""
            doctor_monthly_df = pd.DataFrame()

        # IVF monthly
        ivf_monthly = df.groupby(['yy_mm', 'ivf'])['Counts'].sum().reset_index()
        if not ivf_monthly.empty:
            # Sort by year and month
            ivf_monthly['year'] = ivf_monthly['yy_mm'].str.extract(r'(\d+)년').astype(int)
            ivf_monthly['month'] = ivf_monthly['yy_mm'].str.extract(r'_(\d+)_월').astype(int)
            ivf_monthly = ivf_monthly.sort_values(['year', 'month'])
            ivf_monthly = ivf_monthly.drop(['year', 'month'], axis=1)
            
            fig = px.bar(ivf_monthly, x='yy_mm', y='Counts', color='ivf',
                        labels={'yy_mm': '년월', 'Counts': '검체 수', 'ivf': 'IVF 여부'})
            fig.update_layout(
                height=800,
                xaxis=dict(
                    tickangle=-45,
                    title=dict(
                        text='년월',
                        font=dict(size=16, color='black', family='Arial Black')
                    ),
                    tickfont=dict(size=14, color='black')
                ),
                yaxis=dict(
                    title=dict(
                        text='검체 수',
                        font=dict(size=16, color='black', family='Arial Black')
                    ),
                    tickfont=dict(size=14, color='black')
                ),
                showlegend=True,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="left",
                    x=0,
                    font=dict(size=10)
                ),
                margin=dict(t=50, b=0, l=0, r=0)
            )
            ivf_monthly_div = plot(fig, output_type="div")
            ivf_monthly_df = ivf_monthly.pivot(index='yy_mm', columns='ivf', values='Counts').reset_index()
        else:
            ivf_monthly_div = ""
            ivf_monthly_df = pd.DataFrame()

        # Final result monthly
        result_monthly = df.groupby(['yy_mm', 'final_result'])['Counts'].sum().reset_index()
        if not result_monthly.empty:
            # Sort by year and month
            result_monthly['year'] = result_monthly['yy_mm'].str.extract(r'(\d+)년').astype(int)
            result_monthly['month'] = result_monthly['yy_mm'].str.extract(r'_(\d+)_월').astype(int)
            result_monthly = result_monthly.sort_values(['year', 'month'])
            result_monthly = result_monthly.drop(['year', 'month'], axis=1)
            
            fig = px.bar(result_monthly, x='yy_mm', y='Counts', color='final_result',
                        labels={'yy_mm': '년월', 'Counts': '검체 수', 'final_result': '결과'})
            fig.update_layout(
                height=800,
                xaxis=dict(
                    tickangle=-45,
                    title=dict(
                        text='년월',
                        font=dict(size=16, color='black', family='Arial Black')
                    ),
                    tickfont=dict(size=14, color='black')
                ),
                yaxis=dict(
                    title=dict(
                        text='검체 수',
                        font=dict(size=16, color='black', family='Arial Black')
                    ),
                    tickfont=dict(size=14, color='black')
                ),
                showlegend=True,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="left",
                    x=0,
                    font=dict(size=10)
                ),
                margin=dict(t=50, b=0, l=0, r=0)
            )
            final_result_div_month = plot(fig, output_type="div")
            result_monthly_df = result_monthly.pivot(index='yy_mm', columns='final_result', values='Counts').reset_index()
        else:
            final_result_div_month = ""
            result_monthly_df = pd.DataFrame()

    except Exception as e:
        print(f"Error generating monthly bar plots: {str(e)}")
        service_monthly_div = doctor_monthly_div = ivf_monthly_div = final_result_div_month = ""
        service_monthly_df = doctor_monthly_df = ivf_monthly_df = result_monthly_df = pd.DataFrame()
    
    # Doctor analysis with error handling
    try:
        # Doctor result analysis
        grouped_data = df.groupby(['doctor', "final_result"]).agg({
            'Counts': 'sum',
        }).reset_index()
        
        if not grouped_data.empty:
            grouped_data.sort_values(["Counts"], inplace=True)
            fig = px.bar(grouped_data, x="doctor", y="Counts",
                        color="final_result", text="final_result")
            
            fig.update_layout(
                width=width/2, height=height/2,
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                showlegend=True,
                xaxis_title='의사',
                yaxis_title='NIPT 건수'
            )
            
            doc_final_result_div = plot(fig, config=config, output_type="div")
        else:
            doc_final_result_div = ""
            
        # Doctor IVF analysis
        grouped_data = df.groupby(['doctor', "ivf"]).agg({
            'Counts': 'sum',
        }).reset_index()
        
        if not grouped_data.empty:
            grouped_data.sort_values(["doctor", "Counts"], inplace=True)
            fig = px.bar(grouped_data, x="doctor", y="Counts",
                        color="ivf", text="ivf")
            
            fig.update_layout(
                width=width/2, height=height/2,
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                showlegend=True,
                xaxis_title='의사',
                yaxis_title='NIPT 건수'
            )
            
            doc_ivf_div = plot(fig, config=config, output_type="div")
        else:
            doc_ivf_div = ""
            
    except Exception as e:
        print(f"Error generating doctor analysis plots: {str(e)}")
        doc_final_result_div = doc_ivf_div = ""
    
    context = {
        "hospital": hospital,
        "start_date": start_date,
        "end_date": end_date,
        "total_samples": total_samples,
        "total_doctors": total_doctors,
        "avg_samples_per_day": round(avg_samples_per_day, 1),
        "avg_samples_per_month": round(avg_samples_per_month, 1),
        "avg_processing_time": round(avg_processing_time, 1),
        "high_risk_rate": round(high_risk_rate, 1),
        
        # Visualizations
        "fetus_div": fetus_div,
        "test_div": test_div,
        "service_type_div": service_type_div,
        "final_result_div": final_result_div,
        "doctor_div": doctor_div,
        "ivf_treatment_div": ivf_treatment_div,
        
        # Daily trends
        "result_type_div_daily": result_type_div_daily,
        "service_type_div_daily": service_type_div_daily,
        "doctor_div_daily": doctor_div_daily,
        
        # Monthly trends
        "service_monthly_div": service_monthly_div,
        "doctor_monthly_div": doctor_monthly_div,
        "ivf_monthly_div": ivf_monthly_div,
        "final_result_div_month": final_result_div_month,
        
        # Doctor analysis
        "doc_final_result_div": doc_final_result_div,
        "doc_ivf_div": doc_ivf_div,
        
        # Export DataFrames
        "fetus_df": fetus_df,
        "test_df": test_df,
        "service_df": service_df,
        "result_df": result_df,
        "doctor_df": doctor_df,
        "ivf_df": ivf_df,
        "service_monthly_df": service_monthly_df,
        "doctor_monthly_df": doctor_monthly_df,
        "ivf_monthly_df": ivf_monthly_df,
        "result_monthly_df": result_monthly_df,
        
        "active_menu": "dashboard",
    }
    
    template = "dashboards/hospital_report_detail.html"
    return render(request, template_name=template, context=context)
