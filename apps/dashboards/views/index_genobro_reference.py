# -*- encoding: utf-8 -*-
"""
Copyright (c) 2021 - present Theragen Genome Care
"""
# Import file from Django
from cgitb import text
import string
import os
from datetime import date, timedelta
from django.http import JsonResponse
from django.core import serializers
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect
from django.template import loader
from django.http import HttpResponse
from django.contrib.auth import authenticate
from django.contrib import messages
from django.contrib.auth import logout
from django.contrib.auth.hashers import make_password
from pyparsing import dbl_quoted_string

######################################################
from tgc.authentication.decorators import user_required, update, admin_required
from tgc.authentication.forms import UserUpdateForm
from tgc.authentication.models import Country, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, User, Employee
from tgc.hospital.forms import HospitalInfoForm
from tgc.hospital.models import <PERSON><PERSON><PERSON><PERSON>, HospitalInfo
from tgc.roleadmin.forms import Doctor<PERSON><PERSON><PERSON>p<PERSON>orm, EventForm, <PERSON>anBranchForm, JedanForm, SignUpForm
from tgc.roleadmin.models import Todo
from tgc.hospital.models import Jedan, JedanBranch
from tgc.common.paginator import pagination_of_sample
from tgc.roleadmin.models.event import Event

#os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"
from tgc.genobro.models.sampleInfo import SampleInfo
from tgc.genobro.models.patient import PatientInfo
from tgc.genobro.models.sales import Sale

# Plotly
import numpy as np
import pandas as pd

from plotly.offline import plot
import plotly.express as px
import plotly.graph_objects as go

from django.core.cache import cache
from django.db.models import Count, Sum, Avg
from django.db import transaction

def get_dashboard_data(request=None):
    """Fetch and process dashboard data from database"""
    cache_key = 'dashboard_data_cache'
    cached_data = cache.get(cache_key)
    
    if cached_data is not None and not request:
        return cached_data
        
    # Get date range from request
    start_date = None
    end_date = None
    if request and request.GET.get('start_date') and request.GET.get('end_date'):
        try:
            start_date = pd.to_datetime(request.GET.get('start_date')).date()
            end_date = pd.to_datetime(request.GET.get('end_date')).date()
        except:
            pass
    
    # Base query
    samples = SampleInfo.objects.filter(is_active=True)
    total_samples = samples.count()
    # print(f"Total active samples: {total_samples}")
    
    # Apply date filter only if both dates are explicitly provided
    if start_date and end_date:
        filtered_samples = samples.filter(patient__created_at__date__range=[start_date, end_date])
        filtered_count = filtered_samples.count()
        # print(f"Filtered samples count: {filtered_count}")
        # print(f"Date range: {start_date} to {end_date}")
        samples = filtered_samples
    
    samples = samples.values(
        'sample_type',
        'sample_status',
        'biopsy_date',
        'final_result',
        'patient__jedan_name__jedan_name',
        'patient__jedan_branch__jedan_branch_name',
        'patient__hospital__hospital_name',
        'patient__doctor__full_name',
        'patient__age',
        'patient__test_type',
        'patient__service_type',
        'patient__created_at',
        'is_qc_pass',
        'server',
        'server_status'
    )
    
    if not samples:
        print("No samples found after values query")
        return pd.DataFrame()
        
    df = pd.DataFrame(samples)
    print(f"DataFrame shape: {df.shape}")
    
    # Check for null values in created_at
    null_dates = df['patient__created_at'].isnull().sum()
    print(f"Null created_at values: {null_dates}")
    
    df["Counts"] = 1
    
    # Rename columns for clarity
    df.rename(columns={
        'patient__jedan_name__jedan_name': "jedan",
        'patient__jedan_branch__jedan_branch_name': "jedan_branch",
        'patient__hospital__hospital_name': "hospital",
        'patient__doctor__full_name': 'doctor',
        'patient__age': 'age',
        'patient__test_type': "test_type",
        'patient__service_type': 'service_type',
        'patient__created_at': 'created_at'
    }, inplace=True)
    
    # Fill missing doctor names with 'Unknown'
    df['doctor'] = df['doctor'].fillna('Unknown')
    
    # Process dates
    df['created_at'] = pd.to_datetime(df['created_at'], errors='coerce')
    df = df.dropna(subset=['created_at'])  # Remove rows with invalid dates
    print(f"DataFrame shape after date processing: {df.shape}")
    
    df['month'] = df['created_at'].dt.strftime('%Y-%m')
    
    # Cache the processed data for 1 hour if no date filter
    if not start_date and not end_date:
        cache.set(cache_key, df, 3600)
    
    return df

def create_sample_status_chart(df: pd.DataFrame) -> str:
    """Create pie chart for sample status distribution"""
    # Group and sort data
    status_data = df.groupby('sample_status')['Counts'].sum().reset_index()
    status_data = status_data.sort_values('Counts', ascending=False)
    
    fig = px.pie(
        status_data,
        values='Counts',
        names='sample_status',
        labels={'sample_status': '검체 주기'},
        hole=0.4  # Make it a donut chart
    )
    
    fig.update_traces(
        textinfo='percent+label+value',
        textfont_size=14,
        textposition='outside',
        marker=dict(line=dict(color='#000000', width=1))
    )
    
    fig.update_layout(
        plot_bgcolor='white',
        paper_bgcolor='white',
        height=500,
        showlegend=True,
        legend=dict(
            orientation="v",
            yanchor="middle",
            y=0.5,
            xanchor="left",
            x=1.1,
            font=dict(size=14, family='Arial Black', color='rgb(0, 51, 102)')
        )
    )
    
    return plot(fig, output_type='div')

def create_sample_type_chart(df: pd.DataFrame) -> str:
    """Create pie chart for sample type distribution"""
    # Group and sort data
    type_data = df.groupby('sample_type')['Counts'].sum().reset_index()
    type_data = type_data.sort_values('Counts', ascending=False)
    
    fig = px.pie(
        type_data,
        values='Counts',
        names='sample_type',
        labels={'sample_type': '검체 종류'},
        hole=0.4
    )
    
    fig.update_traces(
        textinfo='percent+label+value',
        textfont_size=14,
        textposition='outside',
        marker=dict(line=dict(color='#000000', width=1))
    )
    
    fig.update_layout(
        plot_bgcolor='white',
        paper_bgcolor='white',
        height=500,
        showlegend=True,
        legend=dict(
            orientation="v",
            yanchor="middle",
            y=0.5,
            xanchor="left",
            x=1.1,
            font=dict(size=14, family='Arial Black', color='rgb(0, 51, 102)')
        )
    )
    
    return plot(fig, output_type='div')

def create_biopsy_date_chart(df: pd.DataFrame) -> str:
    """Create pie chart for biopsy date distribution"""
    # Group and sort data
    biopsy_data = df.groupby('biopsy_date')['Counts'].sum().reset_index()
    biopsy_data = biopsy_data.sort_values('Counts', ascending=False)
    
    fig = px.pie(
        biopsy_data,
        values='Counts',
        names='biopsy_date',
        labels={'biopsy_date': '생검 단계'},
        hole=0.4
    )
    
    fig.update_traces(
        textinfo='percent+label+value',
        textfont_size=14,
        textposition='outside',
        marker=dict(line=dict(color='#000000', width=1))
    )
    
    fig.update_layout(
        plot_bgcolor='white',
        paper_bgcolor='white',
        height=500,
        showlegend=True,
        legend=dict(
            orientation="v",
            yanchor="middle",
            y=0.5,
            xanchor="left",
            x=1.1,
            font=dict(size=14, family='Arial Black', color='rgb(0, 51, 102)')
        )
    )
    
    return plot(fig, output_type='div')

def create_final_result_chart(df: pd.DataFrame) -> str:
    """Create pie chart for final result distribution"""
    # Group and sort data
    df['final_result'] = df['final_result'].replace('', '-')
    result_data = df.groupby('final_result')['Counts'].sum().reset_index()
    result_data = result_data.sort_values('Counts', ascending=False)
    
    fig = px.pie(
        result_data,
        values='Counts',
        names='final_result',
        labels={'final_result': '검사 결과'},
        hole=0.4
    )
    
    fig.update_traces(
        textinfo='percent+label+value',
        textfont_size=14,
        textposition='outside',
        marker=dict(line=dict(color='#000000', width=1))
    )
    
    fig.update_layout(
        plot_bgcolor='white',
        paper_bgcolor='white',
        height=500,
        showlegend=True,
        legend=dict(
            orientation="v",
            yanchor="middle",
            y=0.5,
            xanchor="left",
            x=1.1,
            font=dict(size=14, family='Arial Black', color='rgb(0, 51, 102)')
        )
    )
    
    return plot(fig, output_type='div')

def create_monthly_trend_chart(df: pd.DataFrame) -> str:
    """Create line chart for monthly sample trends"""
    monthly_data = df.groupby('month').agg({
        'Counts': 'sum',
        'sample_type': 'count'
    }).reset_index()
    
    # Calculate regression line with error handling
    x = np.arange(len(monthly_data))
    y = monthly_data['Counts'].values
    
    # Initialize trend line and growth rate
    trend_line = None
    growth_rate = 0
    
    try:
        # Only calculate trend if we have enough data points
        if len(x) >= 2 and not np.all(y == 0):
            z = np.polyfit(x, y, 1)
            p = np.poly1d(z)
            trend_line = p(x)
            
            # Calculate growth rate safely
            if trend_line[0] != 0:
                growth_rate = ((trend_line[-1] - trend_line[0]) / abs(trend_line[0])) * 100
    except Exception as e:
        print(f"Error calculating trend line: {str(e)}")
        trend_line = None
        growth_rate = 0
    
    # Create a figure with secondary y-axis
    fig = go.Figure()
    
    # Add bar trace for counts with professional gradient colors
    colors = []
    max_count = monthly_data['Counts'].max()
    for count in monthly_data['Counts']:
        # Create a gradient from light blue to medium blue (professional color scheme)
        ratio = count / max_count if max_count > 0 else 0
        r = int(135 + (41 - 135) * ratio)
        g = int(206 + (128 - 206) * ratio)
        b = int(235 + (185 - 235) * ratio)
        colors.append(f'rgba({r}, {g}, {b}, 0.85)')
    
    fig.add_trace(go.Bar(
        x=monthly_data['month'],
        y=monthly_data['Counts'],
        name='Total Samples',
        marker_color=colors,
        marker_line_color='rgb(0, 51, 102)',
        marker_line_width=2,
        opacity=0.9,
        text=monthly_data['Counts'],
        textposition='outside',
        texttemplate='%{text:,}',
        textfont=dict(
            size=16,
            color='rgb(0, 51, 102)',
            family='Arial Black'
        ),
        hovertemplate="<b>%{x}</b><br>Count: %{y:,}<br><extra></extra>"
    ))
    
    # Add moving average line if we have enough data points
    if len(monthly_data) >= 3:
        moving_avg = monthly_data['Counts'].rolling(window=3, min_periods=1).mean()
        fig.add_trace(go.Scatter(
            x=monthly_data['month'],
            y=moving_avg,
            name='3-Month Moving Average',
            line=dict(color='rgb(220, 53, 69)', width=4),
            mode='lines',
            hovertemplate="<b>%{x}</b><br>Moving Average: %{y:,.0f}<br><extra></extra>"
        ))
    
    # Add regression line if calculation was successful
    if trend_line is not None and len(trend_line) > 0:
        fig.add_trace(go.Scatter(
            x=monthly_data['month'],
            y=trend_line,
            name=f'Trend Line ({growth_rate:.1f}% Growth)',
            line=dict(color='rgb(40, 167, 69)', width=4, dash='dot'),
            mode='lines',
            hovertemplate="<b>%{x}</b><br>Trend: %{y:,.0f}<br><extra></extra>"
        ))
        
        # Add growth rate annotation only if we have valid data and growth rate
        if len(trend_line) > 1 and growth_rate != 0:
            fig.add_annotation(
                x=monthly_data['month'].iloc[-1],
                y=trend_line[-1],
                text=f"↑ {growth_rate:.1f}%",
                showarrow=True,
                arrowhead=2,
                arrowsize=1.5,
                arrowwidth=3,
                arrowcolor='rgb(40, 167, 69)',
                font=dict(size=16, color='rgb(40, 167, 69)', family='Arial Black'),
                ax=40,
                ay=-40
            )
    
    fig.update_layout(
        title=None,  # Remove title
        xaxis_title='월',
        yaxis_title='샘플 수',
        hovermode='x unified',
        plot_bgcolor='white',
        paper_bgcolor='white',
        height=700,
        margin=dict(l=50, r=50, t=50, b=80),  # Reduced top margin
        xaxis=dict(
            tickangle=-45,
            tickfont=dict(size=14, family='Arial Black', color='rgb(0, 51, 102)'),
            gridcolor='lightgray',
            zerolinecolor='lightgray',
            showgrid=True,
            title_font=dict(size=16, family='Arial Black', color='rgb(0, 51, 102)')
        ),
        yaxis=dict(
            tickfont=dict(size=14, family='Arial Black', color='rgb(0, 51, 102)'),
            gridcolor='lightgray',
            zerolinecolor='lightgray',
            showgrid=True,
            tickformat=',',
            title_font=dict(size=16, family='Arial Black', color='rgb(0, 51, 102)')
        ),
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="center",
            x=0.5,
            font=dict(size=14, family='Arial Black', color='rgb(0, 51, 102)')
        ),
        showlegend=True
    )
    
    return plot(fig, output_type='div')

def create_hospital_distribution_chart(df: pd.DataFrame) -> str:
    """Create bar chart for top hospitals by sample count"""
    hospital_data = df.groupby('hospital').agg({
        'Counts': 'sum'
    }).sort_values('Counts', ascending=False).head(10)
    
    # Create color scale based on count values
    max_count = hospital_data['Counts'].max()
    colors = [f'rgba(23, 162, 184, {0.3 + (count/max_count)*0.7})' for count in hospital_data['Counts']]
    
    fig = go.Figure()
    fig.add_trace(go.Bar(
        x=hospital_data.index,
        y=hospital_data['Counts'],
        text=hospital_data['Counts'],
        textposition='outside',
        textangle=0,
        marker_color=colors,
        marker_line_color='rgb(8,48,107)',
        marker_line_width=1.5,
        hovertemplate="<b>%{x}</b><br>Count: %{y}<br><extra></extra>"
    ))
    
    fig.update_layout(
        title=None,  # Remove title
        xaxis_title='의료기관',
        yaxis_title='샘플 수',
        plot_bgcolor='white',
        paper_bgcolor='white',
        height=500,
        margin=dict(l=50, r=50, t=50, b=150),  # Reduced top margin
        xaxis=dict(
            tickangle=-45,
            tickfont=dict(size=12, family='Arial Black', color='rgb(0, 51, 102)'),
            gridcolor='lightgray',
            zerolinecolor='lightgray',
            title_font=dict(size=16, family='Arial Black', color='rgb(0, 51, 102)')
        ),
        yaxis=dict(
            tickfont=dict(size=12, family='Arial Black', color='rgb(0, 51, 102)'),
            gridcolor='lightgray',
            zerolinecolor='lightgray',
            title_font=dict(size=16, family='Arial Black', color='rgb(0, 51, 102)')
        ),
        showlegend=False
    )
    
    return plot(fig, output_type='div')

def create_qc_status_chart(df: pd.DataFrame) -> str:
    """Create pie chart for QC status distribution"""
    qc_data = df.groupby('is_qc_pass').agg({
        'Counts': 'sum'
    }).reset_index()
    
    fig = px.pie(
        qc_data,
        values='Counts',
        names='is_qc_pass',
        title='QC Status Distribution',
        labels={'is_qc_pass': 'QC Status'}
    )
    fig.update_traces(textinfo='percent+label+value', showlegend=False)
    return plot(fig, output_type='div')

def create_server_status_chart(df: pd.DataFrame) -> str:
    """Create pie chart for server status distribution"""
    server_data = df.groupby('server_status').agg({
        'Counts': 'sum'
    }).reset_index()
    
    fig = px.pie(
        server_data,
        values='Counts',
        names='server_status',
        title='Server Status Distribution',
        labels={'server_status': 'Server Status'}
    )
    fig.update_traces(textinfo='percent+label+value', showlegend=False)
    return plot(fig, output_type='div')

def create_top_hospital_chart(df: pd.DataFrame) -> str:
    """Create bar chart for top hospitals by sample count"""
    hospital_data = df.groupby(['hospital', 'jedan_branch']).agg({
        'Counts': 'sum'
    }).reset_index()
    
    hospital_data = hospital_data.sort_values('Counts', ascending=False).head(50)
    
    # Create vibrant color scale based on count values
    max_count = hospital_data['Counts'].max()
    colors = []
    for count in hospital_data['Counts']:
        # Create a gradient from blue to purple based on count
        ratio = count / max_count
        r = int(23 + (147 - 23) * ratio)
        g = int(162 + (112 - 162) * ratio)
        b = int(184 + (219 - 184) * ratio)
        colors.append(f'rgba({r}, {g}, {b}, 0.85)')
    
    fig = go.Figure()
    fig.add_trace(go.Bar(
        x=hospital_data['hospital'],
        y=hospital_data['Counts'],
        text=hospital_data['Counts'],
        textposition='outside',
        textangle=0,
        texttemplate='%{text:,}',
        textfont=dict(
            size=14,
            color='rgb(8,48,107)',
            family='Arial Black'
        ),
        marker_color=colors,
        marker_line_color='rgb(8,48,107)',
        marker_line_width=2,
        hovertemplate="<b>%{x}</b><br>Branch: %{customdata}<br>Count: %{y:,}<br><extra></extra>",
        customdata=hospital_data['jedan_branch']
    ))
    
    fig.update_layout(
        title=None,  # Remove title
        xaxis_title='의료기관',
        yaxis_title='샘플 수',
        plot_bgcolor='white',
        paper_bgcolor='white',
        height=700,
        margin=dict(l=50, r=50, t=50, b=150),  # Reduced top margin
        xaxis=dict(
            tickangle=-45,
            tickfont=dict(size=12, family='Arial Black', color='rgb(0, 51, 102)'),
            gridcolor='lightgray',
            zerolinecolor='lightgray',
            showgrid=True,
            title_font=dict(size=16, family='Arial Black', color='rgb(0, 51, 102)')
        ),
        yaxis=dict(
            tickfont=dict(size=12, family='Arial Black', color='rgb(0, 51, 102)'),
            gridcolor='lightgray',
            zerolinecolor='lightgray',
            showgrid=True,
            tickformat=',',
            title_font=dict(size=16, family='Arial Black', color='rgb(0, 51, 102)')
        ),
        showlegend=False,
        annotations=[
            dict(
                x=0.5,
                y=-0.2,
                xref='paper',
                yref='paper',
                text='막대 위에 마우스를 올려 지점 정보를 확인하세요',
                showarrow=False,
                font=dict(size=14, color='rgb(0, 51, 102)', family='Arial Black')
            )
        ]
    )
    
    return plot(fig, output_type='div')

def create_top_doctor_chart(df: pd.DataFrame) -> str:
    """Create bar chart for top doctors by sample count"""
    doctor_data = df[df['doctor'] != 'Unknown'].groupby(['doctor', 'hospital', 'jedan_branch']).agg({
        'Counts': 'sum'
    }).reset_index()
    
    doctor_data = doctor_data.sort_values('Counts', ascending=False).head(50)
    
    if doctor_data.empty:
        fig = go.Figure()
        fig.update_layout(
            title='Top 50 Doctors by Sample Count',
            plot_bgcolor='white',
            paper_bgcolor='white',
            height=700
        )
        return plot(fig, output_type='div')
    
    # Create vibrant color scale based on count values
    max_count = doctor_data['Counts'].max()
    colors = []
    for count in doctor_data['Counts']:
        # Create a gradient from green to blue based on count
        ratio = count / max_count
        r = int(46 + (23 - 46) * ratio)
        g = int(204 + (162 - 204) * ratio)
        b = int(113 + (184 - 113) * ratio)
        colors.append(f'rgba({r}, {g}, {b}, 0.85)')
    
    fig = go.Figure()
    fig.add_trace(go.Bar(
        x=doctor_data['doctor'],
        y=doctor_data['Counts'],
        text=doctor_data['Counts'],
        textposition='outside',
        textangle=0,
        texttemplate='%{text:,}',
        textfont=dict(
            size=14,
            color='rgb(8,48,107)',
            family='Arial Black'
        ),
        marker_color=colors,
        marker_line_color='rgb(8,48,107)',
        marker_line_width=2,
        hovertemplate="<b>%{x}</b><br>Hospital: %{customdata[0]}<br>Branch: %{customdata[1]}<br>Count: %{y:,}<br><extra></extra>",
        customdata=doctor_data[['hospital', 'jedan_branch']].values
    ))
    
    fig.update_layout(
        title=None,  # Remove title
        xaxis_title='의사',
        yaxis_title='샘플 수',
        plot_bgcolor='white',
        paper_bgcolor='white',
        height=700,
        margin=dict(l=50, r=50, t=50, b=150),  # Reduced top margin
        xaxis=dict(
            tickangle=-45,
            tickfont=dict(size=12, family='Arial Black', color='rgb(0, 51, 102)'),
            gridcolor='lightgray',
            zerolinecolor='lightgray',
            showgrid=True,
            title_font=dict(size=16, family='Arial Black', color='rgb(0, 51, 102)')
        ),
        yaxis=dict(
            tickfont=dict(size=12, family='Arial Black', color='rgb(0, 51, 102)'),
            gridcolor='lightgray',
            zerolinecolor='lightgray',
            showgrid=True,
            tickformat=',',
            title_font=dict(size=16, family='Arial Black', color='rgb(0, 51, 102)')
        ),
        showlegend=False,
        annotations=[
            dict(
                x=0.5,
                y=-0.2,
                xref='paper',
                yref='paper',
                text='막대 위에 마우스를 올려 의료기관 및 지점 정보를 확인하세요',
                showarrow=False,
                font=dict(size=14, color='rgb(0, 51, 102)', family='Arial Black')
            )
        ]
    )
    
    return plot(fig, output_type='div')

def export_excel(dataframe, filename):
    """Helper function to export DataFrame to Excel"""
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = f'attachment; filename={filename}'
    
    with pd.ExcelWriter(response, engine='openpyxl') as writer:
        dataframe.to_excel(writer, index=True)
    
    return response

@login_required(login_url="/")
@user_required
@update
def index(request):
    """Handle main dashboard for admin"""
    try:
        # Handle Excel exports
        if 'export' in request.GET:
            df = get_dashboard_data(request)
            if df.empty:
                return render(request, 'roleadmin/index.html')
                
            chart_type = request.GET.get('type')
            if chart_type == 'sample_status':
                data = df.groupby('sample_status')['Counts'].sum().reset_index()
                return export_excel(data, 'sample_status.xlsx')
            elif chart_type == 'sample_type':
                data = df.groupby('sample_type')['Counts'].sum().reset_index()
                return export_excel(data, 'sample_type.xlsx')
            elif chart_type == 'biopsy_date':
                data = df.groupby('biopsy_date')['Counts'].sum().reset_index()
                return export_excel(data, 'biopsy_date.xlsx')
            elif chart_type == 'final_result':
                data = df.groupby('final_result')['Counts'].sum().reset_index()
                return export_excel(data, 'final_result.xlsx')
            elif chart_type == 'monthly_trend':
                data = df.groupby('month')['Counts'].sum().reset_index()
                return export_excel(data, 'monthly_trend.xlsx')
            elif chart_type == 'top_hospital':
                data = df.groupby(['hospital', 'jedan_branch'])['Counts'].sum().reset_index()
                data = data.sort_values('Counts', ascending=False).head(50)
                return export_excel(data, 'top_hospitals.xlsx')
            elif chart_type == 'top_doctor':
                data = df[df['doctor'] != 'Unknown'].groupby(['doctor', 'hospital', 'jedan_branch'])['Counts'].sum().reset_index()
                data = data.sort_values('Counts', ascending=False).head(50)
                return export_excel(data, 'top_doctors.xlsx')
        
        df = get_dashboard_data(request)
        if df.empty:
            return render(request, 'roleadmin/index.html')
            
        # Create all charts
        charts = {
            'sample_status_div': create_sample_status_chart(df),
            'sample_type_div': create_sample_type_chart(df),
            'biopsy_date_div': create_biopsy_date_chart(df),
            'final_result_div': create_final_result_chart(df),
            'monthly_trend_div': create_monthly_trend_chart(df),
            'top_hospital_div': create_top_hospital_chart(df),
            'top_doctor_div': create_top_doctor_chart(df),
            'qc_status_div': create_qc_status_chart(df),
            'server_status_div': create_server_status_chart(df)
        }
        
        # Calculate summary statistics
        summary_stats = {
            'total_samples': len(df),
            'total_hospitals': df['hospital'].nunique(),
            'total_jedan_branches': df['jedan_branch'].nunique(),
            'avg_age': df['age'].astype(float).mean(),
            'service_samples': len(df[df['test_type'] == 'Service']),
            'test_samples': len(df[df['test_type'] == 'Test']),
            'free_samples': len(df[df['test_type'] == 'Free']),
            'pass_samples': len(df[df['server_status'] == 'Pass']),
            'fail_samples': len(df[df['server_status'] == 'Fail'])
        }
        
        context = {
            **charts,
            'summary_stats': summary_stats,
            'active_menu': 'dashboard'
        }
        
        return render(request, 'roleadmin/index.html', context)
        
    except Exception as e:
        # Log the error and return a user-friendly error page
        return render(request, 'roleadmin/error.html', {'error': str(e)})
        
