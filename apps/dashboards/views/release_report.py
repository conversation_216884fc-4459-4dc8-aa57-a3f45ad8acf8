import pandas as pd
import numpy as np
from datetime import datetime
from django.shortcuts import render
from apps.authentication.decorators import admin_required, staff_required
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
import io

from apps.genomom.models import SampleInfo

def export_to_excel(df, filename):
    """Helper function to export DataFrame to Excel"""
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='Sheet1', index=True)
    output.seek(0)
    response = HttpResponse(
        output.read(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    return response

@login_required(login_url="/")
# ! Show to All Staff / Employee
@staff_required(allowed_user_groups=[  "7", "6"] )
def release_report(request):
    """
    출고 관리 보고서 입니다.
    
    
    """
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    # Handle export requests
    if request.GET.get('export'):
        export_type = request.GET.get('export')
        if export_type == 'entry_count':
            return export_to_excel(entry_count_df, 'entry_count_report.xlsx')
        elif export_type == 'release_count':
            return export_to_excel(release_count_df, 'release_count_report.xlsx')
    
    #! Get all Samples from data base 
    all_samples = SampleInfo.objects.filter(is_active = True, )
    
    # Apply date filter if dates are provided
    if start_date and end_date:
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            all_samples = all_samples.filter(entry_date__range=[start_date, end_date])
        except ValueError:
            pass
    
    samples = all_samples.values("patient__jedan__jedan_name",           
                             #!"service_type__service_name",
                             "entry_date",
                             "report_publish",
                             "result_sent_time", 
                             "samples_trisomy__final_result2",   ) # 
    
    df = pd.DataFrame(samples ) 
    
    df.rename(columns={'patient__jedan__jedan_name': "jedan",
                
                'patient__created_at':"created_at",
                #!"service_type__service_name":"service_type" ,
                # "samples_trisomy__final_result2": "final_result",
                #"samples_trisomy__sample_result":"sample_result"
                
            }, inplace=True)
    
    #! 
    
    
    df["Counts"] = 1

    df["entry_date"] = pd.to_datetime(df.entry_date , format='%y%m%d', errors='ignore')

    df['result_sent_time'] = pd.to_datetime(df.result_sent_time.dt.date)
    
    # df["yymm"] = df.result_sent_time.astype(str).str[:7] # For Biar Diagram
    # df["day"] = df.result_sent_time.astype(str).str[-2:]
    
    #! On the basis of Sample Entry Date 
    df["yymm"] = df.entry_date.astype(str).str[2:7] # For Biar Diagram
    df["day"] = df.entry_date.astype(str).str[-2:]
    
    #! For ordering Jedan Name keep number 
    df["jedan"] = df.jedan.replace({"SML": "1.SML", "SCL": "2.SCL",
                                    'Seegene': "3.Seegene", 'EONE': "4:Eone",
                                    'KCL': "5.KCL", 'SD': "6:SD",
                                    'TGC': "7:TGC"})
    
    
    
    
    
    pivot_cnt = pd.pivot_table(df,
                     values=["Counts"],
                     index= ["yymm", "jedan",], # "service_type"
                     columns=[ "day" ],
                     aggfunc= 'sum',
                     
                     fill_value = 0 )



    pivot_cnt.rename_axis(index = {"jedan": "재단",  "yymm": "년-월", "day": '일'} , inplace= True ) # "service_type" : "서비스" ,
    pivot_cnt.insert(0, "전체", pivot_cnt.sum(axis=1))


    entry_count_html = pivot_cnt.to_html()
    
    # release_count_html = release_count_html.replace( 'class="dataframe"', 'class="table table-striped table-hover table-bordered text-center dataframe"')
    
    entry_count_html = entry_count_html.replace(
        'class="dataframe"', 'class="table-bordered text-center dataframe"')
    
    
    
    
    
    #! On the basis of Release date
    df["yymm"] = df.result_sent_time.astype(str).str[2:7] # For Biar Diagram
    df["day"] = df.result_sent_time.astype(str).str[-2:]



    pivot_cnt = pd.pivot_table(df,
                               values=["Counts"],
                               index=["yymm", "jedan",],  # "service_type"
                               columns=["day"],
                               aggfunc='sum',

                               fill_value=0)

    # "service_type" : "서비스" ,
    pivot_cnt.rename_axis(
        index={"jedan": "재단",  "yymm": "년-월", "day": '일'}, inplace=True)
    pivot_cnt.insert(0, "전체 건수", pivot_cnt.sum(axis=1))

    release_count_html = pivot_cnt.to_html()

    # release_count_html = release_count_html.replace( 'class="dataframe"', 'class="table table-striped table-hover table-bordered text-center dataframe"')

    release_count_html = release_count_html.replace(
        'class="dataframe"', 'class="table-bordered text-center dataframe"')
    
    # Calculate entry statistics
    entry_count_df = pd.pivot_table(
        df,
        values=["Counts"],
        index=["yymm", "jedan"],
        columns=["day"],
        aggfunc='sum',
        fill_value=0
    )
    
    entry_count_df.rename_axis(
        index={"jedan": "재단", "yymm": "년-월", "day": '일'}, 
        inplace=True
    )
    entry_count_df.insert(0, "전체", entry_count_df.sum(axis=1))
    
    # Calculate entry statistics
    total_entry = entry_count_df["전체"].sum()
    
    # Calculate average daily entry - sum all entries and divide by number of days
    entry_days = df['entry_date'].nunique()
    avg_entry = total_entry / entry_days if entry_days > 0 else 0
    
    # Find maximum daily entry
    daily_entry = df.groupby('entry_date')['Counts'].sum()
    max_entry = daily_entry.max()
    max_entry_date = daily_entry.idxmax().strftime('%Y-%m-%d')
    
    # Calculate release statistics
    release_count_df = pd.pivot_table(
        df,
        values=["Counts"],
        index=["yymm", "jedan"],
        columns=["day"],
        aggfunc='sum',
        fill_value=0
    )
    
    release_count_df.rename_axis(
        index={"jedan": "재단", "yymm": "년-월", "day": '일'}, 
        inplace=True
    )
    release_count_df.insert(0, "전체 건수", release_count_df.sum(axis=1))
    
    # Calculate release statistics
    total_release = release_count_df["전체 건수"].sum()
    
    # Calculate average daily release - sum all releases and divide by number of days
    release_days = df['result_sent_time'].nunique()
    avg_release = total_release / release_days if release_days > 0 else 0
    
    # Find maximum daily release
    daily_release = df.groupby('result_sent_time')['Counts'].sum()
    max_release = daily_release.max()
    max_release_date = daily_release.idxmax().strftime('%Y-%m-%d')
    
    # Calculate completion rate
    completion_rate = (total_release / total_entry * 100) if total_entry > 0 else 0
    
    # Calculate average processing time
    df['processing_time'] = (df['result_sent_time'] - df['entry_date']).dt.days
    avg_processing_time = df['processing_time'].mean()
    
    context = {"entry_count_html": entry_count_html,
               "release_count_html": release_count_html,
               "active_menu": "dashboard",
               "total_entry": int(total_entry),
               "avg_entry": round(avg_entry, 1),
               "max_entry": int(max_entry),
               "max_entry_date": max_entry_date,
               "total_release": int(total_release),
               "avg_release": round(avg_release, 1),
               "max_release": int(max_release),
               "max_release_date": max_release_date,
               "completion_rate": round(completion_rate, 1),
               "avg_processing_time": round(avg_processing_time, 1),
               "start_date": start_date.strftime('%Y-%m-%d') if start_date else None,
               "end_date": end_date.strftime('%Y-%m-%d') if end_date else None,
               }
    
    
    template = "dashboards/release_report.html"
    return render(request, template_name=template, context=context)
