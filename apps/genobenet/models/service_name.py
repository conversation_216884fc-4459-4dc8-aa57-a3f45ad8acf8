
from django.db import models
from simple_history.models import HistoricalRecords

#! Each sample must assign to one Service Type
class GBN_ServiceName(models.Model):
    """
    In a single Genobenet project we are doing geno find and genobenet project 
    
    """

    service_name = models.Char<PERSON>ield(
                        max_length=30,
                        verbose_name='서비스 이름',
                        help_text='이 서비스의 이름을 입력하세요. (예: 프리미엄 요금제)'
                    )
    
    service_name_eng = models.CharField(max_length=30, verbose_name="Service Name English")
    
    #! these 2 fields are used in result first page 
    service_limit = models.CharField(max_length=200, verbose_name="검사 항목 정보", blank=True , null= True )
    service_limit_eng = models.Char<PERSON>ield(max_length=200, verbose_name="Test type eng" , blank= True , null= True  )
    
    #! this field will used to get html template designed for seprate result
    pdf_name = models.CharField(
            max_length=200,
            default="contact Krish!",
            help_text="이 필드는 출고 결과지의 이름을 나타냅니다. SQL 필터링 및 PDF 디자인 시 사용되므로 변경하지 마세요."
        )
    
    # #! in result section this is little different
    test_list = models.<PERSON><PERSON><PERSON><PERSON>(max_length=200, verbose_name="검사 항목 소견서 ", default = "All" )
    test_list_eng = models.CharField(max_length=200, verbose_name="Test type eng", default = "All" )
    
    
    #! these 2 are useing in 검사 소견 parts
    service_limit_memo = models.TextField(verbose_name="검사 소견 설명", default="-", help_text="<p> </p> for new line")
    service_limit_memo_eng = models.TextField( verbose_name="검사 소견 Eng", default='-')
    
    
    service_memo = models.CharField(max_length=250, default='-', help_text="memo for developer")
    
    
    # history = HistoricalRecords()
    
    def __str__(self) -> str:
        return self.service_name
    
    class Meta:
        verbose_name = "검사 항목 (GBN/GFN)"  # table header in admin sites
        verbose_name_plural = "검사 항목 (GBN/GFN)"
        ordering = ["pk"]
