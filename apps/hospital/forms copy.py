from .models import Doctor<PERSON><PERSON><PERSON>
from .models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> , HospitalIn<PERSON>
from django.forms import ModelForm
from django.core.exceptions import ValidationError 
from django import forms
#! for function based view
from django.contrib.auth.forms import UserCreationForm

from django.contrib.auth import get_user_model


User = get_user_model()
#from apps.authentication.models import User

class SignUpForm(UserCreationForm):
    """
    User Creation form.
    same form will apply to add jedan, hospital , jedan branch

    
    """
    class Meta:
        model = User
        fields = ["username", "password1", "password2"]
        #! "name" column is added from view, name is not required but our db is very small so 
        #! for user friendly we are adding it 
        
        
        
    def __init__(self, *args, **kwargs):
        super(SignUpForm, self).__init__(*args, **kwargs)
        #self.label_suffix = ""  # Removes : as label suffix
        for fname, f in self.fields.items():
            f.widget.attrs['class'] = 'form-control'
            #f.widget.attrs['placeholders'] = self.fields[fname].label
            self.fields[fname].widget.attrs['placeholder'] = self.fields[fname].label
    
    def clean(self):
        cleaned_data = super(SignUpForm, self).clean()
        password1 = cleaned_data.get('password1')
        password2 = cleaned_data.get('password2')
        if password1 and password2 and password1 != password2:
            raise forms.ValidationError("Passwords do not match")
        return cleaned_data
    
    def clean_username(self):
        username = self.cleaned_data.get('username')
        if User.objects.filter(username=username).exists():
            raise forms.ValidationError("Username is already taken")
        return username
            
class JedanAddForm(ModelForm) :
    
    class Meta:
        model = Jedan
        fields = ("jedan_name","jedan_person", "phone_number", "email","address")
        
    
    
        # def clean_email(self):
        #     """
        #     We pass unique Email field 
            
        #     """
        #     email = self.cleaned_data["email"]
        #     email_count = Jedan.objects.filter(email=email)
            
        #     if email_count.count():
        #         raise ValidationError("Email already Exists" )
        #     return email
    
    
    def __init__(self, *args, **kwargs):
        super(JedanAddForm, self).__init__(*args, **kwargs)
        #self.label_suffix = ""  # Removes : as label suffix
                
        for fname, f in self.fields.items():
            f.widget.attrs['class'] = 'form-control'
            self.fields[fname].widget.attrs['placeholder'] = self.fields[fname].label
            #f.widget.attrs['placeholders'] = self.fields[fname].label

    def clean_email(self):
            email = self.cleaned_data.get('email')
            if email:
                # Django's built-in email validation
                from django.core.validators import validate_email
                from django.core.exceptions import ValidationError
                try:
                    validate_email(email)
                except ValidationError:
                    raise forms.ValidationError("Invalid email address")
            return email
            

class JedanBranchAddForm(ModelForm):

    class Meta:
        model = JedanBranch
        fields = ("jedan","jedan_branch_name", "phone_number", "address")

    def __init__(self, *args, **kwargs):
        super(JedanBranchAddForm, self).__init__(*args, **kwargs)
        # self.label_suffix = ""  # Removes : as label suffix

        for fname, f in self.fields.items():
            f.widget.attrs['class'] = 'form-control'
            self.fields[fname].widget.attrs['placeholder'] = self.fields[fname].label
            # f.widget.attrs['placeholders'] = self.fields[fname].label

    # def clean_email(self):
    #     email = self.cleaned_data.get('email')
    #     if email:
    #         # Django's built-in email validation
    #         from django.core.validators import validate_email
    #         from django.core.exceptions import ValidationError
    #         try:
    #             validate_email(email)
    #         except ValidationError:
    #             raise forms.ValidationError("Invalid email address")
    #     return email

class HospitalAddForm(ModelForm):
    
    
    class Meta:
        model = HospitalInfo
        
        fields = ("hospital_name", "phone_number",
                  "price_lite", "price_std", "price_plus", "price_twin")
        

    def __init__(self, *args, **kwargs):
        super(HospitalAddForm, self).__init__(*args, **kwargs)
        # self.label_suffix = ""  # Removes : as label suffix

        for fname, f in self.fields.items():
            f.widget.attrs['class'] = 'form-control'
            self.fields[fname].widget.attrs['placeholder'] = self.fields[fname].label
            # f.widget.attrs['placeholders'] = self.fields[fname].label
            
    #! let user save phone number phone number patterns are very different 
    # def clean_phone_number(self):
        
    #     phone_number = self.cleaned_data['phone_number']
    #     if not phone_number.isdigit():
    #         raise ValidationError("Phone number should only contain digits.")
    #     # You can also add other validation rules for the phone number, such as length or format
    #     return phone_number

# class DoctorAddForm(ModelForm):
    
    
#     class Meta:
#         model = DoctorInfo
#         fields = ("full_name", 'department',"phone_number")
        

#     def __init__(self, *args, **kwargs):
#         super(DoctorAddForm, self).__init__(*args, **kwargs)
#         self.label_suffix = ""  # Removes : as label suffix


#         for fname, f in self.fields.items():
#             f.widget.attrs['class'] = 'form-control'


class DoctorAddForm(forms.ModelForm):
    class Meta:
        model = DoctorInfo
        fields = ("full_name", 'department', "phone_number")

        widgets = {
            'full_name': forms.TextInput(attrs= {'class': 'form-control' , 'required':'True'}),
            'department': forms.TextInput(attrs={'class': 'form-control'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control'}),
        }
        
    
        labels = {
            'full_name': 'Full Name',
            'department': 'Department',
            'phone_number': 'Phone Number',
        }

    def __init__(self, *args, **kwargs):
        super(DoctorAddForm, self).__init__(*args, **kwargs)
        self.label_suffix = ""  # Removes : as label suffix

        for fname, f in self.fields.items():
            f.widget.attrs['class'] = 'form-control'
