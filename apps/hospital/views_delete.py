from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from .models import HospitalInfo
from .forms import HospitalAddForm
from apps.hospital.models import Jedan

@login_required
def manage_hospital(request, pk=None):
    hospital = get_object_or_404(HospitalInfo, pk=pk) if pk else None
    
    if request.method == 'POST':
        form = HospitalAddForm(request.POST, instance=hospital)
        if form.is_valid():
            hospital = form.save(commit=False)
            hospital.jedan_id = request.POST.get('jedan_id')
            hospital.jedan_branch_id = request.POST.get('jedan_branch_id')
            hospital.save()
            return JsonResponse({'success': True})
        return JsonResponse({'success': False, 'error': form.errors})
    
    form = HospitalAddForm(instance=hospital)
    jedans = Jedan.objects.all()
    
    return render(request, 'hospital/manage_hospital.html', {
        'form': form,
        'hospital': hospital,
        'jedans': jedans,
    })