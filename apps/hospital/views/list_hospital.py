
from apps.hospital.models import HospitalInfo
from django.views.generic import ListView
from apps.authentication.decorators import staff_required
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.db.models import Count, Q
from datetime import datetime, timedelta

@method_decorator(login_required(login_url="/"), name='dispatch')
@method_decorator(staff_required(allowed_user_groups=["1", '5']), name='dispatch')
class HospitalListView(ListView):
    """
    Enhanced view for displaying hospital list with additional metrics and styling
    """
    model = HospitalInfo
    template_name = 'hospital/list_hospital.html'
    context_object_name = "hospital_list"
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Annotate with additional metrics
        return queryset.select_related(
            'jedan', 
            'jedan_branch', 
            'user',
            
        ).annotate(
            patient_count=Count('hospital_patientInfo'),
            # filter=Q(doctor_info__is_active=True)
            active_doctors=Count('doctor_info', )
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Basic navigation context
        context["active_menu"] = "hospital"
        context["page_title"] = "Hospital Management"
        
        # Statistics
        hospitals = self.get_queryset()
        context.update({
            'total_hospitals': hospitals.count(),
            'total_doctors': sum(h.number_of_doctors() for h in hospitals),
            'total_patients':10, # sum(h.number_of_patients() for h in hospitals if isinstance(h.number_of_patients(), int)),
            
            # Add status indicators for template
            'warning_threshold': 3,  # Hospitals with less than 3 doctors
            'danger_threshold': 0,   # Hospitals with no doctors
            
            # Last 30 days statistics
            'recently_added': hospitals.filter(
                created_at__gte=datetime.now() - timedelta(days=30)
            ).count(),
            
            # Group hospitals by jedan for better organization
            'jedan_groups': {
                jedan: hospitals.filter(jedan=jedan)
                for jedan in set(h.jedan for h in hospitals)
            }
        })
        
        return context



