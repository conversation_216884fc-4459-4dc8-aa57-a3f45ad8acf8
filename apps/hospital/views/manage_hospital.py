from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from ..models import HospitalInfo, Jedan, JedanBranch
from ..forms import HospitalAddForm
from apps.authentication.decorators import staff_required

import logging
from datetime import datetime


hospital_logger = logging.getLogger('hospital_logger')


@login_required(login_url="/")
@staff_required(allowed_user_groups=['1', '6',"7"])
def manage_hospital(request, hospital_id=None):
    hospital = get_object_or_404(
        HospitalInfo, id=hospital_id) if hospital_id else None
    is_new = hospital is None

    if request.method == 'POST':
        form = HospitalAddForm(request.POST, instance=hospital)
        if form.is_valid():
            hospital = form.save(commit=False)
            hospital.jedan_id = request.POST.get('jedan_id')
            hospital.jedan_branch_id = request.POST.get('jedan_branch_id')
            hospital.last_edited_by = request.user.user_employee.full_name
            hospital.added_by = request.user.username if is_new else hospital.added_by
            hospital.save()

            # 로그 기록
            action = "생성" if is_new else "수정"
            log_message = f"{datetime.now()} | [{action}] 사용자: {request.user.username} | 병원ID: {hospital.id}\n"
            for field in hospital._meta.fields:
                field_name = field.name
                field_value = getattr(hospital, field_name, '')
                log_message += f"    - {field_name}: {field_value}\n"
            hospital_logger.info(log_message)

            return JsonResponse({'success': True})
        return JsonResponse({'success': False, 'error': form.errors})

    form = HospitalAddForm(instance=hospital)
    jedan_branches = JedanBranch.objects.filter(
        jedan=hospital.jedan) if hospital else None

    context = {
        'form': form,
        'hospital': hospital,
        'jedans': Jedan.objects.all().order_by('jedan_name'),
        'jedan_branches': jedan_branches,
        "active_menu":"hospital"
    }

    return render(request, 'hospital/manage_hospital.html', context)







@login_required(login_url="/")
@require_http_methods(["GET"])
def get_jedan_branches(request):
    jedan_id = request.GET.get('jedan_id')
    branches = JedanBranch.objects.filter(jedan_id=jedan_id).order_by('jedan_branch_name')
    data = {
        'branches': [{'id': branch.id, 'name': branch.jedan_branch_name} for branch in branches]
    }
    return JsonResponse(data)
