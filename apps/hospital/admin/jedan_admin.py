from django.contrib import admin
from apps.hospital.models import <PERSON><PERSON>


@admin.register(Jedan)
class JedanAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'jedan_name', 'jedan_name_kor',
        'jedan_person', 'phone_number',
        'gbn_person', 'gbn_number', 'email',
        'number_of_branch', 'number_of_hospital', 'memo',
        'created_at', 'last_edited'
    )
    search_fields = ('jedan_name', 'jedan_name_kor',
                     'jedan_person', 'gbn_person')
    readonly_fields = ('added_by',  'last_edited_by','created_at', 'last_edited')
    
    list_filter = ('gbn_person',)
    
    list_display_links = ('jedan_name', 'jedan_name_kor', )

    fieldsets = (
        ("1. 재단 기본 정보", {
            'fields': ('jedan_name', 'jedan_name_kor', 'jedan_user', 'jedan_person')
        }),
        ("2. 연락처 및 위치", {
            'fields': ('phone_number', 'email', 'address')
        }),
        ("3. NIPT Genovenet / Genofind 담당자 정보 (있으면 등록, 없으면 생략)", {
            'fields': ('gbn_person', 'gbn_number', 'gm_person', 'gm_number')
        }),
        ("4. 메모 및 기록", {
            'fields': ('memo', 'added_by', 'last_edited_by', 'created_at', 'last_edited')
        }),
        
       
    )
    
    def save_model(self, request, obj, form, change):
        # Set last_edited_by only on update (optional: or also on creation)
        if hasattr(request.user, 'user_employee'):
            obj.last_edited_by += request.user.user_employee.full_name + ", "
        else:
            obj.last_edited_by += request.user.username  + ", " # fallback
        super().save_model(request, obj, form, change)
