from django.contrib import admin
from apps.hospital.models import HospitalInfo


@admin.register(HospitalInfo)
class HospitalInfoAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'hospital_name', 'jedan', 'jedan_branch',
        'marketing_manager', 'phone_number',
        'price_lite', 'price_std', 'extra_report',
        'created_at', 'last_edited_by',
        'get_contracted_services',  # custom method added
    )

    search_fields = (
        'hospital_name', 'hospital_name_eng',
        'jedan__jedan_name', 'jedan_branch__jedan_branch_name',
        'marketing_manager__user__username'
    )

    list_filter = ('jedan', 'jedan_branch',
                   'marketing_manager', 'extra_report')

    list_display_links = ('hospital_name',)

    autocomplete_fields = ['jedan_branch',
                           'jedan', "user", 'contracted_services']

    readonly_fields = ('added_by', 'last_edited_by',
                       'created_at', 'last_edited')

    fieldsets = (
        ("1. 재단 및 병원 기본 정보", {
            'fields': ('jedan', 'jedan_branch', 'user', 'hospital_name', 'hospital_name_eng')
        }),
        ("2. 주소 및 연락처", {
            'fields': ('country', 'do_address', 'si_address', 'last_address', 'address_full_eng', 'phone_number')
        }),
        ("3. 병원 정보", {
            # Added here
            'fields': ('pan_number', 'marketing_manager', 'key_person_info', 'memo_for_hospital', 'contracted_services')
        }),
        ("4. 가격 및 결과 코드", {
            'fields': (
                'price_lite', 'price_std', 'price_plus', 'price_twin',
                'code_lite', 'code_std', 'code_plus', 'code_twin', 'code_twin_std'
            )
        }),
        ("5. 보고 옵션", {
            'fields': ('extra_report', 'result_type', "gender_info")
        }),
        ("6. 기록 정보", {
            'fields': ('added_by', 'last_edited_by', 'created_at', 'last_edited')
        }),
    )

    def get_contracted_services(self, obj):
        return ", ".join([s.name for s in obj.contracted_services.all()])
    get_contracted_services.short_description = "계약 서비스"  # Custom column name

    def save_model(self, request, obj, form, change):
        if not obj.pk and not obj.added_by:
            obj.added_by = request.user.user_employee.full_name

        current_editor = request.user.user_employee.full_name
        if obj.last_edited_by:
            obj.last_edited_by += f', {current_editor}'
        else:
            obj.last_edited_by = current_editor

        super().save_model(request, obj, form, change)



