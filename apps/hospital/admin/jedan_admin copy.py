from django.contrib import admin
from apps.hospital.models import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HospitalInfo, DoctorInfo

from import_export import resources
from import_export.fields import Field
from import_export.admin import ExportActionMixin, ImportExportModelAdmin
# Register your models here.


class JedanResource(resources.ModelResource):
    """
    In case <PERSON><PERSON> there are only few datas so no need to worry
    """
    class Meta:
        model = Jedan
        #  ! use default if you want certain cols or exclude cols can use parameter
        #fields = ('id', 'name', 'branch', 'phone', 'email', 'address')
        #export_order = ('id', 'name', 'branch', 'phone', 'email', 'address')
        #exclude


class JedanAdmin(ExportActionMixin, admin.ModelAdmin):
    resource_class = JedanResource
    list_display = ('id', 'jedan_name','get_user_name',"last_login", 'number_of_branch',"number_of_hospital", 'phone_number','jedan_person', 'email')
    list_filter = ('jedan_name',)
    search_fields = ("jedan_name",)
    list_display_links = ['jedan_name',]





admin.site.register(<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>)

