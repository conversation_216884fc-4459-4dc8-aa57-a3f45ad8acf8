from django.contrib import admin
from apps.hospital.models import GC_Service


class GCServiceAdmin(admin.ModelAdmin):
    list_display = ('code', 'name')  # Fields to display in the list view
    # Enable search functionality for these fields
    search_fields = ('code', 'name')
    list_filter = ('code',)  # Add filter options for the 'code' field
    ordering = ('code',)  # Default ordering of records


admin.site.register(GC_Service, GCServiceAdmin)
