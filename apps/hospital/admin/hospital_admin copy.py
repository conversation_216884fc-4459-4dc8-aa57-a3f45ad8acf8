from django.contrib import admin
from apps.hospital.models import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HospitalInfo, Doctor<PERSON>n<PERSON>

from import_export import resources
from import_export.fields import Field
from import_export.admin import ExportActionMixin, ImportExportModelAdmin
# Register your models here.



#  !############### Hospital information
class HospitalInforResource(resources.ModelResource):
    """
    In case <PERSON><PERSON> there are only few datas so no need to worry
    """
    class Meta:
        model = HospitalInfo


class HospitalInfoAdmin(ExportActionMixin, admin.ModelAdmin):
    resource_class = HospitalInforResource
    list_display = ("hospital_name","user", 'jedan_branch',"number_of_doctors", "code_lite",
                    "code_std", "code_plus", "code_twin", "created_at","last_edited", "price_lite", "price_std", "price_plus", "last_address")
    
    
    list_filter = ('jedan_branch__jedan__jedan_name',)
    search_fields = ("jedan_branch__jedan_branch_name", "phone_number", "hospital_name"  )
    list_display_links = ['jedan_branch', "hospital_name"]

admin.site.register(HospitalInfo,  HospitalInfoAdmin)