from django.contrib import admin
from import_export import resources
from import_export.admin import ExportActionMixin, ImportExportModelAdmin

from apps.hospital.models import JedanBranch


class JedanBranchResource(resources.ModelResource):
    """
    Resource class for JedanBranch export/import.
    """
    class Meta:
        model = JedanBranch
        # You can customize fields here if needed
        # fields = ('id', 'jedan', 'jedan_branch_name', ...)
        # export_order = ('id', 'jedan', 'jedan_branch_name', ...)


@admin.register(JedanBranch)
class JedanBranchAdmin(ExportActionMixin, ImportExportModelAdmin):
    resource_class = JedanBranchResource

    list_display = (
        'id', 'jedan', 'jedan_branch_name',
        'phone_number', 'address',
        'branch_head', 'number_of_hospital',
        'is_active', 'note',
        'added_by', 'last_edited',
    )

    list_display_links = ('jedan_branch_name',)
    list_filter = ('jedan', 'is_active')
    search_fields = ('jedan_branch_name', 'jedan__jedan_name', 'branch_head',
                     'key_person_info', 'note')
    readonly_fields = ('created_at', 'last_edited')
    
    list_display_links = ('jedan_branch_name',)

    fieldsets = (
        ("1. 기본 정보", {
            'fields': ('jedan', 'jedan_branch_name', 'jedan_branch_user')
        }),
        ("2. 연락처 및 위치", {
            'fields': ('phone_number', 'address')
        }),
        ("3. 인물 정보", {
            'fields': ('branch_head', 'key_person_info')
        }),
        ("4. 관리 및 상태", {
            'fields': ('is_active', 'note')
        }),
        ("5. 등록 정보", {
            'fields': ('added_by', 'last_edited_by', 'created_at', 'last_edited')
        }),
    )

    def get_queryset(self, request):
        """
        Optionally customize queryset to annotate or filter.
        """
        return super().get_queryset(request)
