from django.contrib import admin
from apps.hospital.models import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HospitalInfo, DoctorIn<PERSON>

from import_export import resources
from import_export.fields import Field
from import_export.admin import ExportActionMixin, ImportExportModelAdmin
# Register your models here.




#  !###################  Jedan Branch 
class JedanBranchResource(resources.ModelResource):
    """
    In case <PERSON><PERSON> there are only few datas so no need to worry
    """
    class Meta:
        model = JedanBranch
        #  ! use default if you want certain cols or exclude cols can use parameter
        #fields = ('id', 'name', 'branch', 'phone', 'email', 'address')
        #export_order = ('id', 'name', 'branch', 'phone', 'email', 'address')
        #exclude


class JedanBranchAdmin(ExportActionMixin, admin.ModelAdmin):
    resource_class = JedanBranchResource
    list_display = ('id',"jedan", 'jedan_branch_name', "number_of_hospital", 'phone_number', "added_by", "last_edited")
    list_filter = ('jedan','jedan_branch_name')
    search_fields = ("jedan_branch_name",)
    list_display_links = ['jedan_branch_name', "jedan_branch_name"]





admin.site.register(JedanBranch, JedanBranchAdmin)

 