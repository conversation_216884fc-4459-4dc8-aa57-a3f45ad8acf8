# -*- encoding: utf-8 -*-
"""
Copyright (c) 2023 Theragen Genome Care ( k<PERSON>hna )
"""
from django.urls import path

from .views import (
    add_jedan, add_jedan_branch,
    add_hospital,
    add_doctor,
    add_doctors_form,
    htmx_jedan_branch,
    htmx_do_address,
    htmx_si_address,
    htmx_search_hospital)

from .views import (
        home,
        change_password,
        #view_that_returns_zipped_file,
        download_jpg_zip_jedan,
        result_pdf_down_jedan,
        terms_confirmed,  # , htmx_hospital, htmx_sample
    
        JedanListView,
        DoctorListView,
        HospitalListView,
        JedanBranchListView,
        selected_result_images_jedan,
        selected_pdf_down_jedan,
        release_date_range_jedan,
    )

from .views import jedan_branch_samples, hospital_samples

from .views import selected_result_images_jedan, selected_pdf_down_jedan

from .views import Jedan<PERSON>ist<PERSON>ie<PERSON>, Doctor<PERSON><PERSON><PERSON>ie<PERSON>, HospitalListView

from .views.manage_hospital import manage_hospital, get_jedan_branches



urlpatterns = [

    path('', home, name='hospital'),

    # path('htmx_sample/', htmx_sample, name='htmx_sample'),

    # path('htmx_hospital/', htmx_hospital, name='htmx_hospital'),

    #! While doctor add search hospital and select one
    path('htmx_search_hospital/', htmx_search_hospital,
         name="htmx_search_hospital"),

    path('terms_confirmed/', terms_confirmed, name='terms_confirmed'),

    #! For Jedan Dash Board Drop Down
    path('jedan_branch_samples', jedan_branch_samples,
         name="jedan_branch_samples"),

    path('hospital_samples', hospital_samples, name="hospital_samples"),


    path('change_password/', change_password, name='change_password'),

    #! While adding Hospital address , need to select Address
    path('htmx_do_address/', htmx_do_address, name="htmx_do_address"),
    path('htmx_si_address/', htmx_si_address, name="htmx_si_address"),

    #! Add Hospital Jedan
    path('add_jedan/', add_jedan, name="add_jedan"),

    #! while adding Hospital then Jedan Branch need to select under which Jedan
    path('htmx_jedan_branch/', htmx_jedan_branch, name="htmx_jedan_branch"),



    #! Add Jedan Brach
    path('add_jedan_branch/', add_jedan_branch, name="add_jedan_branch"),

    #! Add Hospital Jedan
    path('add_hospital/', add_hospital, name="add_hospital"),

    #! Add Hospital Jedan
    path('add_doctor/', add_doctor, name="add_doctor"),

    path('add_doctors_form/', add_doctors_form, name="add_doctors_form"),

    #! List From Database
    # path('list_jedan/', list_jedan, name="list_jedan"),
    path('list_jedan/', JedanListView.as_view(), name="list_jedan"),

    #! List From Database
    path('list_jedan_branch/', JedanBranchListView.as_view(),name="list_jedan_branch"),

    #! List selected jedan Jedan Branch , pass jedan pk
    path('list_jedan_branch/<int:parent_pk>',JedanBranchListView.as_view(), name="list_jedan_branch_with_parent"),

    #! List From Database
    # path('list_hospital/', list_hospital, name="list_hospital"),
    path('list_hospital/', HospitalListView.as_view(), name="list_hospital"),

    #! List From Database
    # path('list_doctor/', list_doctor, name="list_doctor"),
    path('list_doctor/', DoctorListView.as_view(), name="list_doctor"),

# Selected pdf download 
#     path('view_that_returns_zipped_file/', view_that_returns_zipped_file,name='view_that_returns_zipped_file'),
    
    path('selected_pdf_down_jedan/', selected_pdf_down_jedan,name='selected_pdf_down_jedan'),
    
    path('selected_result_images_jedan/', selected_result_images_jedan,name='selected_result_images_jedan'),

    path('download_jpg_zip_jedan/<int:pk>', download_jpg_zip_jedan, name="download_jpg_zip_jedan"),
    
    
    path('result_pdf_down_jedan/<int:pk>',result_pdf_down_jedan, name="result_pdf_down_jedan"),
    
    
    
    path('release_date_range_jedan', release_date_range_jedan, name="release_date_range_jedan"),

    path('manage_hospital/', manage_hospital, name='create_hospital'),
    path('manage_hospital/<int:hospital_id>/', manage_hospital, name='edit_hospital'),
    path('get_jedan_branches/', get_jedan_branches, name='get_jedan_branches'),
]
