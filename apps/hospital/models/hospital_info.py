from django.db import models
from apps.authentication.models import User

# Create your models here.
from django.db.models.deletion import PROTECT

from apps.hospital.models.jedan import Jedan
from apps.hospital.models.jedan_branch import JedanBranch

from apps.authentication.models import Employee
from apps.hospital.models.gc_service import GC_Service




class HospitalInfo(models.Model):

    """
    Hospital Information
    """
    RESULT_TYPE = (
        ("0",'0'),
        ("1",'1'),
        ("2",'2'),
    )
    
    STATUS_CHOICES = [
        ('active', '활성'),
        ('inactive', '비활성'),
        ('paused', '일시 중단'),
        ('terminated', '계약 종료'),
    ]
    
    
    #! Jedan Branch is not required can done directly with jedan branch , but using this can make dashboard fast and 
    
    jedan = models.ForeignKey(
        Jedan,
        verbose_name="의료재단명",
        on_delete=PROTECT,
        related_name="hospital_info"
    )
    
    
    jedan_branch = models.ForeignKey(
        JedanBranch,
        verbose_name="의료재단 지점명",
        on_delete=PROTECT,
        related_name="hospital_info"
    )

    user = models.OneToOneField(
        User,
        verbose_name="병원 계정 (User)",
        on_delete=models.PROTECT,
        related_name="hospital_name",
        null=True,
        blank=True
    )

    hospital_name = models.CharField(
        verbose_name="의료기관명",
        max_length=100
    )

    
    hospital_name_eng = models.CharField(
        verbose_name="Hospital Name (영문)",
        max_length=100,
        default='-',
        help_text="병원의 영문 이름을 입력하세요"
    )

    #!hospital_user = models.OneToOneField(User, verbose_name="사용자ID",on_delete=models.PROTECT ) 
    
    #phone_regex = RegexValidator(regex=r'^\+?1?\d{9,15}$', message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.")
    
    phone_number = models.CharField(
        verbose_name="전화번호",
        max_length=100,
        default='-'
    )
    
    country = models.CharField(
        verbose_name="국가명",
        max_length=30,
        default='대한민국'
    )
    
    
    
    
    do_address = models.CharField(
        verbose_name="시/도",
        max_length=20
    )
    
    
    si_address = models.CharField(
        verbose_name="시/군/구",
        max_length=20
    )


    last_address = models.CharField(
        verbose_name="상세 주소",
        max_length=100
    )
    
    address_full_eng = models.CharField(
        verbose_name="영문 주소",
        max_length=200,
        default='-'
    )
    
    contracted_services = models.ManyToManyField(
        GC_Service,
        blank=True,
        verbose_name="계약한 서비스",
        related_name="contracted_hospitals"  # Helps in reverse relation
    )
    
    is_hospital = models.BooleanField(
        default=True,
        verbose_name="병원 서비스 여부",
        help_text="대부분 병원에 해당됩니다. 병원과 관련 없는 경우에만 체크 해제하세요."
    )
    
    

    
    pan_number = models.CharField(
        max_length=20,
        verbose_name="사업자등록번호",
        blank=True,
        help_text="해당 병원의 사업자 등록번호 (선택 사항)"
    )
    
    
    #! Add Marketing Manager for this hospital 
    marketing_manager = models.ForeignKey(
        Employee,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='sales_manager',
        verbose_name="영업 담당자"
    )

    key_person_info = models.CharField(
        max_length=250,
        verbose_name="병원 내 핵심 인물 정보",
        blank=True,
        help_text="이름, 전화번호 등 자유롭게 입력 (여러 명 가능)"
    )


        
    #! after precision launching some hospital dont want to receive extra report more than 140 existence plus     
    extra_report = models.BooleanField(
        verbose_name="3Mbp 이상 CNV 결과 보고",
        default=True,
        help_text="Precision Genomom Plus 검사에서 3Mbp 이상 CNV 결과 보고 허용 여부"
    )
    
    # 새로운 필드: 성별 정보 강제 표시 여부
    gender_info = models.BooleanField(
        default=False,
        verbose_name='성별 정보 강제 표시',
        help_text='이 옵션을 선택하면 환자의 성별과 무관하게 성별 정보를 강제로 표시합니다.'
    )
    
    
    
    price_lite = models.FloatField(
        verbose_name="Lite 검사 가격",
        default=240900,
        help_text="Lite 검사의 세금 포함 가격"
    )

    price_std = models.FloatField(
        verbose_name="Standard 검사 가격",
        default=240900,
        help_text="Standard 검사의 세금 포함 가격"
    )

    price_plus = models.FloatField(
        verbose_name="Plus 검사 가격",
        default=236364,
        help_text="Plus 검사의 세금 포함 가격"
    )

    price_twin = models.FloatField(
        verbose_name="Twin 검사 가격",
        default=240900,
        help_text="Twin 검사의 세금 포함 가격"
    )

    code_lite = models.CharField(
        max_length=20,
        verbose_name="Lite 결과 코드",
        help_text="Lite PDF 결과 코드",
        default="",
        blank=True,
        null=True
    )

    code_std = models.CharField(
        max_length=20,
        verbose_name="Standard 결과 코드",
        help_text="Standard PDF 결과 코드",
        default="",
        blank=True,
        null=True
    )

    code_plus = models.CharField(
        max_length=20,
        verbose_name="Plus 결과 코드",
        help_text="Plus PDF 결과 코드",
        default="",
        blank=True,
        null=True
    )

    code_twin = models.CharField(
        max_length=20,
        verbose_name="Twin 결과 코드",
        help_text="Twin PDF 결과 코드",
        default="",
        blank=True,
        null=True
    )

    code_twin_std = models.CharField(
        max_length=20,
        verbose_name="Twin STD 결과 코드",
        help_text="Twin STD PDF 결과 코드",
        default="64097",
        blank=True,
        null=True
    )

    #! 성별 결과 보고 방법 병원 별 다릅니다 어디 +, - Male , Female ...
    result_type = models.CharField(
           verbose_name="PDF 타입 구분",
           choices=RESULT_TYPE,
           max_length=2,
           default="0",
           help_text="M/F 결과 템플릿에서 사용할 PDF 타입 설정"
       )
    
    
    
    is_new = models.BooleanField(
        default=False,
        verbose_name="신규 병원",
        help_text="처음 등록된 병원 여부"
    )

    is_contracted = models.BooleanField(
        default=False,
        verbose_name="계약 체결 여부",
        help_text="계약된 병원인지 여부"
    )

    contract_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="계약 체결일",
        help_text="병원과 계약한 날짜"
    )

    contract_expiry = models.DateField(
        null=True,
        blank=True,
        verbose_name="계약 만료일",
        help_text="계약 종료 예정일"
    )
    
    hospital_status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name="병원 상태",
        help_text="현재 병원의 운영 상태"
    ) 
    
    memo_for_hospital = models.TextField(
        verbose_name="병원 메모",
        blank=True,
        help_text="담당자가 입력하는 병원 관련 메모"
    )
    
    
    
       
    added_by = models.CharField(
        max_length=30,
        null=True,
        blank=True,
        verbose_name="최초 등록자"
    )

    last_edited = models.DateTimeField(
        null=True,
        blank=True,
        auto_now=True,
        verbose_name="최종 수정일"
    )

    last_edited_by = models.CharField(
        max_length=30,
        null=True,
        blank=True,
        verbose_name="최종 수정자"
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="생성일"
    )



    def __str__(self):
        #return self.hospital_name
        return f"{self.jedan.jedan_name}_{self.jedan_branch.jedan_branch_name}_{self.hospital_name}"

    def get_full_address(self):
        """
        Return the Full Address of the Hospital.
        """
        return f"{self.do_address} {self.si_address} {self.last_address}"

    def number_of_doctors(self):
        return self.doctor_info.count()
    
    def number_of_patients(self):
        try:
            return self.hospital_patientInfo.count()
        except:
            return 'Err.'


    class Meta:
        verbose_name = "3.병원 정보" # table header in admin sites
        verbose_name_plural = "3.병원 정보"
        
        ordering = ["jedan__jedan_name", "jedan_branch__jedan_branch_name", "hospital_name"]
        
    