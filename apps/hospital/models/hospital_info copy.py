from django.db import models
from apps.authentication.models import User

# Create your models here.
from django.db.models.deletion import PROTECT
from django.db import models

from django.core.validators import RegexValidator

#from simple_history.models import HistoricalRecords

from apps.hospital.models.jedan import <PERSON>an
from apps.hospital.models.jedan_branch import JedanBranch

from apps.authentication.models import Employee




class HospitalInfo(models.Model):

    """
    Hospital Information
    """
    RESULT_TYPE = (
        ("0",'0'),
        ("1",'1'),
        ("2",'2'),
    )
    #! Jedan Branch is not required can done directly with jedan branch , but using this can make dashboard fast and 
    jedan = models.ForeignKey(Jedan, verbose_name="의료재단 명", on_delete=PROTECT, related_name="hospital_info")    
    
    
    
    
    jedan_branch = models.ForeignKey(JedanBranch,verbose_name="의료재단 지점 명", on_delete=PROTECT, related_name="hospital_info")    
    user = models.OneToOneField(User, verbose_name="사용자ID", on_delete=models.PROTECT, related_name="hospital_name", null=True, blank=True )   
    
    hospital_name = models.CharField(verbose_name="의료기관 명", max_length=100)
    hospital_name_eng = models.CharField(verbose_name="Hosptital Name", max_length=100, default = '-' , help_text = "Hospital name in English")
    
    #!hospital_user = models.OneToOneField(User, verbose_name="사용자ID",on_delete=models.PROTECT ) 
    
    #phone_regex = RegexValidator(regex=r'^\+?1?\d{9,15}$', message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.")
    
    phone_number = models.CharField('전화 번호', max_length=100, default='-')
    
    country = models.CharField(verbose_name="Country", max_length=30, default='-')
    do_address = models.CharField(verbose_name="시/도 주소", max_length=20)
    si_address = models.CharField(verbose_name="시/군/구 주소", max_length=20)
    last_address = models.CharField(verbose_name='남는 주소', max_length=100)

    address_full_eng = models.CharField(verbose_name ="영문 주소", max_length=200, default="-")
    
    pan_number = models.CharField(
        max_length=20,
        verbose_name='사업자 등록 번호',
        blank=True,
        help_text='있으면 장소(병원)의 사업자번호를 입력해주세요'
    )
    #! Add Marketing Manager for this hospital 
    marketing_manager = models.ForeignKey(
        Employee,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='sales_manager',
        verbose_name='담당자',
    )
    
    key_person_info = models.CharField(
        max_length=250,
        verbose_name="핵심 인물 정보",
        blank=True,
        help_text="이름, 전화번호 등 여러 사람을 자유롭게 입력하세요"
    )

    memo_for_hospital = models.TextField(
        verbose_name="병원 메모",
        blank=True,
        help_text="영업 담당자 용 병원에 대한 메모"
    )
        
    #! after precision launching some hospital dont want to receive extra report more than 140 existence plus     
    extra_report = models.BooleanField(verbose_name="Extra reporting" , default= True , help_text="Report or not on size greater than  3Mbp 검출 허용/거부 병원 ")
    

    price_lite = models.FloatField( verbose_name='Lite Price', default=240900, help_text="Lite Tax included price ")
    price_std = models.FloatField(verbose_name='STD Price', default=240900 , help_text= "Std Tax included price")
    price_plus = models.FloatField(verbose_name='PLUS Price', default=236364 , help_text= "Plus Tax included price ")
    price_twin = models.FloatField(verbose_name='Twin Price', default=240900, help_text="Plus Tax included price")
    
    #! Each Hospital has different Result Code
    code_lite = models.CharField(max_length=20, verbose_name="Lite Code", help_text= "result code for pdf for lite ", default="", blank=True, null=True )
    code_std = models.CharField(max_length=20, verbose_name="Std Code", help_text= "result code for pdf for Std ", default="", blank=True, null=True )
    code_plus = models.CharField(max_length=20, verbose_name="Plus Code", help_text= "result code for pdf for Plus ", default="", blank=True, null=True )
    code_twin = models.CharField(max_length=20, verbose_name="Twin Code", help_text= "result code for pdf for Plus ", default="", blank=True, null=True )
    code_twin_std = models.CharField(max_length=20, verbose_name="Twin Code",
                                 help_text="result code for pdf for Plus ", default="64097", blank=True, null=True)
    
    result_type = models.CharField(verbose_name="pdf type", choices=RESULT_TYPE, max_length=2, default="0", help_text="this will use in Sex result template M/F")
    
    added_by = models.CharField(max_length=30, null=True, blank=True)
    last_edited = models.DateTimeField(null=True, blank=True, auto_now=True,)
    last_edited_by = models.CharField(max_length=30, null=True, blank=True)   
    created_at = models.DateTimeField(auto_now_add=True)
    
    
    #history = HistoricalRecords()

    # objects

    def __str__(self):
        #return self.hospital_name
        return f"{self.jedan.jedan_name}_{self.jedan_branch.jedan_branch_name}_{self.hospital_name}"

    def get_full_address(self):
        """
        Return the Full Address of the Hospital.
        """
        return f"{self.do_address} {self.si_address} {self.last_address}"

    def number_of_doctors(self):
        return self.doctor_info.count()
    
    def number_of_patients(self):
        try:
            return self.hospital_patientInfo.count()
        except:
            return 'Err.'


    class Meta:
        verbose_name = "3.병원 정보" # table header in admin sites
        verbose_name_plural = "3.병원 정보"
        
        ordering = ["jedan__jedan_name", "jedan_branch__jedan_branch_name", "hospital_name"]
        
        

