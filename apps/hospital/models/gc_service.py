from django.db import models


class GC_Service(models.Model):
    SERVICE_CHOICES = [
        ('NIPT', 'NIPT'),
        ('PGT', 'PGT'),
        ('ORA', 'ORA'),
        ('GBN', 'GBN'),
        ('GENOFIND', 'GENOFIND'),
    ]

    code = models.CharField(
        max_length=20,
        choices=SERVICE_CHOICES,
        unique=True,
        verbose_name="서비스 코드"
    )

    name = models.CharField(
        max_length=100,
        verbose_name="서비스 이름"
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="생성일"
    )

    last_updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name="마지막 수정일"
    )
    
    

    def __str__(self):
        return self.name
