from django.db import models
from apps.authentication.models import User

# Create your models here.
from django.db.models.deletion import PROTECT
from django.db import models

from django.core.validators import RegexValidator

#from simple_history.models import HistoricalRecords

from apps.hospital.models.jedan import Jedan

class JedanBranch(models.Model) :
    """
    jedan branch is branch Under Jedan.
    """
    # user
    jedan = models.ForeignKey(Jedan,verbose_name="의료재단 명", on_delete=PROTECT, related_name="jedan_branch")
    
    
    
    
    # Branch name
    jedan_branch_name = models.CharField(
        max_length=100,
        verbose_name="지점명",
        help_text="의료재단의 지점명을 입력하세요. (예: 서울 강남점)"
    )
    

    #! 2025-02-05 백인종 이사님 요청 이매일 & 양진화 과장님 기안 올렸음 
    
    # Assigned branch user (e.g., manager or representative user)
    jedan_branch_user = models.OneToOneField(
        User,
        verbose_name="지점 담당자 (사용자)",
        on_delete=PROTECT,
        related_name="assigned_jedan_branch",  # jedan_branch_name
        null=True,
        blank=True,
        help_text="지점 담당자로 지정된 사용자 계정 (선택 사항)"
    )
    
    
    # Contact number for the branch
    phone_number = models.CharField(
        verbose_name="지점 대표 전화번호",
        max_length=15,
        null=True,
        blank=True,
        default='-',
        help_text="지점 대표 번호를 입력하세요 (예: 02-1234-5678)"
    )

    # Full address
    address = models.CharField(
        verbose_name="지점 전체 주소",
        max_length=200,
        null=True,
        blank=True,
        default='-',
        help_text="지점의 전체 주소를 입력하세요"
    )
    
    # Branch head information (free-text)
    branch_head = models.CharField(
        max_length=100,
        verbose_name="지점장 정보",
        blank=True,
        default='-',
        help_text="지점장의 이름, 전화번호, 이메일 등을 자유롭게 입력하세요"
    )


    # Key person(s) at the branch (free-text)
    key_person_info = models.CharField(
        max_length=250,
        verbose_name="중요 인물 정보",
        blank=True,
        default='-',
        help_text="지점의 핵심 인물 (이름, 연락처 등)을 입력하세요"
    )

    note = models.TextField(
        verbose_name="비고",
        null=True,
        blank=True,
        help_text="지점 관련 특이사항이나 메모를 입력하세요"
    )


    is_active = models.BooleanField(
        default=True,
        verbose_name="활성 상태",
        help_text="비활성화된 지점은 시스템 내 검색에 제외될 수 있습니다"
    )

    # phone_regex = RegexValidator(regex=r'^\+?1?\d{9,15}$', message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.")
    

    # Meta and audit fields
    added_by = models.CharField(
        max_length=30,
        null=True,
        blank=True,
        verbose_name="최초 등록자"
    )

    last_edited = models.DateTimeField(
        auto_now=True,
        null=True,
        blank=True,
        verbose_name="최종 수정 일시"
    )

    last_edited_by = models.CharField(
        max_length=30,
        null=True,
        blank=True,
        verbose_name="최종 수정자"
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="생성일시"
    )

    #history = HistoricalRecords()

    def __str__(self):
        return f"{self.jedan.jedan_name}_{self.jedan_branch_name}"
    
    def number_of_hospital(self):
        """
        Returns the number of hospitals linked to this branch.
        Assumes a reverse relation named 'hospital_info'.
        """
        return self.hospital_info.count()

    class Meta:
        verbose_name = "2.의료재단 지점 정보" # table header in admin sites
        verbose_name_plural = "2.의료재단 지점 정보"
        
        unique_together = [["jedan", "jedan_branch_name"]]
        
        ordering = ["jedan__jedan_name", "jedan_branch_name"]

# class HospitalManager(models.Manager):

#     def get_doctors(self, hospital_id):
#         doctors = DoctorInfo.objects.filter(hospital_id)
#         return doctors
