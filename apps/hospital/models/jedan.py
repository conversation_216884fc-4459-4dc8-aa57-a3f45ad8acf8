from django.db import models
from apps.authentication.models import User

# Create your models here.
from django.db.models.deletion import PROTECT
from django.db import models

from django.core.validators import RegexValidator

#from simple_history.models import HistoricalRecords


# from tgc.authentication.models import User

class Jedan(models.Model):

    """
    Models For Jedan. <PERSON><PERSON> is head of Hospital.hospital > jedan_branch > jedan
    """
    # each jedan has One User
    #phone_regex = RegexValidator(regex = r'^\+?1?\d{9,15}$', message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.")

    
    jedan_name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name="의료 재단명 (약칭)",
        help_text="예: 서울의료재단, 대전의료원"
    )
    
    jedan_name_kor = models.CharField(
        max_length=100,
        verbose_name="의료 재단명 (전체)",
        default='-',
        help_text="의료 재단의 전체 한글 명칭을 입력하세요"
    )
    
    
    jedan_user = models.OneToOneField(
        User,
        verbose_name="재단 사용자 계정",
        on_delete=models.PROTECT,
        related_name="jedan_name",
        help_text="의료재단과 연결된 사용자 계정"
    )
    
    jedan_person = models.CharField(
        max_length=200,
        verbose_name="재단 담당자명",
        null=True,
        blank=True,
        help_text="재단의 담당자 이름 (선택 사항)"
    )
    
    phone_number = models.CharField(
        verbose_name="재단 전화번호",
        max_length=200,
        help_text="재단의 공식 전화번호 여러개 입력 가능 , 쉼표로 구분"
    )

    #! later require then create gbn depart and person also 
    
    gm_person = models.CharField(
           verbose_name="GM 담당자명",
           max_length=40,
           default='GM 담당자',
           help_text="재단에서 NIPT(GM) 서비스 담당자 이름"
       )

    gm_number = models.CharField(
        verbose_name="GM 연락처",
        max_length=15,
        default='GM 담당자 번호',
        help_text="재단에서 NIPT(GM) 서비스 담당자 전화번호"
    )
    
    
    
    gbn_person = models.CharField(
        verbose_name="GBN/GFN 담당자명",
        max_length=100,
        default='GBN/GFN 담당자',
        help_text="재단에서 GBN/GFN 서비스  담당자 이름"
    )

    gbn_number = models.CharField(
        verbose_name="GBN 연락처",
        max_length=50,
        default='GBN 담당자 번호',
        help_text="재단에서 GBN/GFN 서비스 담당자의 전화번호"
    )
    
    
    
    
    
    
    email = models.EmailField(
        verbose_name="이메일",
        max_length=50,
        help_text="재단의 대표 이메일 주소"
    )

    address = models.CharField(
        verbose_name="재단 주소",
        max_length=100,
        help_text="재단의 전체 주소 (도로명 또는 지번 포함)"
    )
    
    memo = models.TextField(
        verbose_name="메모",
        null=True,
        blank=True,
        help_text="의료재단 관련 특이사항이나 참고 메모를 자유롭게 입력하세요"
    )
    
    
    added_by = models.CharField(
        max_length=30,
        null=True,
        blank=True,
        verbose_name="등록자"
    )

    last_edited = models.DateTimeField(
        null=True,
        blank=True,
        auto_now=True,
        verbose_name="최종 수정일"
    )

    last_edited_by = models.CharField(
        max_length=250,
        null=True,
        blank=True,
        verbose_name="최종 수정자"
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="생성일"
    )

    #history = HistoricalRecords()
    def __str__(self) :
        return self.jedan_name
    
    def get_user_name (self):
        return self.jedan_user.username
    
    def last_login (self):
        return self.jedan_user.last_login
    
    def number_of_branch(self):
        return self.jedan_branch.count()
    
    def number_of_hospital(self):
        """
        Counts the total number of hospitals under this Jedan (across all branches)
        
        """
        #print(dir(self.jedan_branch))
        all_branches = self.jedan_branch.all()
        sum_ = 0
        for branches in all_branches:
            count_in_branch = branches.hospital_info.all().count()
            sum_ += count_in_branch
            
        return sum_

    class Meta:
        verbose_name = "1.의료재단 정보"
        verbose_name_plural = "1.의료재단 정보"
