from django.db import models
from apps.authentication.models import User

# Create your models here.
from django.db.models.deletion import PROTECT
from django.db import models

from django.core.validators import RegexValidator

#from simple_history.models import HistoricalRecords


# from tgc.authentication.models import User

class <PERSON>an(models.Model):

    """
    Models For Jedan. <PERSON><PERSON> is head of Hospital.hospital > jedan_branch > jedan
    """
    # each jedan has One User
    #phone_regex = RegexValidator(regex = r'^\+?1?\d{9,15}$', message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.")

    jedan_name = models.CharField(max_length=100, unique=True, verbose_name="의료 재단명")
    
    jedan_name_kor = models.CharField(max_length=100,  verbose_name="의료 재단명 전체", default= '-')
    
    jedan_user = models.OneToOneField(User, verbose_name="사용자ID",on_delete=models.PROTECT, related_name="jedan_name" )
    jedan_person= models.CharField(max_length=100, verbose_name="의료재단 담당자",null=True, blank=True)

    phone_number = models.CharField(verbose_name="전화번호", max_length=15, ) # +9779845312460
    
    #! later require then create gbn depart and person also 
    gbn_person = models.CharField(verbose_name="GBN 담당자", max_length=40,
                                  help_text="GBN 담당자", default='GBN 담당자')  # +9779845312460
    
    gbn_number = models.CharField(verbose_name="GBN 전화번호", max_length=15,
                                  help_text="GBN 담당자 번호", default='GBN 담당자 번호')  # +9779845312460
    
    email = models.EmailField(verbose_name="Email", max_length= 50 )

    address  = models.CharField(verbose_name="전체 주소", max_length=100)    
    added_by = models.CharField(max_length=30, null = True, blank=True)
    last_edited = models.DateTimeField(null=True, blank=True, auto_now=True,)
    last_edited_by = models.CharField(max_length=30, null = True, blank=True )
    created_at = models.DateTimeField(auto_now_add=True)
    
    #history = HistoricalRecords()
    def __str__(self) :
        return self.jedan_name
    
    def get_user_name (self):
        return self.jedan_user.username
    
    def last_login (self):
        return self.jedan_user.last_login
    
    def number_of_branch(self):
        return self.jedan_branch.count()
    
    def number_of_hospital(self):
        """
        Count the number of hospital exist in same Jedan 
        
        """
        #print(dir(self.jedan_branch))
        all_branches = self.jedan_branch.all()
        sum_ = 0
        for branches in all_branches:
            count_in_branch = branches.hospital_info.all().count()
            sum_ += count_in_branch
            
        return sum_

    class Meta:
        verbose_name = "1.의료재단 정보"
        verbose_name_plural = "1.의료재단 정보"
