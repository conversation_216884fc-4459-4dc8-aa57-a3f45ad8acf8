{% extends 'layouts/base.html' %}
{% block content %}
<div class="">
    <div class="card">


        <!-- Header -->
        <div class="header pb-2">
            <div class="container-fluid">
                <div class="header-body">
                    <div class="row align-items-center py-4">
                        <div class="col-lg-6 col-7">
                            <h6 class="h2 d-inline-block mb-0">{% if hospital %} 수정 {% else %} 추가 {% endif %} 병원 정보 </h6>
                            <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                                <ol class="breadcrumb breadcrumb-links">
                                    <li class="breadcrumb-item"><a href="#"><i class="fas fa-home"></i></a></li>
                                    <li class="breadcrumb-item"><a href="#"> 재단 및 기관 관리 </a></li>
                                    <li class="breadcrumb-item active" aria-current="page">의료기관 목록(Hospital List)</li>
                                </ol>
                            </nav>
                        </div>
                        <div class="col-lg-6 col-5 text-right">
                            <a href="{% url 'add_hospital' %}" class="btn  btn-danger"> 병원 추가 with User ID  </a>
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>



        
        <div class="card-header">
            <h2 class="text-success"></h2>
        </div>

        


        <div class="card-body">

            <!-- 사용자 정의 알림창 -->
            <div class="custom-alert">
                <h3 style="color: red;">📌 병원 등록 시 유의사항</h3>
                <ul>
                    <li><strong>동일 병원 중복 등록을 피해주세요.</strong> 이미 등록된 병원은 다시 등록하지 않도록 주의해주세요.</li>
                    <li>같은 병원이더라도 <strong>재단(Jedan)이 다른 경우</strong>에는 기존 병원의 재단 정보를 업데이트할 수 있습니다. 반드시 재단과 지점을 정확히 구분하여 입력해주세요.</li>
                    <li><strong>사업자등록번호</strong>를 정확히 입력해야 타 서비스와의 자동 연동이 원활하게 이루어집니다. 사업자 번호 모루시면 Pass </li>
                    <li>병원명은 <strong>검사 결과지에 그대로 표기</strong>되므로, 오타 없이 정확하게 입력해주세요.</li>
                </ul>
            </div>


<hr>


            <form id="hospitalForm" method="POST">
                {% csrf_token %}
                {{ form.non_field_errors }}

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <h5 class="text-left font-weight-bold">의료재단명</h5>
                            <select name="jedan_id" id="jedan_id" class="form-control" required>
                                <option value="">재단을 선택하세요</option>
                                {% for jedan in jedans %}
                                <option value="{{ jedan.id }}" {% if hospital.jedan_id == jedan.id %}selected{% endif %}>
                                    {{ jedan.jedan_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <h5 class="text-left font-weight-bold">의료재단 지점명</h5>
                            <select name="jedan_branch_id" id="jedan_branch_id" class="form-control" required>
                                <option value="">지점을 선택하세요</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    {% for field in form %}
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="{{ field.id_for_label }}" class="form-label">
                                <strong>{{ field.label }}</strong>
                                {% if field.field.required %}<span class="text-danger">*</span>{% endif %}
                            </label>
                            
                            {{ field }}
                            
                            {% if field.help_text %}
                            <small class="form-text text-muted">{{ field.help_text }}</small>
                            {% endif %}
                            
                            {% if field.errors %}
                            <div class="text-danger">
                                {% for error in field.errors %}
                                <small>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save
                        </button>
                        <a href="{% url 'list_hospital' %}" class="btn btn-secondary">Cancel</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Initialize jedan_branch_id if hospital exists
    {% if hospital %}
        $.ajax({
            url: "{% url 'get_jedan_branches' %}",
            data: {
                'jedan_id': $('#jedan_id').val()
            },
            dataType: 'json',
            success: function(data) {
                let options = '<option value="">지점을 선택하세요</option>';
                data.branches.forEach(function(branch) {
                    options += `<option value="${branch.id}" 
                        ${branch.id == {{ hospital.jedan_branch_id|default:'null' }} ? 'selected' : ''}>
                        ${branch.name}
                    </option>`;
                });
                $('#jedan_branch_id').html(options);
            }
        });
    {% endif %}

    $('#jedan_id').change(function() {
        const jedanId = $(this).val();
        if (jedanId) {
            $.ajax({
                url: "{% url 'get_jedan_branches' %}",
                data: {
                    'jedan_id': jedanId
                },
                dataType: 'json',
                success: function(data) {
                    let options = '<option value="">지점을 선택하세요</option>';
                    data.branches.forEach(function(branch) {
                        options += `<option value="${branch.id}">${branch.name}</option>`;
                    });
                    $('#jedan_branch_id').html(options);
                }
            });
        } else {
            $('#jedan_branch_id').html('<option value="">지점을 선택하세요</option>');
        }
    });

    $('#hospitalForm').submit(function(e) {
        e.preventDefault();
        const formData = $(this).serialize();
        
        $.ajax({
            type: 'POST',
            url: window.location.href,
            data: formData,
            success: function(response) {
                if (response.success) {
                    window.location.href = "{% url 'list_hospital' %}";
                } else {
                    alert(response.error);
                }
            },
            error: function(xhr) {
                alert('Error saving hospital information');
            }
        });
    });
});
</script>
{% endblock %}
