{% extends "layouts/base.html" %}

{% block title %} 의료기관 목록 {% endblock %}

<!-- Specific Page CSS goes HERE  -->
{% block stylesheets %}
<style>
    .action-buttons .btn {
        margin-right: 5px;
    }
    .hospital-name {
        font-weight: 600;
        color: #5e72e4;
    }
    .badge-count {
        font-size: 0.9rem;
        padding: 0.35rem 0.5rem;
    }
    .table th {
        font-size: 0.85rem;
        text-transform: uppercase;
        font-weight: 600;
        color: #8898aa;
    }
    .status-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
    }
    .status-active {
        background-color: #2dce89;
    }
    .status-warning {
        background-color: #fb6340;
    }
    .status-danger {
        background-color: #f5365c;
    }
</style>
{% endblock stylesheets %}

{% block content %}

<!-- Header -->
<div class="header pb-6">
    <div class="container-fluid">
        <div class="header-body">
            <div class="row align-items-center py-4">
                <div class="col-lg-6 col-7">
                    <h6 class="h2 d-inline-block mb-0">의료기관 목록(Hospital List)</h6>
                    <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                        <ol class="breadcrumb breadcrumb-links">
                            <li class="breadcrumb-item"><a href="#"><i class="fas fa-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="#"> 재단 및 기관 관리 </a></li>
                            <li class="breadcrumb-item active" aria-current="page">의료기관 목록(Hospital List)</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-lg-6 col-5 text-right">
                    <a href="{% url 'add_hospital' %}" class="btn btn-sm btn-primary"> 
                        <i class="fas fa-plus"></i> 병원 추가 
                    </a>
                    <button type="button" class="btn btn-sm btn-neutral" data-toggle="modal" data-target="#filterModal"> 
                        <i class="fas fa-filter"></i> 필터 
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Page content -->

<div class="container-fluid mt--6">

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card card-stats">
                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0">Total Hospitals</h5>
                            <span class="h2 font-weight-bold mb-0">{{ total_hospitals }}</span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-gradient-red text-white rounded-circle shadow">
                                <i class="ni ni-building"></i>
                            </div>
                        </div>
                    </div>
                    <p class="mt-3 mb-0 text-sm">
                        <span class="text-success mr-2">
                            <i class="fa fa-arrow-up"></i> {{ recently_added }}
                        </span>
                        <span class="text-nowrap">Last 30 days</span>
                    </p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card card-stats">
                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0">Total Doctors</h5>
                            <span class="h2 font-weight-bold mb-0">{{ total_doctors }}</span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-gradient-orange text-white rounded-circle shadow">
                                <i class="ni ni-single-02"></i>
                            </div>
                        </div>
                    </div>
                    <p class="mt-3 mb-0 text-sm">
                        <span class="text-success mr-2">
                            <i class="fa fa-arrow-up"></i> {{ recently_added_doctors }}
                        </span>
                        <span class="text-nowrap">Last 30 days</span>
                    </p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card card-stats">
                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0">Total Patients</h5>
                            <span class="h2 font-weight-bold mb-0">{{ total_patients }}</span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-gradient-green text-white rounded-circle shadow">
                                <i class="ni ni-active-40"></i>
                            </div>
                        </div>
                    </div>
                    <p class="mt-3 mb-0 text-sm">
                        <span class="text-success mr-2">
                            <i class="fa fa-arrow-up"></i> {{ recently_added_patients }}
                        </span>
                        <span class="text-nowrap">Last 30 days</span>
                    </p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card card-stats">
                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0">Active Hospitals</h5>
                            <span class="h2 font-weight-bold mb-0">{{ active_hospitals }}</span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-gradient-info text-white rounded-circle shadow">
                                <i class="ni ni-chart-bar-32"></i>
                            </div>
                        </div>
                    </div>
                    <p class="mt-3 mb-0 text-sm">
                        <span class="text-success mr-2">
                            <i class="fa fa-arrow-up"></i> {{ active_percentage }}%
                        </span>
                        <span class="text-nowrap">Active rate</span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Table -->
    <div class="card shadow">
        <div class="card-header border-0">
            <h3 class="mb-0">의료기관 목록</h3>
        </div>
        <div class="table-responsive py-4">
            <table class="table table-flush" id="datatable-basic">
                <thead class="thead-light">
                    <tr>
                        <th>Actions</th>
                        <th>의료재단</th>
                        <th>의료재단 지점</th>
                        <th>의료기관명</th>
                        <th> 영업담당자 </th>
                        <th>CS건수 </th>
                        <th> 방문건수 </th>


                        
                        <th>전화번호</th>
                        
                        <th>의사 수</th>
                        <th>서비스 건수</th>
                        <th>주소</th>
                        
                        <th> 등록 정보 </th>
                        <th>수정 정보</th>
                    </tr>
                </thead>
                <tbody>
                    {% for hospital in hospital_list %}
                    <tr class="{% if hospital.number_of_doctors == 0 %}table-danger{% elif hospital.number_of_doctors <= warning_threshold %}table-warning{% endif %}">
                        <td>
                            <div class="action-buttons">
                                <a href="{% url 'edit_hospital' hospital.id %}" class="btn btn-sm btn-info" title="Edit Hospital">
                                    <i class="fas fa-pencil-alt"></i>
                                </a>
                                <a href="{% url 'hospital_report_detail' hospital.id %}" class="btn btn-sm btn-primary" title="View Hospital Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </td>
                        <td>{{ hospital.jedan_branch.jedan.jedan_name }}</td>
                        <td>{{ hospital.jedan_branch.jedan_branch_name }}</td>
                        <td>
                            <a href="{% url 'hospital_report_detail' hospital.id %}" class="hospital-name" title=" Hospital Login User {{ hospital.user }} ">
                                {{ hospital.hospital_name }}
                            </a>
                        </td>
                        <td>{{ hospital.marketing_manager|default:"-" }}</td>

                        <td>{{ hospital.hospital_inquiries.count }}</td>

                        <td>{{ hospital.marketing_visits.count }}</td>


                       
                        <td>{{ hospital.phone_number }}</td>
                        
                        <td>
                            <span class="badge badge-pill badge-count bg-{% if hospital.number_of_doctors == 0 %}danger{% elif hospital.number_of_doctors <= warning_threshold %}warning{% else %}success{% endif %}">
                                {{ hospital.number_of_doctors }}
                            </span>
                        </td>
                        <td>

                            <span class="mr-2">{{ hospital.number_of_patients }}</span>

<!--
                            <div class="d-flex align-items-center">
                                <span class="mr-2">{{ hospital.number_of_patients }}</span>
                                 
                                {% if hospital.number_of_patients > 0 %}
                                <div class="progress-wrapper w-75">
                                    <div class="progress-info">
                                        <div class="progress-percentage">
                                            <span>{{ hospital.patient_percentage }}%</span>
                                        </div>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar bg-primary" role="progressbar" aria-valuenow="{{ hospital.patient_percentage }}" aria-valuemin="0" aria-valuemax="100" style="width: {{ hospital.patient_percentage }}%;"></div>
                                    </div>
                                </div>
                                {% endif %} 
                            </div>
                            -->
                            
                        </td>
                        <td>{{ hospital.get_full_address }}</td>
                       
                        <td>
                            <small class="text-muted">
                                {{ hospital.added_by }}
                                <br>
                                {{ hospital.created_at|date:"Y-m-d" }}
                            </small>
                        </td>

                        <td>
                            <span class="text-muted">
                                {{ hospital.last_edited|date:"Y-m-d" }}
                                <br>
                                {{ hospital.last_edited_by }}
                            </span>
                        </td>



                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

</div>

<!-- Filter Modal -->
<div class="modal fade" id="filterModal" tabindex="-1" role="dialog" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filterModalLabel">Filter Hospitals</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <label for="jedan-filter">의료재단</label>
                        <select class="form-control" id="jedan-filter">
                            <option value="">All</option>
                            <!-- Add options dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="branch-filter">의료재단 지점</label>
                        <select class="form-control" id="branch-filter">
                            <option value="">All</option>
                            <!-- Add options dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="status-filter">Status</label>
                        <select class="form-control" id="status-filter">
                            <option value="">All</option>
                            <option value="active">Active</option>
                            <option value="warning">Warning</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Apply Filters</button>
            </div>
        </div>
    </div>
</div>

{% include 'includes/footer.html' %}

{% endblock content %}

<!-- Specific Page JS goes HERE  -->
{% block javascripts %}

<!-- Optional JS -->
<script src="/static/assets/vendor/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="/static/assets/vendor/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="/static/assets/vendor/datatables.net-buttons/js/dataTables.buttons.min.js"></script>
<script src="/static/assets/vendor/datatables.net-buttons-bs4/js/buttons.bootstrap4.min.js"></script>
<script src="/static/assets/vendor/datatables.net-buttons/js/buttons.html5.min.js"></script>
<script src="/static/assets/vendor/datatables.net-buttons/js/buttons.flash.min.js"></script>
<script src="/static/assets/vendor/datatables.net-buttons/js/buttons.print.min.js"></script>
<script src="/static/assets/vendor/datatables.net-select/js/dataTables.select.min.js"></script>

<script>
    $(document).ready(function() {
        $('#datatable-basic').DataTable({
            language: {
                paginate: {
                    previous: "<i class='fas fa-angle-left'>",
                    next: "<i class='fas fa-angle-right'>"
                }
            },
            "order": [[ 3, "asc" ]],  // Sort by hospital name by default
            "pageLength": 200,  // Show 25 entries per page
            "lengthMenu": [[10, 25, 50,200, -1], [10, 25, 50,200, "All"]]
        });
    });
</script>

{% endblock javascripts %}
