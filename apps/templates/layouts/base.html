<!--
=========================================================
* Argon Dashboard PRO - v1.2.0
=========================================================
* Product Page: https://www.creative-tim.com/product/argon-dashboard-pro
* Copyright  Creative Tim (http://www.creative-tim.com)
* Coded by www.creative-tim.com
=========================================================
* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
-->
<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description" content="LIMS for Genomecare">
  <meta name="author" content="지놈케어">

  <title>
    지놈케어{% block title %}{% endblock %} | AIM
  </title>

  <!-- Favicon -->
  <link rel="icon" href="/static/assets/img/brand/favicon.ico" type="image/png">

  <!-- Fonts -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Nanum+Gothic:wght@400;700&display=swap" rel="stylesheet">


  <!-- Icons  base -->
  <link rel="stylesheet" href="/static/assets/vendor/nucleo/css/nucleo.css" type="text/css">

  <link rel="stylesheet" href="/static/assets/vendor/@fortawesome/fontawesome-free/css/all.min.css">

  <!-- Include Select2 CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">

  

  <!-- Specific Page CSS goes HERE  -->  
  {% block stylesheets %}{% endblock stylesheets %}

  <!-- Argon CSS -->
  <link rel="stylesheet" href="/static/assets/css/argon.css?v=1.2.0" type="text/css">


</head>
<body class="">
  
  {% include 'includes/sidebar.html' %}  
  
  <!-- Main content -->
  <div class="main-content" id="panel">

    {% include 'includes/navigation.html' %}

        <!-- Messages -->
        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

    <!-- Page content -->
    {% block content %}

    
    
    {% endblock content %}
    
  </div>
  
  <!-- Argon Scripts -->
  <!-- Core -->
  <script src="/static/assets/vendor/jquery/dist/jquery.min.js"></script>
  <script src="/static/assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="/static/assets/vendor/js-cookie/js.cookie.js"></script>
  <script src="/static/assets/vendor/jquery.scrollbar/jquery.scrollbar.min.js"></script>
  <script src="/static/assets/vendor/jquery-scroll-lock/dist/jquery-scrollLock.min.js"></script>
  
  <script src="https://unpkg.com/htmx.org@1.6.0/dist/htmx.min.js"></script>

  <!-- Include Select2 JS -->
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

  <div id="tester" style="width:600px;height:250px;"></div>

  <script>
    document.body.addEventListener('htmx:configRequest', (event) => {
        event.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
    })






    function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            // Does this cookie string begin with the name we want?
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}



// Search Common in Nav Bar 
$('#search_common').on('keypress', function (e) {
    if (e.which === 13) { // Check if the pressed key is Enter
        e.preventDefault(); // Prevent the default behavior of the Enter key

        var patient_info = $(this).val();
        if (patient_info.length > 2) {
            var url = "{% url 'search_patient_download' %}";
            var requestData = {
                patient_info: patient_info
            };

            $.ajax({
                type: "POST",
                url: url,
                headers: {
                    "X-Requested-With": "XMLHttpRequest",
                    "X-CSRFToken": getCookie("csrftoken")
                },
                data: requestData,

                success: function(response) {
                    // Log the response to check if it contains the expected data
                    //console.log(response);

                    // Update modal content with response data
                    $('#modalBodyContent').html(response.samples);

                    // Show the modal
                    $('#resultModal').modal('show');
                },

                error: function(xhr, status, error) {
                    // Log any errors for debugging
                    console.error(xhr.responseText);
                }
            });
        }
    }
});





  </script>


  <!-- Specific Page JS goes HERE  -->
  {% block javascripts %}
  
  {% endblock javascripts %}
  
  <!-- Argon JS -->
  <script src="/static/assets/js/argon.js?v=1.2.0"></script>
  <!-- Demo JS - remove this in your project -->


</body>
</html>
