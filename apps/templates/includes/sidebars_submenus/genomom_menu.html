                {% if request.user.is_staff and request.user.user_group in '123456'  or request.user.is_admin %}

                <li class="nav-item">
                     
                        <a class="nav-link {% if 'nipt-' in segment %} active {% endif %}"
                        href="#navbar-nipt" data-toggle="collapse" role="button" aria-expanded="false"
                        aria-controls="navbar-nipt">

                        <i class="fa fa-child"></i>

                        <span class="nav-link-text">NIPT 서비스</span>
                    </a>
                    <div class="collapse  {% if active_menu == 'nipt' %} show {% endif %} " id="navbar-nipt">

                        <ul class="nav nav-sm flex-column">

                            {% if request.user.user_group in '125' %}

                            <li class="nav-item">
                                <a href="{% url 'sample_entry_new' %}" class="nav-link {% if request.resolver_match.url_name == 'sample_entry_new' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> 입고 {{ request.user.user_group }}  </span>
                                    <span class="sidenav-normal"> 검체 입고 </span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="{% url 'sample_entry_list' %}" class="nav-link {% if request.resolver_match.url_name == 'sample_entry_list' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> 확인 </span>
                                    <span class="sidenav-normal"> 입고 확인 </span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{% url 'dna_qc0' %}" class="nav-link {% if request.resolver_match.url_name == 'dna_qc0' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> qc0 </span>
                                    <span class="sidenav-normal"> DNA Prep </span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="{% url 'dna_qc1' %}" class="nav-link {% if request.resolver_match.url_name == 'dna_qc1' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> qc1 </span>
                                    <span class="sidenav-normal"> DNA Lib </span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="{% url 'retest_lab_list' %}" class="nav-link {% if request.resolver_match.url_name == 'retest_lab_list' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> R </span>
                                    <span class="sidenav-normal text-red "> Re-test </span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="{% url 'dna_qc2' %}" class="nav-link {% if request.resolver_match.url_name == 'dna_qc2' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> qc2 </span>
                                    <span class="sidenav-normal"> DNA Seq </span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{% url 'analysis_list' %}" class="nav-link {% if request.resolver_match.url_name == 'analysis_list' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> p.p </span>
                                    <span class="sidenav-normal"> Pooling </span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="{% url 'pipeline_run' %}" class="nav-link {% if request.resolver_match.url_name == 'pipeline_run' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> 분석 </span>
                                    <span class="sidenav-normal"> 분석 </span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="{% url 'result_confirm1' %}" class="nav-link {% if request.resolver_match.url_name == 'result_confirm1' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> 1차 </span>
                                    <span class="sidenav-normal"> 1차 판독 </span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="{% url 'result_confirm2' %}" class="nav-link {% if request.resolver_match.url_name == 'result_confirm2' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> 2차 </span>
                                    <span class="sidenav-normal"> 2차 판독 </span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a href="{% url 'retest_confirm_release' %}" class="nav-link {% if request.resolver_match.url_name == 'retest_confirm_release' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> re-test </span>
                                    <span class="sidenav-normal text-red"> 재실험 관리 </span>
                                </a>
                            </li>

                            {% comment %} <li class="nav-item">
                                <a href="{% url 'result_design_pdf' %}" class="nav-link">
                                    <span class="sidenav-mini-icon"> pdf </span>
                                    <span class="sidenav-normal"> 결과지 생성 (design) </span>
                                </a>
                            </li> {% endcomment %}

                            <li class="nav-item">
                                <a href="{% url 'release_management' %}" class="nav-link {% if request.resolver_match.url_name == 'release_management' %} active {% endif %}">
                                    <span class="sidenav-mini-icon">출고 </span>
                                    <span class="sidenav-normal"> 출고 관리 </span>
                                </a>
                            </li>

                             {% endif %}



                            <li class="nav-item">
                                <a href="{% url 'result_download_pdf' %}" class="nav-link {% if request.resolver_match.url_name == 'result_download_pdf' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> DB </span>
                                    <span class="sidenav-normal">Data base </span>
                                </a>
                            </li>

                            {% if request.user.user_group in '125' %}

                            <li class="nav-item">
                                <a href="{% url 'analysis_list_all' %}" class="nav-link {% if request.resolver_match.url_name == 'analysis_list_all' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> 정보 </span>
                                    <span class="sidenav-normal"> 분석 정보 </span>
                                </a>
                            </li> 

                            <li class="nav-item">
                                <a href="{% url 'entry_check' %}" class="nav-link {% if request.resolver_match.url_name == 'entry_check' %} active {% endif %}">
                                    <span class="sidenav-mini-icon">EC </span>
                                    <span class="sidenav-normal"> 검수 </span>
                                </a>
                            </li>


                            <li class="nav-item">
                                <a href="{% url 'retest_list' %}" class="nav-link {% if request.resolver_match.url_name == 'retest_list' %} active {% endif %}">
                                    <span class="sidenav-mini-icon">re-test(DB) </span>
                                    <span class="sidenav-normal">Re-Test(DB) </span>
                                </a>
                            </li>


                            <li class="nav-item">
                                <a href="{% url 'disposal_samples' %}" class="nav-link {% if request.resolver_match.url_name == 'disposal_samples' %} active {% endif %}">
                                    <span class="sidenav-mini-icon">   DB  </span>
                                    <span class="sidenav-normal"> 검체폐기대장  </span>
                                </a>
                            </li>


                             {% endif %}
                             
                        </ul>
                    </div>
                </li>
                {% endif %}