{% if request.user.is_staff and request.user.is_admin %}

<li class="nav-item">
    <a class="nav-link {% if 'nipt-' in segment %} active {% endif %}" href="#navbar-staff" data-toggle="collapse"
        role="button" aria-expanded="false" aria-controls="navbar-staff">
        <i class="fa fa-users" aria-hidden="true"></i>

        <span class="nav-link-text">직원 관리 </span>
    </a>
    <div class="collapse {% if active_menu == 'emp_mgmt' %} show {% endif %}  " id="navbar-staff">

        <ul class="nav nav-sm flex-column">
            <li class="nav-item">
                <a href="{% url 'add_staff' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'add_staff' %} active {% endif %}">
                    <span class="sidenav-mini-icon"> 추가 </span>
                    <span class="sidenav-normal"> 직원 추가 </span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{% url 'list_staff' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'list_staff' %} active {% endif %}">
                    <span class="sidenav-mini-icon"> 목록 </span>
                    <span class="sidenav-normal"> 직원 목록 </span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{% url 'user_list' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'user_list' %} active {% endif %}">
                    <span class="sidenav-mini-icon"> 사용자 </span>
                    <span class="sidenav-normal"> 사용자 관리 </span>
                </a>
            </li>
        </ul>

    </div>
</li>

{% endif %}