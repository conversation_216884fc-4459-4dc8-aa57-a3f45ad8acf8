                {% if request.user.is_staff and request.user.user_group in '123'  or request.user.is_admin %}



                <li class="nav-item">
                    <a class="nav-link {% if 'gbn-' in segment %} active {% endif %}"
                        href="#navbar-gbn" data-toggle="collapse" role="button" aria-expanded="false"
                        aria-controls="navbar-gbn">

                        <i class="fa fa-user-circle" aria-hidden="true" style="color:blue;font-weight:800" ></i>

                        <span class="nav-link-text" style="color:#080E4B;font-weight:800">Genobenet 서비스</span>
                    </a>
                    <div class="collapse {% if active_menu == 'genobenet' %} show {% endif %}  " id="navbar-gbn">

                        <ul class="nav nav-sm flex-column text-green ">
                        
                            {% if request.user.user_group in '125' %}
                            <li class="nav-item">
                                <a href="{% url 'gbn_sample_entry' %}"
                                    class="nav-link {% if request.resolver_match.url_name == 'gbn_sample_entry' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> 입고 </span>
                                    <span class="sidenav-normal" style="color:#000435;"> 검체 입고 </span>
                                </a>
                            </li>
                        
                            <li class="nav-item">
                                <a href="{% url 'gbn_entry_list' %}"
                                    class="nav-link {% if request.resolver_match.url_name == 'gbn_entry_list' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> 확인 </span>
                                    <span class="sidenav-normal" style="color:#000435;"> 입고 확인 </span>
                                </a>
                            </li>
                        
                            <li class="nav-item">
                                <a href="{% url 'gbn_dna_qc0' %}"
                                    class="nav-link {% if request.resolver_match.url_name == 'gbn_dna_qc0' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> qc0 </span>
                                    <span class="sidenav-normal" style="color:#000435;"> DNA Prep </span>
                                </a>
                            </li>
                        
                            <li class="nav-item">
                                <a href="{% url 'gbn_dna_qc1' %}"
                                    class="nav-link {% if request.resolver_match.url_name == 'gbn_dna_qc1' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> qc1 </span>
                                    <span class="sidenav-normal" style="color:#000435;"> DNA1 Lib </span>
                                </a>
                            </li>
                        
                            <li class="nav-item">
                                <a href="{% url 'gbn_dna_qc2' %}"
                                    class="nav-link {% if request.resolver_match.url_name == 'gbn_dna_qc2' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> qc2 </span>
                                    <span class="sidenav-normal" style="color:#000435;"> DNA Seq </span>
                                </a>
                            </li>
                        
                            <li class="nav-item">
                                <a href="{% url 'gbn_analysis' %}"
                                    class="nav-link {% if request.resolver_match.url_name == 'gbn_analysis' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> p.p </span>
                                    <span class="sidenav-normal" style="color:#000435;"> Pooling </span>
                                </a>
                            </li>
                        
                            <li class="nav-item">
                                <a href="{% url 'gbn_ir_update' %}"
                                    class="nav-link {% if request.resolver_match.url_name == 'gbn_ir_update' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> R </span>
                                    <span class="sidenav-normal" style="color:#000435;"> IR Update </span>
                                </a>
                            </li>
                        
                            <li class="nav-item">
                                <a href="{% url 'gbn_result_confirm' %}"
                                    class="nav-link {% if request.resolver_match.url_name == 'gbn_result_confirm' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> C </span>
                                    <span class="sidenav-normal" style="color:#000435;"> 판독 </span>
                                </a>
                            </li>
                        
                            <li class="nav-item">
                                <a href="{% url 'gbn_release_management' %}"
                                    class="nav-link {% if request.resolver_match.url_name == 'gbn_release_management' %} active {% endif %}">
                                    <span class="sidenav-mini-icon">출고 </span>
                                    <span class="sidenav-normal" style="color:#000435;"> 출고 관리 </span>
                                </a>
                            </li>
                            {% endif %}
                        
                            <li class="nav-item">
                                <a href="{% url 'gbn_result_down' %}"
                                    class="nav-link {% if request.resolver_match.url_name == 'gbn_result_down' %} active {% endif %}">
                                    <span class="sidenav-mini-icon"> DB </span>
                                    <span class="sidenav-normal" style="color:#000435;">Data base </span>
                                </a>
                            </li>
                        
                            {% if request.user.user_group in '125' %}
                            <li class="nav-item">
                                <a href="{% url 'gbn_entry_check' %}"
                                    class="nav-link {% if request.resolver_match.url_name == 'gbn_entry_check' %} active {% endif %}">
                                    <span class="sidenav-mini-icon">EC </span>
                                    <span class="sidenav-normal" style="color:#000435;"> GBN 검수 </span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>


                    </div>
                </li>
                {% endif %}