<li class="nav-item">
    <a class="nav-link active " href="#navbar-crm" data-toggle="collapse" role="button" aria-expanded="false"
        aria-controls="navbar-nipt">
        <i class="fa fa-phone-square" aria-hidden="true"></i>

        <span class="nav-link-text">TGC-CRM </span>
    </a>
    <div class="collapse  {% if active_menu == 'crm' %} show {% endif %} " " id="navbar-crm">

        <ul class="nav nav-sm flex-column">

            <li class="nav-item">
                <a href="{% url 'faq_list' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'faq_list'  or  request.resolver_match.url_name == 'faq_create'  or  request.resolver_match.url_name == 'faq_detail'  %} active {% endif %}">
                    <span class="sidenav-mini-icon"> FAQ </span>
                    <span class="sidenav-normal"> FAQ 리스트 </span>
                </a>
            </li>

            <li class="nav-item">
                <a href="{% url 'cs_inquiry_list' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'cs_inquiry_list' %} active {% endif %}">
                    <span class="sidenav-mini-icon"> CS </span>
                    <span class="sidenav-normal"> CS 이력 기록 </span>
                </a>
            </li>

            {% if request.user.is_staff %}
            <!--  Only Persons who are in Marketing depart ment show this menu or User who is krishna ( Only for testing purpose ) and CEO 대표님 and  request.user.user_employee.work_depart in 'marketsalesitceo'   -->

            <li class="nav-item">
                <a href="{% url 'marketing_visit_list' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'marketing_visit_list' or  request.resolver_match.url_name == 'marketing_visit_create' %} active {% endif %}">
                    <span class="sidenav-mini-icon"> PM </span>
                    <span class="sidenav-normal"> 방문기록 작성 </span>
                </a>
            </li>

            <li class="nav-item">
                <a href="{% url 'marketing_visit_list_detail' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'marketing_visit_list_detail'   %} active {% endif %}">
                    <span class="sidenav-mini-icon"> PM </span>
                    <span class="sidenav-normal"> 방문기록 리뷰 </span>
                </a>
            </li>

            {% endif %}

            <li class="nav-item">
                <a href="{% url 'pagemanual_list' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'pagemanual_list' %} active {% endif %}">
                    <span class="sidenav-mini-icon"> PM </span>
                    <span class="sidenav-normal"> Page manuals </span>
                </a>
            </li>

        </ul>
    </div>
</li>