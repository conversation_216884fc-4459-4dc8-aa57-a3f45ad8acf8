{% if request.user.is_staff and request.user.user_group in '12567' or request.user.is_admin %}

<li class="nav-item">
    <a class="nav-link {% if 'hospital-' in segment %} active {% endif %}" href="#navbar-hospitals"
        data-toggle="collapse" role="button" aria-expanded="false" aria-controls="navbar-hospitals">
        <i class="ni ni-building"></i>
        <span class="nav-link-text">재단 및 기관 관리 </span>
    </a>
    <div class="collapse  {% if active_menu == 'hospital' %} show {% endif %} " id="navbar-hospitals">

        <ul class="nav nav-sm flex-column">
            <li class="nav-item">
                <a href="{% url 'add_jedan' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'add_jedan' %} active {% endif %}">
                    <span class="sidenav-mini-icon"> 재단추가 </span>
                    <span class="sidenav-normal"> 의료재단 추가 </span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{% url 'list_jedan' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'list_jedan' %} active {% endif %}">
                    <span class="sidenav-mini-icon"> 재단목록 </span>
                    <span class="sidenav-normal"> 의료재단 목록 </span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{% url 'add_jedan_branch' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'add_jedan_branch' %} active {% endif %}">
                    <span class="sidenav-mini-icon"> 지점추가 </span>
                    <span class="sidenav-normal">의료재단 지점 추가 </span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{% url 'list_jedan_branch' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'list_jedan_branch' %} active {% endif %}">
                    <span class="sidenav-mini-icon"> 지점목록 </span>
                    <span class="sidenav-normal"> 의료재단 지점 목록 </span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{% url 'create_hospital' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'add_hospital' %} active {% endif %}">
                    <span class="sidenav-mini-icon"> 기관추가 </span>
                    <span class="sidenav-normal"> 의료기관 추가 </span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{% url 'list_hospital' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'list_hospital' %} active {% endif %}">
                    <span class="sidenav-mini-icon"> 기관목록 </span>
                    <span class="sidenav-normal"> 의료기관 목록 </span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{% url 'add_doctor' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'add_doctor' %} active {% endif %}">
                    <span class="sidenav-mini-icon"> 의사추가 </span>
                    <span class="sidenav-normal"> 의사 추가 </span>
                </a>
            </li>
            <li class="nav-item">
                <a href="{% url 'list_doctor' %}"
                    class="nav-link {% if request.resolver_match.url_name == 'list_doctor' %} active {% endif %}">
                    <span class="sidenav-mini-icon"> 의사목록 </span>
                    <span class="sidenav-normal"> 의사 목록 </span>
                </a>
            </li>
        </ul>

    </div>
</li>

{% endif %}