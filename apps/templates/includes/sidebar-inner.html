<style>
    .nav-link.active {
        background-color: rgb(72, 220, 119);
        /* Light gray, or your theme color */
        border-radius: 8px;
        font-weight: bold;
    }
</style>

<!-- The Inner links -->
<div class="scrollbar-inner">
    <!-- Brand -->
    <div class="sidenav-header  d-flex  align-items-center">
        <a class="navbar-brand" href="/">
            <img src="/static/assets/img/brand/logo_long.png" class="navbar-brand-img" alt="...">
        </a>
        <div class=" ml-auto ">
            <!-- Sidenav toggler -->
            <div class="sidenav-toggler d-none d-xl-block" data-action="sidenav-unpin" data-target="#sidenav-main">
                <div class="sidenav-toggler-inner">
                    <i class="sidenav-toggler-line"></i>
                    <i class="sidenav-toggler-line"></i>
                    <i class="sidenav-toggler-line"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="navbar-inner">
        <!-- Collapse -->
        <div class="collapse navbar-collapse" id="sidenav-collapse-main">
            <!-- Nav items -->
            <ul class="navbar-nav">

                {% if request.user.is_staff and request.user.user_group in '1256'  or request.user.is_admin %}

                <button type="button"> <a href="/tgc_admin/" class="nav-link">
                        <span class="sidenav-mini-icon"> A </span>
                        <span class="sidenav-normal"> 관리자 </span>
                    </a>
                </button>

                {%endif %}

                {% include 'includes/sidebars_submenus/dashboards_menu.html' %}

                {% include 'includes/sidebars_submenus/genomom_menu.html'%}

                {% include 'includes/sidebars_submenus/hospital_mgmt.html' %}

                {% include 'includes/sidebars_submenus/employee_management.html' %}

                {% include 'includes/sidebars_submenus/cs_crm_management.html' %}

                {% include "includes/sidebars_submenus/genobenet_menu.html" %}

                {% if request.user.is_staff and  request.user.is_admin %}
                <li class="nav-item">
                    <a class="nav-link {% if 'jedan' in segment %} active {% endif %}" href="{% url 'hospital' %}">
                        <i class="fa fa-hospital" aria-hidden="true"></i>
                        <span class="nav-link-text"> 재단 페이지</span>
                    </a>
                </li>
                {% endif %}

                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'calendar' %} active {% endif %}"
                        href="{% url 'calendar' %}">
                        <i class="ni ni-calendar-grid-58 text-red"></i>
                        <span class="nav-link-text">Calendar</span>
                    </a>
                </li>

            </ul>
        </div>
    </div>
</div>