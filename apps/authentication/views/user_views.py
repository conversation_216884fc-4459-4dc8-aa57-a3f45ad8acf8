from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.utils import timezone
from django.contrib import messages
from django.db.models import Q, Case, When, Value, IntegerField, F, ExpressionWrapper, DurationField, Count
from ..models.User import User
from datetime import datetime, timedelta

@login_required
def user_list(request):
    """View to list all users with pagination and filtering"""
    users = User.objects.all()
    
    # Get statistics
    stats = {
        'total_users': User.objects.count(),
        'hospital_users': User.objects.filter(is_hospital=True).count(),
        'jedan_users': User.objects.filter(is_jedan=True).count(),
        'jedan_branch_users': User.objects.filter(is_jedan_branch=True).count(),
        'employee_users': User.objects.filter(user_employee__isnull=False).count(),
        'active_users': User.objects.filter(is_active=True).count(),
        'inactive_users': User.objects.filter(is_active=False).count(),
        'inactive_60_days': User.objects.filter(
            last_login__lt=timezone.now() - timedelta(days=60)
        ).count()
    }
    
    # Apply sorting
    sort_by = request.GET.get('sort', '-date_joined')
    sort_direction = '-' if sort_by.startswith('-') else ''
    sort_field = sort_by.lstrip('-')
    
    # Apply filters
    status = request.GET.get('status')
    role = request.GET.get('role')
    search_query = request.GET.get('search', '')
    login_condition = request.GET.get('login_condition')
    login_days = request.GET.get('login_days')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(user_employee__full_name=search_query)  |
            Q(assigned_jedan_branch__jedan_branch_name=search_query) | 
            Q(hospital_name__hospital_name=search_query) 
        )
    
    if status:
        if status == 'active':
            users = users.filter(is_active=True)
        elif status == 'inactive':
            users = users.filter(is_active=False)
    
    if role:
        if role == 'staff':
            users = users.filter(is_staff=True)
        elif role == 'admin':
            users = users.filter(is_admin=True)
        elif role == 'jedan':
            users = users.filter(is_jedan=True)
        elif role == 'hospital':
            users = users.filter(is_hospital=True)
        elif role == 'jedan_branch':
            users = users.filter(assigned_jedan_branch__isnull=False)
    
    # Apply date range filter
    if date_from:
        users = users.filter(date_joined__date__gte=date_from)
    if date_to:
        users = users.filter(date_joined__date__lte=date_to)
    
    # Calculate days since last login for each user
    today = timezone.now()
    
    # First, annotate with the actual days difference
    users = users.annotate(
        days_diff=Case(
            When(
                last_login__isnull=True,
                then=Value(None)
            ),
            default=ExpressionWrapper(
                timezone.now() - F('last_login'),
                output_field=DurationField()
            )
        )
    )
    
    # Apply login days filter
    if login_days and login_days.isdigit():
        login_days = int(login_days)
        if login_condition == 'less_than':
            users = users.filter(days_diff__lt=timedelta(days=login_days))
        elif login_condition == 'more_than':
            users = users.filter(days_diff__gt=timedelta(days=login_days))
        elif login_condition == 'exactly':
            users = users.filter(days_diff__days=login_days)
    
    # Apply sorting
    if sort_field == 'days':
        users = users.order_by(
            Case(
                When(last_login__isnull=True, then=Value(3)),  # Never logged in
                When(last_login__date=today.date(), then=Value(0)),  # Today
                When(last_login__date=today.date() - timedelta(days=1), then=Value(1)),  # Yesterday
                default=Value(2),  # Other days
                output_field=IntegerField()
            ),
            'last_login' if sort_direction == '' else '-last_login'
        )
    else:
        users = users.order_by(sort_by)
    
    # Add roles and format days since login for each user
    for user in users:
        # Format days since login
        if user.last_login is None:
            user.days_since_login = 'Never'
        elif user.last_login.date() == today.date():
            user.days_since_login = 'Today'
        elif user.last_login.date() == today.date() - timedelta(days=1):
            user.days_since_login = 'Yesterday'
        else:
            user.days_since_login = user.days_diff.days
        
        # Add roles
        user.roles = []
        if user.is_superuser:
            user.roles.append(('superuser', 'Super User', 'fa-user-shield', 'text-danger'))
        if user.is_staff:
            user.roles.append(('staff', 'Staff', 'fa-user-tie', 'text-info'))
        if user.is_admin:
            user.roles.append(('admin', 'Admin', 'fa-user-shield', 'text-warning'))
        if user.is_jedan:
            user.roles.append(('jedan', 'Jedan', 'fa-user-md', 'text-success'))
        if user.is_hospital:
            user.roles.append(('hospital', 'Hospital', 'fa-hospital', 'text-primary'))
        if user.is_jedan_branch:
            user.roles.append(('jedan_branch', 'Jedan Branch', 'fa-building', 'text-success'))
        if not user.roles:
            user.roles.append(('user', 'User', 'fa-user', 'text-secondary'))
    
    # Pagination
    paginator = Paginator(users, 100)  # Show 100 users per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'active_menu': 'user_mgmt',
        'stats': stats,
        'current_filters': {
            'status': status,
            'role': role,
            'search': search_query,
            'sort': sort_by,
            'login_condition': login_condition,
            'login_days': login_days,
            'date_from': date_from,
            'date_to': date_to
        }
    }
    return render(request, 'authentication/user_list.html', context)

@login_required
def user_detail(request, user_id):
    """View to show detailed information about a specific user"""
    user = get_object_or_404(User, id=user_id)
    
    # Calculate days since last login
    if user.last_login:
        user.days_since_login = (timezone.now() - user.last_login).days
    else:
        user.days_since_login = 'Never'
    
    context = {
        'user': user,
        'active_menu': 'user_mgmt'
    }
    return render(request, 'authentication/user_detail.html', context)

@login_required
def toggle_user_status(request, user_id):
    """Toggle user active status"""
    if not request.user.is_staff and not request.user.is_admin:
        messages.error(request, "이 작업을 수행할 권한이 없습니다.")
        return redirect('user_list')
        
    user = get_object_or_404(User, id=user_id)
    user.is_active = not user.is_active
    user.save()
    
    status = "활성화" if user.is_active else "비활성화"
    messages.success(request, f"사용자 {user.username}가 {status}되었습니다. 이제 LIMS 시스템에 {'로그인할 수 있습니다' if user.is_active else '로그인할 수 없습니다'}.")
    return redirect('user_list') 