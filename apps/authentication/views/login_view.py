# -*- encoding: utf-8 -*-
"""
A One Man Army project

Copyright (c) 2024 - present Genome Care 

AI Based NIPT analysis LIMS system to make process smoothly and securely.

developed by AIM Team <PERSON><PERSON><PERSON> 크리스나 <EMAIL> 01022734018 
"""


# Third-party Imports
from axes.decorators import axes_dispatch

from django.shortcuts import render, redirect

from apps.authentication.forms import LoginForm

from django.contrib.auth import authenticate, login
from urllib.parse import unquote




@axes_dispatch
def login_view(request):

    """
    This function will call when first time user login.
    User must be redirect on the basis of user type.
    decorater axes_dispatch is use to keep user log recording.
    
    """
        
    #!  First Check weather user is authenticated or Not
    is_authenticated = request.user.is_authenticated
    is_active = request.user.is_active
    

    #! Currently we are using same url for hospitals to all
    # print( is_authenticated, is_active , request.user.is_jedan_branch  )
    
    if request.user.is_authenticated and is_active and request.user.is_jedan:
        return redirect('/hospital')
    
    
    if  is_authenticated and is_active and request.user.is_jedan_branch:
        return redirect("/hospital")
    

    
    if request.user.is_authenticated and is_active and request.user.is_hospital:
        return redirect('/hospital')
    
    
    if  request.user.is_authenticated and is_active and ( request.user.is_admin or request.user.is_staff )  :
        #! check weather this user is assigned to specific employee or not 
                
        page_redirect_code = request.user.redirect 
        
        #! Only Staff will come here because of or condition
        if page_redirect_code == "1":  # Sample Entry
            return redirect('sample_entry_new') 
        
        #! Only Staff will come here because of or condition
        if page_redirect_code == "2":  # Sample Entry
            return redirect('dna_qc0') 
        
        
        #! Only Staff will come here because of or condition
        if page_redirect_code == "3":  # Pipe line Run
            return redirect('pipeline_run') 
        
        #! Only Staff will come here because of or condition
        if page_redirect_code == "4":  # Sample Entry
            return redirect('result_confirm1') 
        
        #! Only Staff will come here because of or condition
        if page_redirect_code == "5":  # Sample Entry
            return redirect('result_confirm2')
        
        #! Only Staff will come here because of or condition
        if page_redirect_code == "6":  # Sample Entry
            return redirect('release_management')
        
        #! Only Staff will come here because of or condition
        if page_redirect_code == "7":  # Sample Entry
            return redirect('release_report')  # /release_report  recent_days
        
        #! Only Staff will come here because of or condition
        if page_redirect_code == "X":  # Sample Entry
            return redirect('login')
        
        #! Only Staff will come here because of or condition
        if page_redirect_code == "H":  # Sample Entry
            return redirect('hospital')
        
        
        
        #! either employee or admin
        if request.user.is_admin:
            return redirect('result_download_pdf')  # dashboards/recent_days/
    
    

    form = LoginForm(data=request.POST or None)
    msg = None

    if request.method == "POST":
        if form.is_valid():
            username = form.cleaned_data.get("username")
            password = form.cleaned_data.get("password")
            user = authenticate(username=username, password=password, request=request)

            if user is not None:
                login(request, user)
                
                # We WIll Re-Direct on the basis of user types
                #  First Check weather user is authenticated or Not
                is_authenticated = request.user.is_authenticated
                is_active = request.user.is_active
                
                if  is_authenticated and is_active and request.user.is_hospital :
                    return redirect("/hospital")
                
                if  is_authenticated and is_active and request.user.is_jedan:
                    return redirect("/hospital")
                
                if  is_authenticated and is_active and request.user.is_jedan_branch:
                    return redirect("/hospital")
                
                
                if  is_authenticated and is_active and ( request.user.is_admin or request.user.is_staff )  :
                    #! check weather this user is assigned to specific employee or not 
                    
                    # 리디렉션 코드
                    if request.GET.get('next'):
                        next_url = request.GET.get('next')
                        decoded_url = unquote(next_url)
                        return redirect(decoded_url)
                    
                    page_redirect_code = request.user.redirect 
                    
                    #! Only Staff will come here because of or condition
                    if page_redirect_code == "1":  # Sample Entry
                        return redirect('sample_entry_new') 
                    
                    #! Only Staff will come here because of or condition
                    if page_redirect_code == "2":  # Sample Entry
                        return redirect('dna_qc0') 
                    
                    
                    #! Only Staff will come here because of or condition
                    if page_redirect_code == "3":  # Pipe line Run
                        return redirect('pipeline_run') 
                    
                    #! Only Staff will come here because of or condition
                    if page_redirect_code == "4":  # Sample Entry
                        return redirect('result_confirm1') 
                    
                    #! Only Staff will come here because of or condition
                    if page_redirect_code == "5":  # Sample Entry
                        return redirect('result_confirm2')
                    
                    #! Only Staff will come here because of or condition
                    if page_redirect_code == "6":  # Sample Entry
                        return redirect('release_management')
                    
                    #! Only Staff will come here because of or condition
                    if page_redirect_code == "7":  # Sample Entry
                        return redirect('release_report')
                    
                    #! Only Staff will come here because of or condition
                    if page_redirect_code == "X":  # Sample Entry
                        return redirect('login')
                    
                    #! Only Staff will come here because of or condition
                    if page_redirect_code == "H":  # Sample Entry
                        return redirect('hospital')
                    
                    
                    
                    #! either employee or admin
                    if request.user.is_admin:
                        return redirect('result_download_pdf')  # dashboards/recent_days/
                
                
                
                
                
                
                
                #! For Admin
                if is_authenticated and is_active and request.user.is_admin:
                    return redirect("recent_days")
                
                #! in organization there are some employees who are not admin but only staffs
                if is_authenticated and is_active and request.user.is_staff:
                    return redirect("recent_days")

                #! Finally For Organization admin superuser
                if is_authenticated and is_active and request.user.is_superuser:
                    # jedan Branch Specific templates
                    return redirect("/admin")
                
            else:
                msg = 'User Id or Password not matched 사용자 로그인 정보가 일치하지 않습니다.'
        else:
            msg = 'Form Validation Error User ID or Password format not matched'

            return  render(request,"authentication/login.html",{"form":form,"msg":msg} , status= 400 )
            ## return render(request, "authentication/login.html", {"form": form, "msg" : msg}) # GB
    return render(request, "accounts/login.html" , {"form": form,"msg":msg}) # "authentication/login.html"
