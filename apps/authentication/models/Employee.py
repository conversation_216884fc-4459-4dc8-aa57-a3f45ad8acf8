# -*- encoding: utf-8 -*-
"""
Copyright (c) 2022 - Theragen Genome Care.
Developed by <PERSON> (크리스나)



"""

from .User import User
from .team import Team
from django.dispatch import receiver
from django.db.models.signals import pre_delete

from django.db import models
from django.core.validators import RegexValidator
# Create your models here.

class Employee(models.Model):
    """
    Employee of the Organization
    """
    
    DEPART_CHOICES = (    
                    
                    ('lab','실험'), #! Lab Testing members
                    ("market","영업"), #! Marketing
                    ("it","IT"), #! IT if required
                    ("ceo","대표님 "), #! CEO 
                    ("account","운영")  #! Option 2
    )
    
    GENDER_CHOICES = (
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
    )
    POSITION = [
        ("대표이사", "대표이사"),
        ("상무이사","상무이사"),
        ("이사","이사"),
        ("부장", "부장"),
        ("차장", "차장"),
        ("과장", "과장"),
        ("대리","대리"),
        ("주임","주임"),
        ("사원","사원")
        
    ]
    
    #! Page permission 
    TEAM_CHOICES = [

                ("lab", "실험"),                
                ("it", "IT팀"),
                ("marketing", "마케팅팀"),
                ("sales", "영업팀"),
                ("research", "연구개발팀"),
                ("cs", "고객지원팀"),
                # ("hr", "인사팀"),
                # ("finance", "재무팀"),
                # ("production", "생산팀"),
                ("quality", "품질관리팀"),
                # ("design", "디자인팀"),
                ("management", "경영지원팀"),
                # ("account", "운영실"),
                # ("executive", "대표이사실"), #! if other employee work to support ceo like cytogen ceo,... 
                ("ceo", "대표님"),
            ]
        
    user = models.OneToOneField(User, on_delete= models.PROTECT, related_name="user_employee" )
    full_name = models.CharField(verbose_name='이름', max_length=150 , help_text= "직원 전체 이름 ", default = "-" )  # Store full name in one field
    english_name = models.CharField(verbose_name='영문 이름', max_length=150, help_text= "직원 전체 영문 이름 ", default = "-" )  # Store full name in one field
    
    #! this team is using to filter , 영업 방문자 
    team = models.CharField( verbose_name='부서', choices=TEAM_CHOICES ,max_length = 20 ,null=True, blank=True, default = "lab"  )
    
    #! use this as office location where e.g 사무실 의치  #! work_location office_location 
    work_depart = models.CharField(
                    verbose_name='사무실 위치',
                    max_length=40,
                    null=True,
                    blank=True,
                    default='8층 검사본부'
                )

    address = models.CharField('주소',max_length=100, null=True, blank=True, default="경기도 수원시 ")
    gender = models.CharField(verbose_name='성별',max_length=1,choices=GENDER_CHOICES,null=True, blank=True, default="F")
    
        
    joined_date = models.DateField(verbose_name='입사 일',null=True, blank=True)
    end_date = models.DateField('퇴사 날',null=True, blank=True)
    
    is_work = models.BooleanField(
        verbose_name="근무 여부",
        default=True,
        help_text="직원이 현재 재직 중이면 체크하세요. 퇴사한 경우 체크 해제하세요.",
        db_index=True
    )

    #! Move this to user table and 
    # profile_image = models.ImageField(blank = True,null = True, upload_to="profile_images/")
    #self_intro = models.TextField(verbose_name='self introduction',max_length = 1000,null=True, blank=True) #! Remove later

    phone_regex = RegexValidator(regex=r'^\+?1?\d{6,15}$', message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.")

    com_tel = models.CharField('내선 번호', max_length=12, blank=True,default='') #!validators=[phone_regex],
    mobile_nb = models.CharField('휴대 전화',validators=[phone_regex], max_length=15, blank=True,default="01067055254")

    company_email = models.EmailField('회사 이메일',max_length = 100,null=True, blank=True,default="@genomecare.net") 
    kakao_user    = models.CharField('카카오 사용자', max_length=100, null=True, blank=True , help_text="카카오 알림 받기 위해 사용자 ID")
    
    cs_alarm = models.BooleanField('CS 알림', default=False, help_text='CS 알림을 받을지 여부') 

    #depart  = models.CharField(verbose_name='부서',max_length = 100,null=True, blank=True, default = "")
    
    emergency_contact_name = models.CharField('비상 연락자 이름', max_length=100, null=True, blank=True, help_text="비상 연락자 이름")
    emergency_contact_phone = models.CharField('비상 연락처', max_length=15, null=True, blank=True, help_text="비상 연락처")

    
    depart = models.ManyToManyField(
        'Team', related_name='employee_team', verbose_name='Team Permissions', )
        
    
    position = models.CharField(verbose_name='직위',max_length = 30,null=True, blank=True,
                                default="사원", choices=POSITION)
    
    
    added_by = models.CharField(max_length=30, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True )
    last_edited = models.DateTimeField(null=True, blank=True, auto_now=True,)
    # profile_edit = models.DateTimeField(verbose_name='최근 수정',auto_now=True)
    

    def __str__(self):
        return f"{self.full_name}-{(self.user.username)}"
    
    def get_full_name(self):
        """
        Return the first_name plus the last_name, with a space in between.
        """
        return self.full_name
        
    
    def get_full_address(self):
        """Return the full address name for the user."""
        return f"{self.do_address} {self.si_address} {self.address2}"
        
    def all_permissions(self):
        """
        get the permissions of Employees related many to many in Team
        """
        all_departments = self.depart.all()
        
        # Extract department display names into a list
        department_display_names = [department.get_name_display() for department in all_departments]
        
        # Join department names into a single string
        departments_string = ", ".join(department_display_names)

        return departments_string

    class Meta:
        verbose_name = "직원 관리"# table header in admin sites
        verbose_name_plural = "직원들 관리"


@receiver(pre_delete, sender=Team)
def remove_teams_from_employees(sender, instance, **kwargs):
    instance.employee_team.clear()