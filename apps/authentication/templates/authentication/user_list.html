{% extends "layouts/base.html" %}

{% load static %}

{% block title %}User Management{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .user-avatar {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: #495057;
        font-size: 0.8rem;
        flex-shrink: 0;
    }
    
    .user-avatar img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
    }

    .table th {
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        color: #6c757d;
        padding: 0.75rem;
        white-space: nowrap;
    }

    .table td {
        vertical-align: middle;
        padding: 0.5rem 0.75rem;
        font-size: 0.95rem;
    }

    .badge {
        padding: 0.4em 0.8em;
        font-weight: 500;
        font-size: 0.85rem;
    }

    .action-btn {
        width: 28px;
        height: 28px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: all 0.2s;
        font-size: 0.9rem;
    }

    .action-btn:hover {
        background: #f8f9fa;
    }

    .search-box {
        position: relative;
    }

    .search-box i {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-size: 0.9rem;
    }

    .search-box input {
        padding: 0.6rem 0.8rem 0.6rem 2rem;
        font-size: 0.95rem;
        border-radius: 6px;
    }

    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.95rem;
        border-radius: 6px;
    }

    .card-header h5 {
        font-size: 1.2rem;
        font-weight: 600;
    }

    .card-header small {
        font-size: 0.9rem;
    }

    .days-badge {
        font-size: 0.85rem;
        padding: 0.4em 0.8em;
    }

    .user-info {
        min-width: 0;
        flex: 1;
    }

    .user-info h6 {
        margin: 0;
        font-size: 0.95rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .user-info small {
        font-size: 0.8rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .role-icon {
        font-size: 1rem;
        width: 24px;
        height: 24px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        background: #f8f9fa;
    }

    .role-icon:hover {
        background: #e9ecef;
    }

    .table-hover tbody tr:hover {
        background-color: #f8f9fa;
    }

    .table-hover tbody tr:hover .role-icon {
        background: #e9ecef;
    }

    .search-box input:focus {
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        border-color: #80bdff;
    }

    .search-box input::placeholder {
        color: #adb5bd;
    }

    .stats-card {
        border-radius: 10px;
        border: none;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        transition: transform 0.2s;
        overflow: hidden;
        height: 100%;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
    }
    
    .stats-icon {
        width: 56px;
        height: 56px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
    }
    
    .stats-value {
        font-size: 2.2rem;
        font-weight: 700;
        margin: 0.5rem 0;
        line-height: 1.2;
    }
    
    .stats-label {
        font-size: 1.1rem;
        font-weight: 500;
        color: rgba(255,255,255,0.9);
        margin: 0;
        line-height: 1.2;
    }

    .user-type-table {
        width: 100%;
        margin-top: 1rem;
    }

    .user-type-table td {
        padding: 0.75rem;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .user-type-table tr:last-child td {
        border-bottom: none;
    }

    .user-type-value {
        font-weight: 700;
        font-size: 1.3rem;
        text-align: right;
    }

    .user-type-label {
        font-size: 1.1rem;
        font-weight: 500;
        color: rgba(255,255,255,0.9);
    }

    .user-type-icon {
        width: 32px;
        text-align: center;
        margin-right: 0.75rem;
        font-size: 1.2rem;
    }

    .mini-stats {
        display: flex;
        justify-content: space-between;
        margin-top: 1.25rem;
        padding-top: 1.25rem;
        border-top: 1px solid rgba(255,255,255,0.1);
    }

    .mini-stat {
        text-align: center;
        flex: 1;
    }

    .mini-stat-value {
        font-size: 1.4rem;
        font-weight: 700;
        color: rgba(255,255,255,0.95);
        line-height: 1.2;
    }

    .mini-stat-label {
        font-size: 0.95rem;
        font-weight: 500;
        color: rgba(255,255,255,0.8);
        margin-top: 0.25rem;
    }

    .card-body {
        padding: 1.5rem;
    }
    
    .bg-gradient-primary {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    }
    
    .bg-gradient-success {
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    }
    
    .bg-gradient-info {
        background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
    }
    
    .bg-gradient-warning {
        background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
    }
    
    .bg-gradient-danger {
        background: linear-gradient(135deg, #e74a3b 0%, #be2617 100%);
    }
    
    .bg-gradient-secondary {
        background: linear-gradient(135deg, #858796 0%, #60616f 100%);
    }

    .bg-gradient-purple {
        background: linear-gradient(135deg, #6f42c1 0%, #4e2a84 100%);
    }

    .bg-gradient-pink {
        background: linear-gradient(135deg, #e83e8c 0%, #b02a6b 100%);
    }

    .text-white {
        color: white !important;
    }

    .stats-card .card-body {
        color: white;
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .stats-main {
        flex: 1;
    }

    .row {
        margin-right: -0.75rem;
        margin-left: -0.75rem;
    }

    .col-xl-3, .col-xl-4, .col-xl-5, .col-md-6, .col-md-12 {
        padding-right: 0.75rem;
        padding-left: 0.75rem;
    }

    .g-4 {
        --bs-gutter-y: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <!-- Total Users Card -->
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-gradient-primary">
                <div class="card-body">
                    <div class="stats-main">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-white text-primary me-3">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <div class="stats-value text-white">{{ stats.total_users }}</div>
                                <div class="stats-label text-white">Total Users</div>
                            </div>
                        </div>
                    </div>
                    <div class="mini-stats">
                        <div class="mini-stat">
                            <div class="mini-stat-value">{{ stats.employee_users }}</div>
                            <div class="mini-stat-label">Employees</div>
                        </div>
                        <div class="mini-stat">
                            <div class="mini-stat-value">{{ stats.active_users }}</div>
                            <div class="mini-stat-label">Active</div>
                        </div>
                        <div class="mini-stat">
                            <div class="mini-stat-value">{{ stats.inactive_users }}</div>
                            <div class="mini-stat-label">Inactive</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Types Card -->
        <div class="col-xl-5 col-md-6">
            <div class="card stats-card bg-gradient-info">
                <div class="card-body">
                    <div class="stats-main">
                        <div class="d-flex align-items-center mb-3">
                            <div class="stats-icon bg-white text-info me-3">
                                <i class="fas fa-user-tag"></i>
                            </div>
                            <div>
                                <div class="stats-label text-white">User Types</div>
                            </div>
                        </div>
                        <table class="user-type-table">
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="user-type-icon text-white"><i class="fas fa-hospital"></i></span>
                                        <span class="user-type-label">Hospital Users</span>
                                    </div>
                                </td>
                                <td class="user-type-value text-white">{{ stats.hospital_users }}</td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="user-type-icon text-white"><i class="fas fa-user-md"></i></span>
                                        <span class="user-type-label">Jedan Users</span>
                                    </div>
                                </td>
                                <td class="user-type-value text-white">{{ stats.jedan_users }}</td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="user-type-icon text-white"><i class="fas fa-building"></i></span>
                                        <span class="user-type-label">Jedan Branch Users</span>
                                    </div>
                                </td>
                                <td class="user-type-value text-white">{{ stats.jedan_branch_users }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Cards -->
        <div class="col-xl-4 col-md-12">
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card stats-card bg-gradient-success">
                        <div class="card-body">
                            <div class="stats-main">
                                <div class="d-flex align-items-center">
                                    <div class="stats-icon bg-white text-success me-3">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div>
                                        <div class="stats-value text-white">{{ stats.active_users }}</div>
                                        <div class="stats-label text-white">Active Users</div>
                                    </div>
                                </div>
                            </div>
                            <div class="mini-stats">
                                <div class="mini-stat">
                                    <div class="mini-stat-value">{{ stats.active_users|floatformat:0 }}%</div>
                                    <div class="mini-stat-label">of Total</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card stats-card bg-gradient-danger">
                        <div class="card-body">
                            <div class="stats-main">
                                <div class="d-flex align-items-center">
                                    <div class="stats-icon bg-white text-danger me-3">
                                        <i class="fas fa-ban"></i>
                                    </div>
                                    <div>
                                        <div class="stats-value text-white">{{ stats.inactive_users }}</div>
                                        <div class="stats-label text-white">Inactive Users</div>
                                    </div>
                                </div>
                            </div>
                            <div class="mini-stats">
                                <div class="mini-stat">
                                    <div class="mini-stat-value">{{ stats.inactive_users|floatformat:0 }}%</div>
                                    <div class="mini-stat-label">of Total</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="card stats-card bg-gradient-purple">
                        <div class="card-body">
                            <div class="stats-main">
                                <div class="d-flex align-items-center">
                                    <div class="stats-icon bg-white text-purple me-3">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div>
                                        <div class="stats-value text-white">{{ stats.inactive_60_days }}</div>
                                        <div class="stats-label text-white">Inactive > 60 Days</div>
                                    </div>
                                </div>
                            </div>
                            <div class="mini-stats">
                                <div class="mini-stat">
                                    <div class="mini-stat-value">{{ stats.inactive_60_days|floatformat:0 }}%</div>
                                    <div class="mini-stat-label">of Total</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-white py-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">User Management</h5>
                    <small class="text-muted">Total Users: {{ page_obj.paginator.count }}</small>
                </div>
                <div class="col-md-6">
                    <div class="d-flex gap-3 justify-content-md-end">
                        <form class="search-box flex-grow-1" method="get">
                            <i class="fas fa-search"></i>
                            <input type="text" class="form-control" name="search" placeholder="Search users..." value="{{ current_filters.search|default:'' }}">
                            <input type="hidden" name="status" value="{{ current_filters.status|default:'' }}">
                            <input type="hidden" name="role" value="{{ current_filters.role|default:'' }}">
                        </form>
                        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#filterModal">
                            <i class="fas fa-filter me-2"></i>Filter
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th class="ps-4">
                                <a href="?sort={% if current_filters.sort == 'username' %}-username{% else %}username{% endif %}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.role %}&role={{ current_filters.role }}{% endif %}" class="text-decoration-none text-dark">
                                    User
                                    {% if current_filters.sort == 'username' %}↑{% elif current_filters.sort == '-username' %}↓{% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?sort={% if current_filters.sort == 'first_name' %}-first_name{% else %}first_name{% endif %}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.role %}&role={{ current_filters.role }}{% endif %}" class="text-decoration-none text-dark">
                                    Full Name
                                    {% if current_filters.sort == 'first_name' %}↑{% elif current_filters.sort == '-first_name' %}↓{% endif %}
                                </a>
                            </th>
                            <th>Roles</th>
                            <th class="text-center">
                                <a href="?sort={% if current_filters.sort == 'is_active' %}-is_active{% else %}is_active{% endif %}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.role %}&role={{ current_filters.role }}{% endif %}" class="text-decoration-none text-dark">
                                    Status
                                    {% if current_filters.sort == 'is_active' %}↑{% elif current_filters.sort == '-is_active' %}↓{% endif %}
                                </a>
                            </th>
                            <th class="text-center">
                                <a href="?sort={% if current_filters.sort == 'last_login' %}-last_login{% else %}last_login{% endif %}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.role %}&role={{ current_filters.role }}{% endif %}" class="text-decoration-none text-dark">
                                    Last Login
                                    {% if current_filters.sort == 'last_login' %}↑{% elif current_filters.sort == '-last_login' %}↓{% endif %}
                                </a>
                            </th>
                            <th class="text-center">
                                <a href="?sort={% if current_filters.sort == 'days' %}-days{% else %}days{% endif %}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.role %}&role={{ current_filters.role }}{% endif %}" class="text-decoration-none text-dark">
                                    Days Since
                                    {% if current_filters.sort == 'days' %}↑{% elif current_filters.sort == '-days' %}↓{% endif %}
                                </a>
                            </th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in page_obj %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center gap-2">
                                    <div class="user-avatar">
                                        {{ user.get_initials }}
                                    </div>
                                    <div class="user-info">
                                        <h6>{{ user.username }}</h6>
                                        <small class="text-muted">{{ user.email }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="fw-medium">{{ user.get_full_name|default:"-" }}</span>
                            </td>
                            <td>
                                <div class="d-flex flex-wrap gap-2">
                                    {% for role_id, role_name, icon, color in user.roles %}
                                    <div class="d-flex align-items-center">
                                        <i class="fas {{ icon }} {{ color }} role-icon" title="{{ role_name }}"></i>
                                    </div>
                                    {% endfor %}
                                </div>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-{% if user.is_active %}success{% else %}danger{% endif %}">
                                    {{ user.is_active|yesno:"Active,Inactive" }}
                                </span>
                            </td>
                            <td class="text-center">
                                {% if user.last_login %}
                                <span class="fw-medium">{{ user.last_login|date:"M d, Y" }}</span>
                                {% else %}
                                <span class="badge bg-light text-dark">Never</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <span class="days-badge badge bg-{% if user.days_since_login == 'Today' %}success{% elif user.days_since_login == 'Yesterday' %}info{% elif user.days_since_login == 'Never' %}light{% elif user.days_since_login > 30 %}danger{% elif user.days_since_login > 7 %}warning{% else %}info{% endif %} {% if user.days_since_login != 'Never' %}text-white{% else %}text-dark{% endif %}">
                                    {{ user.days_since_login }}
                                    {% if user.days_since_login not in 'Never,Today,Yesterday' %} days{% endif %}
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="d-flex gap-2 justify-content-center">
                                    <a href="{% url 'user_detail' user.id %}" class="action-btn text-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if request.user.is_staff or request.user.is_admin %}
                                    <a href="{% url 'toggle_user_status' user.id %}" 
                                       class="action-btn {% if user.is_active %}text-danger{% else %}text-success{% endif %}"
                                       title="{% if user.is_active %}Deactivate{% else %}Activate{% endif %} User"
                                       onclick="return confirm('Are you sure you want to {% if user.is_active %}deactivate{% else %}activate{% endif %} this user?')">
                                        <i class="fas fa-{% if user.is_active %}ban{% else %}check-circle{% endif %}"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center py-5">
                                <div class="text-muted">
                                    <i class="fas fa-users fa-3x mb-3"></i>
                                    <p class="mb-0 fs-5">No users found</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        {% if page_obj.has_other_pages %}
        <div class="card-footer bg-white">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mb-0">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.role %}&role={{ current_filters.role }}{% endif %}{% if current_filters.sort %}&sort={{ current_filters.sort }}{% endif %}">First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.role %}&role={{ current_filters.role }}{% endif %}{% if current_filters.sort %}&sort={{ current_filters.sort }}{% endif %}">Previous</a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                    {% if num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item {% if page_obj.number == num %}active{% endif %}">
                        <a class="page-link" href="?page={{ num }}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.role %}&role={{ current_filters.role }}{% endif %}{% if current_filters.sort %}&sort={{ current_filters.sort }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.role %}&role={{ current_filters.role }}{% endif %}{% if current_filters.sort %}&sort={{ current_filters.sort }}{% endif %}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.role %}&role={{ current_filters.role }}{% endif %}{% if current_filters.sort %}&sort={{ current_filters.sort }}{% endif %}">Last</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<!-- Filter Modal -->
<div class="modal fade" id="filterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title fs-4">Filter Users</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form method="get" id="filterForm">
                    <input type="hidden" name="search" value="{{ current_filters.search|default:'' }}">
                    
                    <!-- Status Filter -->
                    <div class="mb-4">
                        <label class="form-label fs-5">Status</label>
                        <select class="form-select form-select-lg" name="status">
                            <option value="">All Users</option>
                            <option value="active" {% if current_filters.status == 'active' %}selected{% endif %}>Active</option>
                            <option value="inactive" {% if current_filters.status == 'inactive' %}selected{% endif %}>Inactive</option>
                        </select>
                    </div>

                    <!-- Role Filter -->
                    <div class="mb-4">
                        <label class="form-label fs-5">Role</label>
                        <select class="form-select form-select-lg" name="role">
                            <option value="">All Roles</option>
                            <option value="staff" {% if current_filters.role == 'staff' %}selected{% endif %}>Staff</option>
                            <option value="admin" {% if current_filters.role == 'admin' %}selected{% endif %}>Admin</option>
                            <option value="jedan" {% if current_filters.role == 'jedan' %}selected{% endif %}>Jedan</option>
                            <option value="hospital" {% if current_filters.role == 'hospital' %}selected{% endif %}>Hospital</option>
                            <option value="jedan_branch" {% if current_filters.role == 'jedan_branch' %}selected{% endif %}>Jedan Branch</option>
                        </select>
                    </div>

                    <!-- Last Login Days Filter -->
                    <div class="mb-4">
                        <label class="form-label fs-5">Last Login (Days)</label>
                        <div class="input-group">
                            <select class="form-select form-select-lg" name="login_condition">
                                <option value="less_than" {% if current_filters.login_condition == 'less_than' %}selected{% endif %}>Less Than</option>
                                <option value="more_than" {% if current_filters.login_condition == 'more_than' %}selected{% endif %}>More Than</option>
                                <option value="exactly" {% if current_filters.login_condition == 'exactly' %}selected{% endif %}>Exactly</option>
                            </select>
                            <input type="number" 
                                   class="form-control form-control-lg" 
                                   name="login_days" 
                                   min="0" 
                                   placeholder="Enter days"
                                   value="{{ current_filters.login_days|default:'' }}"
                                   oninput="this.value = this.value.replace(/[^0-9]/g, '')">
                        </div>
                        <small class="text-muted">Leave empty to ignore this filter</small>
                    </div>

                    <!-- Date Range Filter -->
                    <div class="mb-4">
                        <label class="form-label fs-5">Date Range</label>
                        <div class="row g-2">
                            <div class="col">
                                <input type="date" 
                                       class="form-control form-control-lg" 
                                       name="date_from" 
                                       value="{{ current_filters.date_from|default:'' }}"
                                       placeholder="From">
                            </div>
                            <div class="col">
                                <input type="date" 
                                       class="form-control form-control-lg" 
                                       name="date_to" 
                                       value="{{ current_filters.date_to|default:'' }}"
                                       placeholder="To">
                            </div>
                        </div>
                        <small class="text-muted">Filter by user creation date</small>
                    </div>

                    <!-- Sort Order -->
                    <div class="mb-4">
                        <label class="form-label fs-5">Sort Order</label>
                        <select class="form-select form-select-lg" name="sort">
                            <option value="-date_joined" {% if current_filters.sort == '-date_joined' %}selected{% endif %}>Newest First</option>
                            <option value="date_joined" {% if current_filters.sort == 'date_joined' %}selected{% endif %}>Oldest First</option>
                            <option value="-last_login" {% if current_filters.sort == '-last_login' %}selected{% endif %}>Recently Active</option>
                            <option value="last_login" {% if current_filters.sort == 'last_login' %}selected{% endif %}>Least Active</option>
                            <option value="username" {% if current_filters.sort == 'username' %}selected{% endif %}>Username (A-Z)</option>
                            <option value="-username" {% if current_filters.sort == '-username' %}selected{% endif %}>Username (Z-A)</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-danger clear-filters">Clear All Filters</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="filterForm" class="btn btn-primary">Apply Filters</button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', () => {
    // Initialize tooltips
    const tooltips = document.querySelectorAll('[title]');
    tooltips.forEach(el => new bootstrap.Tooltip(el));

    // Search on Enter key
    const searchBox = document.querySelector('.search-box input');
    if (searchBox) {
        searchBox.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchBox.closest('form').submit();
            }
        });
    }

    // Form submission handling
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        filterForm.addEventListener('submit', (e) => {
            const submitButton = filterForm.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
            }
        });
    }

    // Clear filters button
    const clearFiltersBtn = document.querySelector('.clear-filters');
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', (e) => {
            e.preventDefault();
            window.location.href = "{% url 'user_list' %}";
        });
    }

    // Date range validation
    const dateFrom = document.querySelector('input[name="date_from"]');
    const dateTo = document.querySelector('input[name="date_to"]');
    
    if (dateFrom && dateTo) {
        dateFrom.addEventListener('change', () => {
            if (dateTo.value && dateFrom.value > dateTo.value) {
                dateTo.value = dateFrom.value;
            }
        });
        
        dateTo.addEventListener('change', () => {
            if (dateFrom.value && dateTo.value < dateFrom.value) {
                dateFrom.value = dateTo.value;
            }
        });
    }

    // Numeric input validation
    const loginDays = document.querySelector('input[name="login_days"]');
    if (loginDays) {
        loginDays.addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });
    }
});
</script>
{% endblock %}
{% endblock content %}