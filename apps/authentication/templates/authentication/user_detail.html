{% extends "layouts/base.html" %}

{% block title %} User Details {% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="row">
                        <div class="col-6 d-flex align-items-center">
                            <h6 class="mb-0">User Details</h6>
                        </div>
                        <div class="col-6 text-end">
                            <a href="{% url 'user_list' %}" class="btn btn-primary btn-sm">Back to List</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            {% if user.profile_image %}
                            <img src="{{ user.profile_image.url }}" class="avatar avatar-xl rounded-circle">
                            {% else %}
                            <div class="avatar avatar-xl rounded-circle bg-gradient-secondary">
                                <i class="fas fa-user fa-2x text-white"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="text-sm font-weight-bold mb-1">Username</p>
                                    <p class="text-sm mb-3">{{ user.username }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="text-sm font-weight-bold mb-1">Full Name</p>
                                    <p class="text-sm mb-3">{{ user.get_full_name }}</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="text-sm font-weight-bold mb-1">Email</p>
                                    <p class="text-sm mb-3">{{ user.email }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="text-sm font-weight-bold mb-1">User Type</p>
                                    <p class="text-sm mb-3">{{ user.user_type }}</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="text-sm font-weight-bold mb-1">Department</p>
                                    <p class="text-sm mb-3">{{ user.get_user_group_display }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="text-sm font-weight-bold mb-1">Status</p>
                                    <p class="text-sm mb-3">
                                        {% if user.is_active %}
                                        <span class="badge badge-sm bg-gradient-success">Active</span>
                                        {% else %}
                                        <span class="badge badge-sm bg-gradient-secondary">Inactive</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="text-sm font-weight-bold mb-1">Joined Date</p>
                                    <p class="text-sm mb-3">{{ user.date_joined|date:"Y-m-d H:i" }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="text-sm font-weight-bold mb-1">Last Login</p>
                                    <p class="text-sm mb-3">{{ user.last_login|date:"Y-m-d H:i" }}</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="text-sm font-weight-bold mb-1">Redirect Page</p>
                                    <p class="text-sm mb-3">{{ user.get_redirect_display }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="text-sm font-weight-bold mb-1">Terms Confirmed</p>
                                    <p class="text-sm mb-3">
                                        {% if user.terms_confirmed %}
                                        <span class="badge badge-sm bg-gradient-success">Yes</span>
                                        {% else %}
                                        <span class="badge badge-sm bg-gradient-secondary">No</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %} 