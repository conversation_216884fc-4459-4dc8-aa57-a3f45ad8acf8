# -*- encoding: utf-8 -*-
"""
Copyright (c) 2019 - present AppSeed.us
"""

from django.urls import path
from .views.logout_view import CustomLogoutView
from .views.user_views import user_list, user_detail, toggle_user_status

from apps.authentication.views import (
                                    login_view,
                                    register_user,
                                    add_staff,
                                    Staffs_ListView,
                                    UserDetailView,
                                    UserDetailEditView,
                                    ChangePasswordView,
                                    NoPermissionView,

                                )


urlpatterns = [
    path('', login_view, name="login"),
    path('register/', register_user, name="register"),
    
    path('logout/', CustomLogoutView.as_view(), name='logout'),
    #! Add Staff and List Staffs 
    #path('add_staff/', AddStaffView.as_view(), name="add_staff"),
    path("add_staff/", add_staff, name="add_staff"), 
    
    path("list_staff/", Staffs_ListView.as_view(), name="list_staff"),
    
    #! User Employee Details
    path("user_detail/", UserDetailView.as_view(), name="user_detail"),
    
    #! edit or update user details
    path("user_detail_edit/<int:pk>", UserDetailEditView.as_view(), name="user_detail_edit"),
    # path("user_detail_edit", UserDetailEditView.as_view(), name="user_detail_edit"),
    
    
    #! Password Change 
    path("change_password", ChangePasswordView.as_view(), name="change_password"),
    
    
    path('no-permission/', NoPermissionView.as_view(), name='no_permission_page'),
    
    path('users/', user_list, name='user_list'),
    path('users/<int:user_id>/', user_detail, name='user_detail'),
    path('users/<int:user_id>/toggle-status/', toggle_user_status, name='toggle_user_status'),
]
