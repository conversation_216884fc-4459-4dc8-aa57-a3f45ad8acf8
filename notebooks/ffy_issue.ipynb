{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0c612c6c-f87c-4367-ba84-9f16f0892a9f", "metadata": {"tags": []}, "outputs": [], "source": ["import os\n", "os.chdir(\"../\")"]}, {"cell_type": "code", "execution_count": 2, "id": "7ed231cf-3226-4f7c-a6f8-720f52f70b7b", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Errno 111] Connection refused\n", "Development Version Running **11\n"]}], "source": ["import os, glob, shutil\n", "import sys \n", "import django \n", "import re\n", "\n", "import pandas as pd\n", "from django.contrib.auth.hashers import make_password\n", "\n", "\n", "\n", "sys.path.insert(0, os.path.join(sys.path[0], '..'))\n", "os.environ.setdefault(\"DJANGO_SETTINGS_MODULE\", \"core.settings\")\n", "django.setup()\n", "\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\" # for jupyter\n", "\n", "\n", "#import pandas as pd\n", "# from apps.authentication.models import User, Country, <PERSON><PERSON><PERSON>ress, SiAddress\n", "# from apps.genomom.models import PatientInfo, SampleInfo, FF_info, Read_Counts_GC , Read_Ratio, Z_score,ServiceName\n", "\n", "# from apps.hospital.models import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HospitalInfo, DoctorInfo\n", "\n", "from apps.genomom.models import SampleInfo, PatientInfo, Read_Counts_GC, ServiceName\n", "from apps.hospital.models import HospitalIn<PERSON>, <PERSON><PERSON>, DoctorInfo"]}, {"cell_type": "code", "execution_count": 12, "id": "95259039-0965-4a27-8522-3e33204ce74b", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["49\n", "43\n"]}], "source": ["from django.db.models import Q\n", "\n", "# Step 1: Initial Filter\n", "all_samples = SampleInfo.objects.filter(\n", "    is_active=True, \n", "    result_status=True, \n", "    report_publish=True,\n", "    patient__fetus_number=\"Single\",\n", "    ff_info__ff_y__lt= 5 ,  ff_info__ff_y__gt= 2\n", ")\n", "\n", "print(len(all_samples))\n", "\n", "# Step 2: Apply OR condition for gender filter\n", "all_samples = all_samples.filter(\n", "    Q(gender=1) | Q(patient__doctor__sex_info=True)\n", ")\n", "\n", "print(len(all_samples))\n"]}, {"cell_type": "code", "execution_count": 13, "id": "032d8534-0d99-485b-acb9-c768939dda95", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AGC2403N02558_R1_V1 2.62 pippin\n", "AGC2403N03327_R1_V1 3.721 pippin\n", "AGC2404N04770_R1_V1 2.304 pippin\n", "AGC2405N05395_R1_V1 2.017 pippin\n", "AGC2406N06475_R1_V1 4.667 pippin\n", "AGC2406N07151_R1_V1 4.926 pippin\n", "AGC2407N07784_R1_V1 4.259 pippin\n", "AGC2407N07895_R1_V1 4.627 pippin\n", "AGC2407N08485_R1_V4 4.898 pippin\n", "AGC2408N09317_R1_V1 3.117 pippin\n", "AGC2408N09928_R1_V1 3.759 pippin\n", "AGC2408N09712_R1_V4 4.93 pippin\n", "AGC2408N09978_R1_V1 3.837 pippin\n", "AGC2408N10052_R1_V1 2.535 pippin\n", "AGC2409N11306_R1_V1 4.848 pippin\n", "AGC2409N11987_R1_V1 4.677 pippin\n", "AGC2503N04503_R1_V1 2.084 pippin\n", "AGC2411N14258_R1_V1 2.851 pippin\n", "AGC2411N14233_R1_V1 4.871 pippin\n", "AGC2411N14673_R1_V1 2.954 pippin\n", "AGC2411N14859_R1_V1 2.125 pippin\n", "AGC2412N15343_R1_V1 3.262 pippin\n", "AGC2412N16705_R1_V1 4.787 pippin\n", "AGC2501N00599_R1_V1 4.887 pippin\n", "AGC2501N00396_R1_V4 4.415 pippin\n", "AGC2501N00455_R1_V4 4.948 pippin\n", "AGC2502N01873_R1_V1 2.664 pippin\n", "AGC2502N01914_R1_V1 2.666 pippin\n", "AGC2502N01821_R1_V4 2.605 pippin\n", "AGC2502N02357_R1_V1 4.515 whole\n", "AGC2502N03031_R1_V1 3.718 whole\n", "AGC2502N03062_R1_V5 4.75 whole\n", "AGC2503N03776_R1_V1 3.922 pippin\n", "AGC2503N04233_R1_V1 3.967 pippin\n", "AGC2503N04331_R1_V1 4.587 pippin\n", "AGC2503N04892_R1_V1 3.821 pippin\n", "AGC2503N05099_R1_V1 2.823 pippin\n", "AGC2504N05420_R1_V1 4.821 pippin\n", "AGC2504N05372_R1_V1 2.257 pippin\n", "AGC2504N05941_R1_V1 4.801 pippin\n", "AGC2504N06096_R1_V1 2.1 pippin\n", "AGC2504N06429_R1_V1 3.44 pippin\n", "AGC2504N06604_R1_V1 2.21 pippin\n"]}], "source": ["for sample in all_samples :\n", "    print(sample.tgc_s_id , sample.ff_info.ff_y , sample.sample_dna_qc.ref_type )"]}, {"cell_type": "code", "execution_count": null, "id": "d7b5653f-4030-4df1-970f-4b4fc1cb6db3", "metadata": {}, "outputs": [], "source": [" "]}, {"cell_type": "code", "execution_count": null, "id": "4be96db7-ae3a-46c3-ae9b-8d80e8c5c864", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9e01e8b9-889a-4a7c-858c-0e97f1e0e190", "metadata": {"tags": []}, "outputs": [], "source": ["\n", "# Step 3: Convert to DataFrame with renamed columns\n", "data = all_samples.values(\n", "    \"patient__tgc_p_id\",  # Original field\n", "    \"tgc_s_id\",  # Original field\n", "    \"ff_info__ff_y\",  # Original field\n", "    \"patient__fetus_number\",\n", ")\n", "\n", "df = pd.DataFrame.from_records(data)\n", "\n", "\n", "\n", "df.rename(columns={\n", "    \"patient__tgc_p_id\": \"PID\",\n", "    \"tgc_s_id\": \"S ID\",\n", "    \"ff_info__ff_y\": \"FFY\"\n", "}, inplace=True)\n", "\n", "# Step 4: Create the Gender column based on FFY value\n", "def determine_gender(ffy):\n", "    if ffy > 5:\n", "        return \"Male\"\n", "    elif f<PERSON> < 2:\n", "        return \"Female\"\n", "    else:\n", "        return \"Unknown\"\n", "\n", "df[\"Gender\"] = df[\"FFY\"].apply(determine_gender)\n", "\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c9c91bc4-c0b0-4ee4-a454-10d6dbb77e78", "metadata": {"tags": []}, "outputs": [], "source": ["df.Gender.value_counts().to_dict() "]}, {"cell_type": "code", "execution_count": null, "id": "0090cc07-cdc5-4e75-a503-e57b2bbe42b3", "metadata": {"tags": []}, "outputs": [], "source": ["df.Gender.value_counts().to_dict() "]}, {"cell_type": "code", "execution_count": null, "id": "640199ca-bbf7-4ac8-a739-4032f7e5d2fc", "metadata": {"tags": []}, "outputs": [], "source": ["( 7227 - 2 + 4 )"]}, {"cell_type": "code", "execution_count": null, "id": "7132cd70-2637-4751-b777-ba4a0ba8066e", "metadata": {"tags": []}, "outputs": [], "source": [" ( 7225 +7764 ) * 100 /  ( 2+4  + 7225 +7764  )"]}, {"cell_type": "code", "execution_count": null, "id": "8e220f50-d796-4678-89e5-1490ab5acacb", "metadata": {"tags": []}, "outputs": [], "source": ["2+4  + 7225 +7764 "]}, {"cell_type": "code", "execution_count": null, "id": "8ee357f2-5fdb-4552-9f3e-5d4b1bd249c0", "metadata": {"tags": []}, "outputs": [], "source": ["(32 * 100)/ 15027"]}, {"cell_type": "code", "execution_count": null, "id": "8ce74619-3b7c-4d07-8668-2d4de8c7d688", "metadata": {"tags": []}, "outputs": [], "source": ["df[df.Gender != \"Unknown\" ].shape  , df[df.Gender == \"Unknown\" ].shape"]}, {"cell_type": "code", "execution_count": null, "id": "0504e8f6-eee9-40be-9e1e-4feedfb50c01", "metadata": {"tags": []}, "outputs": [], "source": ["( 7223 +7768 )  "]}, {"cell_type": "code", "execution_count": null, "id": "1391878c-3db3-41c0-a0e1-4c09f506366f", "metadata": {"tags": []}, "outputs": [], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d16db5f1-a55d-4e4e-b845-1383c64912e1", "metadata": {"tags": []}, "outputs": [], "source": ["## CNV filter final adding service size and filtered size"]}, {"cell_type": "markdown", "id": "f593f20b-b1f7-4b02-b22b-1b74d66c315e", "metadata": {}, "source": ["### Finally Assign Now"]}, {"cell_type": "markdown", "id": "a40f0733-4993-4690-a14b-116240adf752", "metadata": {}, "source": ["### Filter Only threquired data "]}, {"cell_type": "code", "execution_count": null, "id": "d56be379-e13c-4d7e-923b-ea4c28a71b8c", "metadata": {"tags": []}, "outputs": [], "source": ["# Query the SampleInfo model with the specified conditions\n", "all_samples_to_change = SampleInfo.objects.filter(\n", "    entry_date__gt=cutoff_date,\n", "    service_type__service_name=\"플러스 (제노맘아이)\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e080bbe7-8953-4cf5-a8c3-d884f47ff09f", "metadata": {"tags": []}, "outputs": [], "source": ["from django.db import transaction"]}, {"cell_type": "code", "execution_count": null, "id": "7c7e4b44-0b9a-4080-b2d5-698cb864f931", "metadata": {"tags": []}, "outputs": [], "source": ["# Step 4: Update only the filtered samples\n", "with transaction.atomic():\n", "    for sample in all_samples_to_change:\n", "        print(sample.tgc_s_id , \"before changing service_name \", sample.service_type.service_name , end= ', ' ) \n", "        \n", "        # Assign the new service type to each filtered sample\n", "        sample.service_type = service_to_assign\n", "        sample.save()\n", "        \n", "        # Save the sample to apply the change in the database\n", "        print(\"after changin service name \" ,  sample.service_type.service_name  )\n", "        \n", "        "]}, {"cell_type": "code", "execution_count": null, "id": "14ff9b17-9130-4593-b81a-1eae069917c1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8a37bb61-b91e-4a67-a22c-aaf2882fefae", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2297581f-2e6b-48be-85e8-b4b4a44e296a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ee901326-b3cf-4cb1-9fc6-6acd53c1c2a2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4194dee4-fd4b-445a-9f5a-db22ddcbf506", "metadata": {"tags": []}, "outputs": [], "source": ["sample = SampleInfo.objects.filter(tgc_s_id =\"AGC2408N10327_R1_V1\" ).first()"]}, {"cell_type": "code", "execution_count": null, "id": "3d7edf62-251b-49ac-98b4-46747b927c31", "metadata": {"tags": []}, "outputs": [], "source": ["sample"]}, {"cell_type": "code", "execution_count": null, "id": "c2448e10-0d9d-45ea-a4f7-24fa43525d98", "metadata": {"tags": []}, "outputs": [], "source": ["cnv_final_path = os.path.join(os.path.join(\"media\",sample.sample_path(), '*final.cnv'))"]}, {"cell_type": "code", "execution_count": null, "id": "0564b217-9d8d-4291-a4d7-c677512ddee8", "metadata": {"tags": []}, "outputs": [], "source": ["cnv_final_path = glob.glob(cnv_final_path)"]}, {"cell_type": "code", "execution_count": null, "id": "a9dfeaf9-d332-4d1d-9cfb-f4ded6b67a6b", "metadata": {"tags": []}, "outputs": [], "source": ["cnv_final_path"]}, {"cell_type": "code", "execution_count": null, "id": "5d49527f-e228-4853-a352-adaf62d1c464", "metadata": {"tags": []}, "outputs": [], "source": ["CNV_result = pd.read_table(cnv_final_path[0], sep = \"\\t\")"]}, {"cell_type": "code", "execution_count": null, "id": "38483db0-5209-4c13-afcf-9d9604df9fcb", "metadata": {"tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c4b3c2ef-8a85-4171-b1cf-6f40b77e1fd4", "metadata": {"tags": []}, "outputs": [], "source": ["#!  passing final cnv to merge with our 149 cnv service list \n", "CNV_del = pd.read_excel('dummy/datas/cnvs_tgc/disease_info_del.xlsx')\n", "CNV_duple = pd.read_excel('dummy/datas/cnvs_tgc/disease_info_duple.xlsx')\n"]}, {"cell_type": "code", "execution_count": null, "id": "1494eb41-1f23-4846-96a2-a073fc4a5533", "metadata": {"tags": []}, "outputs": [], "source": ["CNV_del.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6cc4d068-0496-4869-8008-db6cf692c63d", "metadata": {"tags": []}, "outputs": [], "source": ["cnv_final_path"]}, {"cell_type": "code", "execution_count": null, "id": "5d44f9a9-9a48-4a89-bf3f-c460a68d69da", "metadata": {"tags": []}, "outputs": [], "source": ["final_result = pd.DataFrame() \n", "\n", "for index in CNV_result.index :\n", "    # run each line and check weather filtered is exist in our range or not \n", "    sample = CNV_result.loc[[index]] \n", "\n", "    #! Extract common values chr number , start & end position of single row \n", "    sample_chr = sample.loc[index, \"Chr\" ]\n", "    sample_start = sample.loc[index , \"Start\" ] \n", "    sample_end   = sample.loc[ index , \"End\" ]\n", "    sample_copy_ratio = sample.loc[index, \"CopyRatio\"] # greater than 1 is duplication , and less is deletion , ignore x, y now \n", "\n", "              \n", "    if sample_copy_ratio < 1:\n", "        select_df = CNV_del[CNV_del['chr'] == sample_chr ] \n", "    else:\n", "        select_df = CNV_duple[CNV_duple['chr'] == sample_chr ]        \n", "        \n", "        \n", "    # now loop in selected df and append in sample \n", "    for i, row in select_df.iterrows():\n", "        if row.start <= sample_end and row.end >= sample_start :\n", "            #! get intersection size\n", "            if row.start >  sample_start  :\n", "                start_posistion = row.start\n", "            else:\n", "                start_posistion = sample_start\n", "            \n", "            if row.end < sample_end :\n", "                end_position = row.end \n", "            else:\n", "                end_position = sample_end\n", "                \n", "            filterd_size = end_position - start_posistion            \n", "            sample[\"disease\"] = row.disease  #! Sample is a single Row             \n", "            sample[\"Intersection_Size\"] = round( filterd_size  / 10_00_000,2 )  #! Sample is a single Row     \n", "            \n", "                    \n", "            \n", "            \n", "            \n", "            \n", "            sample[\"Service_start\"] = row.start\n", "            sample[\"Service_end\"] = row.end            \n", "            sample[\"Service_size\"] = row.end - row.start            \n", "            \n", "            sample[\"Service_size\"] = round(sample[\"Service_size\"] / 10_00_000,2 ) \n", "            \n", "            \n", "            #! Converting to Mb \n", "            sample[\"Intersection_Size\"] = sample[\"Intersection_Size\"].astype(str) + \"Mb\"\n", "            sample[\"Service_size\"]  = sample[\"Service_size\"].astype(str).astype(str) + \"Mb\" \n", "            \n", "            \n", "            final_result = pd.concat([ final_result, sample ] , ignore_index= True )\n", "            \n", "# in final result M replace with <PERSON>b\n", "final_result[\"Length\"] = final_result[\"Length\"].astype(str).str.replace(\"M\",\"Mb\").str.replace('K', 'Kb')"]}, {"cell_type": "code", "execution_count": null, "id": "72cfecdf-e633-42a7-806d-8d919409a7ee", "metadata": {"tags": []}, "outputs": [], "source": ["final_result.iloc[:, [-5,-4,-3,-2,-1,0,1,2,3,4,5,6,7,8,9,10]]"]}, {"cell_type": "code", "execution_count": null, "id": "199878e7-8f6c-445c-b8c1-eed77242e87e", "metadata": {"tags": []}, "outputs": [], "source": ["final_result"]}, {"cell_type": "code", "execution_count": null, "id": "a3d7b4be-9c9f-4b59-b71a-38a869bdce35", "metadata": {"tags": []}, "outputs": [], "source": ["sample"]}, {"cell_type": "code", "execution_count": null, "id": "609c2b8d-682e-4f41-8e87-212e384bee15", "metadata": {"tags": []}, "outputs": [], "source": ["# for doctor in all_doctors :\n", "    # print(doctor.full_name_eng, doctor.full_name )\n", "    # doctor.full_name_eng = doctor.full_name\n", "    # doctor.save()"]}, {"cell_type": "code", "execution_count": null, "id": "3ff1bff6-de27-40ba-9f02-70f9f08172c4", "metadata": {"tags": []}, "outputs": [], "source": ["CNV_result"]}, {"cell_type": "code", "execution_count": null, "id": "11e5c963-30a2-4f1b-a86f-d80d02d8ddac", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "235dde17-7bb0-45d3-b747-4d1fce21c38a", "metadata": {"tags": []}, "outputs": [], "source": ["cnv_final_path"]}, {"cell_type": "code", "execution_count": null, "id": "f5d979e0-e4f2-4148-8629-df9986f8c04e", "metadata": {"tags": []}, "outputs": [], "source": ["# delete final filtered cnv files "]}, {"cell_type": "code", "execution_count": null, "id": "b49ab6de-9173-4d86-9dd1-14cebd07a5f2", "metadata": {"tags": []}, "outputs": [], "source": ["all_samples = SampleInfo.objects.filter(sample_process = \"6\" , is_active = True ) # '1차 판독'\n", " \n", "for sample in all_samples :\n", "    # print(sample , sample.get_sample_process_display() )\n", "    \n", "    cnv_final_path = os.path.join(os.path.join(\"media\",sample.sample_path(), f'{sample.tgc_s_id}_final_filtered.cnv'))\n", "    print(cnv_final_path)\n", "    if len(cnv_final_path) ==0 :\n", "        print(\"**\")\n", "        continue\n", "    cnv_final_path = glob.glob(cnv_final_path)[0]\n", "    \n", "\n", "    \n", "    #! delete this cnv_final path \n", "    # Delete the file\n", "    if os.path.exists(cnv_final_path):\n", "        os.remove(cnv_final_path)\n", "        print(f\"Deleted: {cnv_final_path}\")\n", "    else:\n", "        print(f\"File not found: {cnv_final_path}\")\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "5ee10451-33d3-4203-b90b-52ff6e1c4d4d", "metadata": {"tags": []}, "outputs": [], "source": ["cnv_final_path"]}, {"cell_type": "code", "execution_count": null, "id": "c737a653-ff5f-4d73-af96-34316bebe13e", "metadata": {"tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ed32e5c5-7759-4fa9-9fbb-d6f682cb7239", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8764d062-7c7e-47f4-b123-38fe56a91fba", "metadata": {"tags": []}, "outputs": [], "source": ["all_hospitals = HospitalInfo.objects.all()"]}, {"cell_type": "code", "execution_count": null, "id": "cdb5f735-6fb8-4052-a44e-ef55244ef390", "metadata": {"tags": []}, "outputs": [], "source": ["for hospital in all_hospitals:\n", "    # print(hospital.hospital_name_eng , hospital.hospital_name )\n", "    # hospital.hospital_name_eng =  hospital.hospital_name\n", "    # hospital.save()\n", "    \n", "    print(hospital.address_full_eng, hospital.do_address, hospital.si_address, hospital.last_address )\n", "    \n", "#     hospital.address_full_eng = f\"{hospital.do_address} {hospital.si_address} {hospital.last_address}\"\n", "#     hospital.save()"]}, {"cell_type": "code", "execution_count": null, "id": "6d9ebaf7-c7b2-4da9-a75c-5b00b64432b5", "metadata": {}, "outputs": [], "source": ["# 6F, CEO Office Building, Buti Town,Mahatma Gandhi Street, 15th Khoroo,Khan-Uul District, Ulaanbaatar, Mongolia 경기도 수원시 영통구 창룡대로 260 810호"]}, {"cell_type": "code", "execution_count": null, "id": "4ea8cd71-01f2-4700-bb35-525784bf2a6d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1ead7a96-9f32-4d1c-b0d1-fe8a14cdb232", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b6721d1e-3b4e-4098-ba41-050dbe51e888", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "70e60d3e-afd8-4669-a078-47eea405133f", "metadata": {"tags": []}, "outputs": [], "source": ["# for hospital in all_hospitals:\n", "#     if hospital.jedan.jedan_name == \"SML\":\n", "#         print(hospital.hospital_name, hospital.jedan.jedan_name, hospital.price_lite, hospital.price_std,hospital.price_plus,hospital.price_twin )\n", "        \n", "# #         hospital.price_lite = 160000\n", "        \n", "# #         hospital.price_std  = 240900\n", "# #         hospital.price_twin = 240900\n", "        \n", "#         hospital.price_plus = 260_000\n", "        \n", "#         hospital.save()\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "9eb72162-d8a2-4ae5-b860-b1a8cd535c79", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b81d94ce-8d16-4898-b929-1d2e625bf2b6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2d711740-06b1-4567-ad30-8ed218b8cae3", "metadata": {"tags": []}, "outputs": [], "source": ["# all_docs = DoctorInfo.objects.all()"]}, {"cell_type": "code", "execution_count": null, "id": "9473cafc-2526-4000-818e-f633e640c556", "metadata": {"tags": []}, "outputs": [], "source": ["# for doctor in all_docs:\n", "#     if doctor.hospital.hospital_name ==  \"지축 라움 산부인과 의원\" :#\"용인제일산부인과\": #\"서울 라헬 여성 의원\" : \n", "#         print(doctor.hospital.hospital_name, doctor)\n", "    \n", "#         doctor.sex_info = True \n", "#         doctor.male_sex = '1'\n", "#         doctor.female_sex = '2'\n", "#         doctor.save()\n", "    \n", "# #     print(doctor.male_sex, doctor.female_sex)"]}, {"cell_type": "code", "execution_count": null, "id": "3a83a657-e9bd-4bf8-93c1-b71ac8ae243a", "metadata": {"tags": []}, "outputs": [], "source": ["# for doctor in all_docs:\n", "    \n", "    \n", "#     doctor.male_sex = '+'\n", "#     doctor.female_sex = '-'\n", "#     doctor.save()\n", "    \n", "#     print(doctor.male_sex, doctor.female_sex)"]}, {"cell_type": "code", "execution_count": null, "id": "fefd8ed2-92f1-4917-a674-f3398726e583", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "24f98046-8298-4ec3-9f3d-5e70fed77e3b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "334ac69c-cb0c-4413-bceb-dd338987d538", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c00d32b7-2884-487b-b40b-3f05fc73f2e2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16ee40b1-999c-4d94-86b9-fbf7b6a7473e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "84afa7b2-ac4f-4a13-af7b-867cae7948d5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fd6da453-eb7f-402f-b552-4f3c037658e6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "83cc3337-9dc8-40ff-bc2c-b8e4002c9d8c", "metadata": {"tags": []}, "outputs": [], "source": ["# for sample in all_samples:\n", "#     if sample.sample_process in ['7','8', '9'] :\n", "        \n", "#         sample_dir = os.path.join(current_path, 'media', sample.sample_path())\n", "        \n", "#         try:\n", "#             os.chdir(sample_dir)\n", "#             unique_csv = glob.glob(sample.tgc_s_id + '*_AIM.xlsx')\n", "            \n", "#             unique_csv = pd.read_excel(unique_csv[0])\n", "#             rc_gc = Read_Counts_GC.objects.get(sample = sample)\n", "            \n", "#             rc_gc.total_raw_reads = int(unique_csv.loc[unique_csv.Info == \"Total_Raw_Read\", \"Value\" ][0])\n", "            \n", "#             rc_gc.wgc =  float(unique_csv.loc[unique_csv.Info == \"Sample_GC\", \"Value\" ][2])\n", "            \n", "#             rc_gc.save()\n", "            \n", "#             ts_result_path = \"./TS_result\"\n", "#             try:\n", "#                 df = pd.read_table(ts_result_path)\n", "#                 rc_gc.barcode   = df.loc[0, \"Barcode\"]\n", "#                 rc_gc.total_bases =  df.loc[0, \"Total_Bases\"]\n", "#                 rc_gc.q20_bases  = df.loc[0, \"Q20_Bases\"]\n", "#                 rc_gc.reads      = df.loc[0, \"Reads\"]\n", "#                 rc_gc.mrl        = df.loc[0, \"Mean_Read_Length\"]\n", "#                 rc_gc.ts_report  = df.loc[0, \"TS_Report\"]\n", "#                 rc_gc.save()\n", "#             except :\n", "#                 #print(sample.tgc_s_id , \"Not Done\"  )\n", "#                 pass\n", "\n", "            \n", "                                 \n", "#         except Exception as err:\n", "#             print(err, sample.tgc_s_id )\n", "        \n", "        \n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "6da205e7-f6c6-44fd-8b11-010bb6ed56a4", "metadata": {"tags": []}, "outputs": [], "source": ["sample.tgc_s_id"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}