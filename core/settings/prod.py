from .logging import *
from .base import *
import socket
#put domain

# WEASYPRINT_BASEURL = 'http://*************/'

# hostname = socket.gethostname()

# domain = "https://gm.genomecare.net/"  # ! Real Server  IP 주소
# domain = "https://gm2.genomecare.net/"  # ! Real Server  IP 주소



# # Check if the hostname matches 'genomom_lims'
# if hostname == 'genomom_lims':
#     WEASYPRINT_BASEURL = 'https://gm.genomecare.net/'  # Update with your actual URL
# elif hostname == 'genomom-service' :
#     WEASYPRINT_BASEURL = 'https://gm2.genomecare.net/'  # Default URL if hostname doesn't match
# else:
#     WEASYPRINT_BASEURL = 'http://127.0.0.1:8000/'


# #! in celery iamges and others fields are not getting so changing 
# Configure the static files URL and directory
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'static')


# MEDIA_URL = '/media/'
#MEDIA_URL = '/tgc_media/'


#! in celery iamges and others fields are not getting so changing
# STATIC_URL = f'https://{domain}/static/'
# MEDIA_URL = f'https://{domain}/media/'
DEBUG = False


