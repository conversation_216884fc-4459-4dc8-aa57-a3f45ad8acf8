# -*- encoding: utf-8 -*-
"""
Copyright (c) 2019 - present AppSeed.us
"""

from django.core.mail import <PERSON><PERSON><PERSON>er<PERSON>rror, send_mail
import random
import string
import os, sys
from decouple import config

from unipath import Path
from dotenv import load_dotenv

#! celery config from /etc/conf.d/celery 
from .celery_config import *

from .summer_note_config import * 

# from .sentary_config import *


sys.path.append('/BiO/github/genomom') #! geno_ai pipeline exist here 

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
# BASE_DIR = Path(__file__).parent
# /BiO/github/genomom_lims
BASE_DIR = Path(__file__).resolve().parent.parent.parent


# BASE_DIR = Path(__file__).resolve().parent.parent
CORE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Load .env file from two steps upward
ENV_FILE = os.path.join(BASE_DIR, '.env')

load_dotenv(dotenv_path=ENV_FILE)

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get('SECRET_KEY')


if not SECRET_KEY:
    SECRET_KEY = ''.join(random.choice(string.ascii_lowercase)
                         for i in range(32))


# load production server from .env

ALLOWED_HOSTS = ['gm.genomecare.net', "www.gm.genomecare.net", "*************", "**************",
                 'gm2.genomecare.net', "www.gm.genomecare.net",  "**************",
                 'localhost', config('SERVER', default='127.0.0.1'), ]



# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',

    #  Apps other than django Developed by Krishna
    'apps.home',  # Enable the inner home (home)
    'apps.genomom',
    'apps.hospital',
    'apps.authentication',
    'apps.dashboards',
    'apps.crm',
    #'apps.wms',
    
    'apps.genobenet',
    #!'apps.genofind',

    # 3rd party apps not developed by Krishna
    'simple_history', # Enable History Records
    'import_export',
    'axes',
    
    'django_celery_results',
    #'django_summernote',#! done below  with + 
]

INSTALLED_APPS += ('django_summernote', )

AUTHENTICATION_BACKENDS = [
    # AxesStandaloneBackend should be the first backend in the AUTHENTICATION_BACKENDS list.
    'axes.backends.AxesStandaloneBackend',

    # Django ModelBackend is the default authentication backend.
    'django.contrib.auth.backends.ModelBackend',
]



MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',

    #'whitenoise.middleware.WhiteNoiseMiddleware',
    #! Simple Record record for History in Data Base
    'simple_history.middleware.HistoryRequestMiddleware',

    # ! axes always in last
    'axes.middleware.AxesMiddleware',
]

# Set session timeout to HOUR X MINUTE * SECONDS (20 * 60 * 60 seconds)
SESSION_COOKIE_AGE =  8*60*60
# Ensure that the session doesn't expire at browser close
SESSION_EXPIRE_AT_BROWSER_CLOSE = True

# Set session to expire after a fixed time (10 hours)
SESSION_SAVE_EVERY_REQUEST = False

# !! For Axes
# Block only on username
AXES_ONLY_USER_FAILURES=True
AXES_FAILURE_LIMIT = lambda *args: 5 #! changed to 10 By Krish
AXES_COOLOFF_TIME = 1 # ! After 1 Hour auto reset
#for Axes reverse proxy settings 
AXES_PROXY_COUNT : 1
AXES_META_PRECEDENCE_ORDER = ('HTTP_X_FORWARDED_FOR', 'X_FORWARDED_FOR', 'REMOTE_ADDR')


#SIMPLE_HISTORY_REVERT_DISABLED = True






AUTH_USER_MODEL = 'authentication.User'
ROOT_URLCONF = 'core.urls'
LOGIN_REDIRECT_URL = "/"  # Route defined in home/urls.py
LOGOUT_REDIRECT_URL = "/"  # Route defined in home/urls.py
TEMPLATE_DIR = os.path.join( BASE_DIR , "apps/templates")  # ROOT dir for templates

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [TEMPLATE_DIR],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'core.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DB_ENGINE = os.getenv('DB_ENGINE', None)
DB_USERNAME = os.getenv('DB_USERNAME', None)
DB_PASS = os.getenv('DB_PASS', None)
DB_HOST = os.getenv('DB_HOST', None)
DB_PORT = os.getenv('DB_PORT', None)
DB_NAME = os.getenv('DB_NAME', None)


# print(DB_ENGINE, DB_USERNAME, DB_PASS, DB_HOST, DB_PORT, DB_NAME, "**" )

if DB_ENGINE and DB_NAME and DB_USERNAME:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.' + DB_ENGINE,
            'NAME': DB_NAME,
            'USER': DB_USERNAME,
            'PASSWORD': DB_PASS,
            'HOST': DB_HOST,
            'PORT': DB_PORT,
        },
    }
else:
    print("First set Data base please ")













#! for summer Note
X_FRAME_OPTIONS = 'SAMEORIGIN'

# Password validation
# https://docs.djangoproject.com/en/3.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Rename Admin page names

FIRST_DAY_OF_WEEK = 1 # Monday # use for date calendar 

# Internationalization
# https://docs.djangoproject.com/en/3.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Seoul'

# Change the default DATE_FORMAT and DATETIME_FORMAT
DATE_FORMAT = 'm월 d일'
DATETIME_FORMAT = 'm월 d일, P'

USE_I18N = True

USE_L10N = True

USE_TZ = True
# USE_TZ = False

#############################################################
# SRC: https://devcenter.heroku.com/articles/django-assets

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/1.9/howto/static-files/


DATA_UPLOAD_MAX_NUMBER_FIELDS = 10240 # Data base fields to change 

# Location setup for media(images/video)
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')



# Extra places for collectstatic to find static files.
STATICFILES_DIRS = (
    os.path.join(BASE_DIR, 'apps/static'),
)

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR , 'static')

#! For Email Settings
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_USE_TLS = True
EMAIL_PORT = 587
EMAIL_HOST_USER = "<EMAIL>"  # sender's email-id
# password associated with above email-id
EMAIL_HOST_PASSWORD = "dqxi wbgi wkpo tomk"



#! for weasy print 
WEASYPRINT_BASEURL = os.environ.get('WEASYPRINT_BASEURL')
#! apply weasy print on the basis of web address from .env file
if not WEASYPRINT_BASEURL:
    WEASYPRINT_BASEURL = 'http://127.0.0.1:8000/'
    
    
# while testing comment these lines 

# #! Deployment check recommendation
# SECURE_HSTS_SECONDS = 31536000
# SECURE_HSTS_INCLUDE_SUBDOMAINS = True
# SECURE_HSTS_PRELOAD = True
# SECURE_SSL_REDIRECT = True

# SESSION_COOKIE_SECURE = True
# CSRF_COOKIE_SECURE = True
# X_FRAME_OPTIONS = 'DENY'


try:
    # send_mail("test", "test", '<EMAIL>', ["<EMAIL>",] )
    
    subject = 'LIMS Server re-started '
    message = f'Hi krishna , LIMS Server is restarted by some one'
    email_from = EMAIL_HOST_USER
    recipient_list = ['<EMAIL>', ]
    send_mail( subject, message, email_from, recipient_list )
    
except Exception as err:
    print(err)
    
    
from .logging import *