
import os
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent  # settings.py 위치 기준


BASE_DIR = Path(__file__).resolve().parent.parent.parent
LOG_DIR = os.path.join(BASE_DIR, 'logs')

if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)





LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "[{asctime}] {levelname} {name} - {message}",
            "style": "{",
        },
        "simple": {
            "format": "{levelname} {message}",
            "style": "{",
        },
    },
    "handlers": {
        "warning_file": {
            "level": "WARNING",
            "class": "logging.FileHandler",
            "filename": "/var/log/celery/celery_warning.log",
            "formatter": "verbose",
        },
        "error_file": {
            "level": "ERROR",
            "class": "logging.FileHandler",
            "filename": "/var/log/celery/celery_error.log",
            "formatter": "verbose",
        },
        "hospital_file": {
            "level": "INFO",
            "class": "logging.FileHandler",
            "filename": os.path.join(BASE_DIR, "logs/hospitals_create_edit/hospital_changes.log"),
            "formatter": "verbose",
        },
    },
    "loggers": {
        "celery": {
            "handlers": ["warning_file", "error_file"],
            "level": "WARNING",
            "propagate": True,
        },
        "hospital_logger": {
            "handlers": ["hospital_file"],
            "level": "INFO",
            "propagate": False,
        },
    },
}
