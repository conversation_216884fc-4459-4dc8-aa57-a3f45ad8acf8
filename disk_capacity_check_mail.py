import os 
import subprocess 

def get_disk_usage(path) :
    """
    this will check the disk capacity
    need to install dfc in ubuntu 
    """
    dfc_output = subprocess.check_output(['dfc' , path ])
    #print(dfc_output)
    usage_line = dfc_output.decode().splitlines()[1]
    usage_percent = int(usage_line.split()[-2][:-1])  # Extract the percentage used
    return usage_percent

if __name__ == '__main__':
    target_path = '/BiO'  # Change this to your desired path
    threshold = 70  # Set your desired threshold

    disk_usage = get_disk_usage(target_path)

    if disk_usage >= threshold:
        # You can implement your alerting mechanism here, e.g., send an email
        print(f"Disk usage for {target_path} is {disk_usage}%. Sending alert...")
        # Add your code to send an email or other alert here
    else:
        print(f"Disk usage for {target_path} is {disk_usage}%.")
