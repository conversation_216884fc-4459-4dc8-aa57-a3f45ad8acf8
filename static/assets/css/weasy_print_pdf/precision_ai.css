

@font-face {
    font-family: 'Nanum Gothic';
    font-weight: 400;
    src: url('./nanum/NanumGothic-Regular.ttf');
}

@font-face {
    font-family: 'Nanum Gothic';
    font-weight: 700;
    src: url('./nanum/NanumGothic-Bold.ttf');
}

@font-face {
    font-family: 'Nanum Gothic';
    font-weight: 800;
    src: url('./nanum/NanumGothic-ExtraBold.ttf');
}



/* A4 page Print Area excluding header & Footer whole width 210 , height 295 margin bottom is page 1/2*/
@page {
    size: A4;
    margin-top: 4mm;
    margin-bottom: 8mm;
    margin-left: 10mm;
    margin-right: 10mm;
    font-family:  'Nanum Gothic', sans-serif;

    @bottom-left {
        content: counter(page) "/" counter(pages);
        text-align: center;
        font-size: 10px;
        font-weight: normal;
        margin-bottom: 6mm;
        margin-left: 9.1cm;
        color: #cb2f89;
    }
}


* {
    box-sizing: border-box;
    font-family:  'Nanum Gothic', sans-serif;

}

.all {
    padding: 0px;
    height: 267mm;
    /* border: 1px solid green ; */
   

}

/* cb2f89 original color purple  */

/* CSS For Header Parts    */

    /* 외부 보라색 라운드 테두리 스타일 */
    .header_footer {
        width: 100%;
        vertical-align: middle;
        text-align: center;
        align-items: center;       /* align-items: center; */
        margin-bottom: 0px;
        /* border: 1px solid green; */
}
    header_footer img {

        width: 100%; /* Sets both header and footer images to full width */
        margin-bottom: 5px;
        /* object-fit: cover;  */
    }
    
    .header img {
        height: 28mm; /* Matches header height */
        width:192mm ;
        margin-left: -10px;
    }

    .footer img {
        height: 14mm; /* Matches footer height */
        width:190mm ;
        margin-bottom: 0px;
    }
   


    /* 강조된 보라색 텍스트 */
    .highlight {
        color: #cb2f89;
        font-weight: bold;
    }


.fail_head {
    background-color: yellow;
    color: black;
    height:25mm;
    line-height: 25mm;
}

.fail_head .fail_h1 {
    font-size: 38px;
    color: black;
    margin-left:5mm;
    margin-top:10mm;
    line-height: 1;
}

.fail_head .fail_h2 {
    font-size: 17px;
    margin-left: 74mm;
    color: black;
    letter-spacing: 0px;
    line-height:1;
}

.header .redraw_h1 {
    font-weight: 800;
    position: absolute;
    top: 12px;
    font-size: 35px;
    margin-left: 5mm;
    letter-spacing: 0px;
}

.header .redraw_h2 {
    font-weight: 800;
    position: absolute;
    top: 45px;
    font-size: 16px;
    margin-left: 77mm;
    letter-spacing: 0px;
}



/* Result Body Parts  */



/* TGC PID */
.tgcpid {
    float: right;
    width: 50%;
    margin-bottom: 5px;
}

/* border bottom color  */
.b_b_c {
    border-bottom: 2px solid #cb2f89 ;
}

/* border top color  */
.b_t_c {
    border-top: 2px solid #cb2f89 ;
}







table,
th,
td {
    border-bottom: 2px solid #BFBFBF;
    border-collapse: collapse;
    text-align: center;
    
}

table {
    margin-top: 5px;
    margin-bottom: 10px;
    table-layout: fixed;
    width: 100%;
    
}

.table_heading {
    margin-bottom: 0px;
    border-bottom: none;
   
}
.table_heading td {
    border: none;
    text-align: left;
}

.gray_bottom_border {
    border-bottom: 2px solid #BFBFBF;
}

.bg_green {
    /*background-color: hsl(327, 76%, 46%);*/
    background-color:#cb2f89;
    color: white;
    /*  Previously White  */
    padding-left: 0px;

    
}


th {
    border-bottom: 2px solid #cb2f89;
    font-size:12px;
    line-height: 1.4;
    
/* override th instead greay  min-height: 10mm; */

}

.fail_th {
    border-bottom: 2px solid #cb2f89;
    border-top: 2px solid #cb2f89;
    font-size:12px;
    line-height: 1.3;
    background-color: #cb2f89;
    color: white;
    padding-left: 0px;
}

td {
    line-height: 1.35;
    padding-left: 10px;
    font-size:12px;
    line-height: 1.4;
    min-height: 6mm;    
}

.fail td {
    line-height: 1.1;
    min-height: 5mm;
    font-size:12px;
}

.bg_gray {
    background-color: #F2F2F2;
    border-top: 2px solid #BFBFBF;
    border-bottom: 2px solid #BFBFBF;
    padding-left: 0px;

}

.left_align {
    text-align: left;
    padding-left: 10px;

}


/* CNV PPV is line Breaking due to long text size so, giving a small font */
.ppv_th {
    text-align: center;
    padding-left:5px;
    font-size: 12px;

}
.cnv_ppv {
    text-align: left;
    padding-left: 5px;
    font-size: 11px;
    /* letter-spacing: -1px; */

}

.main_result_low {  
    font-size: 36px;
    color: blue;
    font-weight: 800;
    letter-spacing: -2px;
}

.main_result_high {
    font-size: 36px;
    color: red;
    font-weight: 800;
    letter-spacing: -2px;
}

.main_result_borderline {
    font-size: 36px;
    color: #C65911;
    font-weight: 800;
    letter-spacing: -2px;
}

.main_result_fail {
        /* color: red !important; */
        font-weight: bold;
        font-size: 10px;
        text-align:center;
}


.sign td {
    border: 0px;
    text-align: left;
    font-size : 11px;
    margin-bottom: 0px;
}

.sign table {
    border: 0px;
}

.sign img {
    width: 15mm;
    height: 10mm;
    margin-left: 30px;
    vertical-align: middle;
}

span {
    font-weight: 500;
    text-align: Center;
}

.r_border {
    border-right: 2px solid #BFBFBF;
}

p {
    line-height: 1.4;
    margin-top: 7px;
    margin-bottom: 7px;
    font-size: 12px;

}

.small_gap {
    margin: 10px;
}

.small_gap_height {
    height: 3mm;
   

}

.result_border_line {
        
      color: #C65911  !important;
      font-weight: 800;
}

.result_high_risk {
        color: red !important;
        font-weight: 800;
        

}

.result_explain_fail {
    font-size:10px;
    line-height:11px;
} 



/* Footer styling */
    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 14mm ; /* Adjusts height based on image size */
        text-align: center; /* Centers the image horizontally */
        
    }

    .footer img {
        max-width: 100%; /* Ensures the image scales responsively */
        height: auto; /* Ensures the image scales responsively */
        object-fit: fill; /* Ensures the image completely fills the specified width and height */


        
    }



.footer p {
    line-height: 1.3;
    color:rgb(65, 60, 60) ;
    font-weight: 400;
    font-size: 9px;
     margin-bottom: 0px;
    
}